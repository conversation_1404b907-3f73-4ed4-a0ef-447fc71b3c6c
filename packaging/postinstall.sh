#!/bin/bash

# Link binary
ln -sf /opt/ca-racing/CA-Racing /usr/bin/ca-racing

# Create desktop entry
cat > /usr/share/applications/ca-racing.desktop <<EOF
[Desktop Entry]
Type=Application
Name=CA-Racing
GenericName=Racing Game
Comment=A retro-style 2D top-down racing game
Exec=/opt/ca-racing/CA-Racing
Icon=/opt/ca-racing/assets/images/icon.png
Categories=Game;ArcadeGame;
Terminal=false
EOF

# Update icon cache
gtk-update-icon-cache -f -t /usr/share/icons/hicolor || true
update-desktop-database || true
