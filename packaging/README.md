# Packaging Guide 📦

This directory contains the necessary configuration files and scripts to build **CA-Racing** packages for various Linux distributions.

## 📂 Directory Structure

*   `aur/`: **Arch Linux** (PKGBUILD, .SRCINFO)
*   `deb/`: **Debian/Ubuntu** (control, postinst, postrm)
*   `rpm/`: **Fedora/RHEL** (.spec file)

## 🛠️ Build Instructions

### Arch Linux (AUR)

Packages are built using `makepkg`. The `PKGBUILD` file fetches the release artifact from GitHub.

```bash
cd aur
updpkgsums                  # Update checksums
makepkg --printsrcinfo > .SRCINFO
makepkg -si                 # Build and install
```

### Debian / Ubuntu (.deb)

To build a `.deb` package manually:

```bash
cd deb
dpkg-deb --build ca-racing-0.1.0-alpha
```

### Fedora / RHEL (.rpm)

Requires `rpmdevtools`.

```bash
# Set up RPM tree
rpmdev-setuptree
cp rpm/ca-racing.spec ~/rpmbuild/SPECS/

# Build
cd ~/rpmbuild/SPECS
rpmbuild -ba ca-racing.spec
```

## 🔄 Publishing

For detailed instructions on publishing to AUR, Launchpad (PPA), and Fedora COPR, please refer to the main [RELEASE.md](../RELEASE.md) guide and the `PUBLISHING.md` (if available) for repository-specific credentials and workflows.

## 📝 Maintainer Notes

*   **Version Consistency**: Ensure the version number in `PKGBUILD`, `control`, and `.spec` matches the project version.
*   **Checksums**: Always update file checksums after creating a new release artifact.
