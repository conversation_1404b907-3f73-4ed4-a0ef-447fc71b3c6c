#!/bin/bash
set -e

# Version configuration
VERSION="${1:-0.1.0-alpha}"
# Sanitize version for RPM (replace - with _)
RPM_VERSION="${VERSION//-/_}"
RPM_ARCH="x86_64"

# Paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"
DIST_DIR="$PROJECT_ROOT/dist"
RPM_BUILD_DIR="$PROJECT_ROOT/build/rpm"
ARTIFACTS_DIR="$PROJECT_ROOT/artifacts"

echo "🎩 Building RPM package for version $VERSION..."

# Ensure artifacts directory exists
mkdir -p "$ARTIFACTS_DIR"

# Pre-requisite: Ensure the game is built
if [ ! -f "$DIST_DIR/CA-Racing" ]; then
    echo "❌ Critical Error: Build artifact not found at $DIST_DIR/CA-Racing"
    exit 1
fi

# Clean up previous build directories
rm -rf "$RPM_BUILD_DIR"
mkdir -p "$RPM_BUILD_DIR"/{BUILD,RPMS,SOURCES,SPECS,SRPMS}

# Create a tarball from the binary
TEMP_BUILD_DIR="$DIST_DIR/ca-racing-rpm-build"
mkdir -p "$TEMP_BUILD_DIR/CA-Racing"
cp "$DIST_DIR/CA-Racing" "$TEMP_BUILD_DIR/CA-Racing/CA-Racing"
(cd "$TEMP_BUILD_DIR" && tar -czvf "$RPM_BUILD_DIR/SOURCES/ca-racing.tar.gz" "CA-Racing")
rm -rf "$TEMP_BUILD_DIR"

# Copy Spec
cp "$PROJECT_ROOT/packaging/rpm/ca-racing.spec" "$RPM_BUILD_DIR/SPECS/ca-racing.spec"

# Modify Spec file for CI build
sed -i "s|Source0:.*|Source0: ca-racing.tar.gz|" "$RPM_BUILD_DIR/SPECS/ca-racing.spec"

# Build RPM
rpmbuild --define "_topdir $RPM_BUILD_DIR" \
         --define "_version $RPM_VERSION" \
         -bb "$RPM_BUILD_DIR/SPECS/ca-racing.spec"

# Move RPM to artifacts
find "$RPM_BUILD_DIR/RPMS" -name "*.rpm" -exec cp {} "$ARTIFACTS_DIR/" \;
echo "✅ RPMs copied to $ARTIFACTS_DIR"