{"tracks": {"track_0": {"name_key": "track_0_name", "map_file": "map_0.tmx", "description_key": "track_0_desc", "difficulty": 1, "laps": 3, "start_pos": [640, 400], "start_angle": 0, "race_type": "circuit", "nav_file": "map_0_nav.json"}, "track_1": {"name_key": "track_1_name", "map_file": "map_1.tmx", "description_key": "track_1_desc", "difficulty": 2, "laps": 3, "start_pos": [640, 400], "start_angle": 0, "race_type": "circuit", "locked": true, "incomplete": true, "nav_file": "map_track_1_nav.json"}}, "cars": {"car_0": {"name_key": "car_0_name", "size_class": "XS", "weight_kg": 900, "price": 0}, "car_1": {"name_key": "car_1_name", "size_class": "XS", "weight_kg": 920, "price": 500}, "car_2": {"name_key": "car_2_name", "size_class": "XS", "weight_kg": 880, "price": 800}, "car_3": {"name_key": "car_3_name", "size_class": "XS", "weight_kg": 910, "price": 1200}, "car_4": {"name_key": "car_4_name", "size_class": "XS", "weight_kg": 895, "price": 1500}, "car_5": {"name_key": "car_5_name", "size_class": "XS", "weight_kg": 875, "price": 2000}, "car_6": {"name_key": "car_6_name", "size_class": "S", "weight_kg": 1100, "price": 3000}, "car_7": {"name_key": "car_7_name", "size_class": "S", "weight_kg": 1120, "price": 3500}, "car_8": {"name_key": "car_8_name", "size_class": "S", "weight_kg": 1080, "price": 4000}, "car_9": {"name_key": "car_9_name", "size_class": "S", "weight_kg": 1150, "price": 4500}, "car_10": {"name_key": "car_10_name", "size_class": "S", "weight_kg": 1090, "price": 5000}, "car_11": {"name_key": "car_11_name", "size_class": "S", "weight_kg": 1110, "price": 5500}, "car_12": {"name_key": "car_12_name", "size_class": "M", "weight_kg": 1350, "price": 7000}, "car_13": {"name_key": "car_13_name", "size_class": "M", "weight_kg": 1400, "price": 8000}, "car_14": {"name_key": "car_14_name", "size_class": "M", "weight_kg": 1380, "price": 9000}, "car_15": {"name_key": "car_15_name", "size_class": "M", "weight_kg": 1420, "price": 10000}, "car_16": {"name_key": "car_16_name", "size_class": "M", "weight_kg": 1360, "price": 11000}, "car_17": {"name_key": "car_17_name", "size_class": "M", "weight_kg": 1390, "price": 12000}}, "parts": {"engines": {"xs_0_engine": {"name_key": "xs_0_engine_name", "size_class": "XS", "horsepower": 120, "weight_kg": 80, "price": 0}, "xs_1_engine": {"name_key": "xs_1_engine_name", "size_class": "XS", "horsepower": 140, "weight_kg": 85, "price": 800}, "xs_2_engine": {"name_key": "xs_2_engine_name", "size_class": "XS", "horsepower": 160, "weight_kg": 90, "price": 1500}, "s_0_engine": {"name_key": "s_0_engine_name", "size_class": "S", "horsepower": 180, "weight_kg": 110, "price": 2500}, "s_1_engine": {"name_key": "s_1_engine_name", "size_class": "S", "horsepower": 200, "weight_kg": 115, "price": 3500}, "s_2_engine": {"name_key": "s_2_engine_name", "size_class": "S", "horsepower": 220, "weight_kg": 120, "price": 4500}, "m_0_engine": {"name_key": "m_0_engine_name", "size_class": "M", "horsepower": 250, "weight_kg": 140, "price": 6000}, "m_1_engine": {"name_key": "m_1_engine_name", "size_class": "M", "horsepower": 280, "weight_kg": 145, "price": 8000}, "m_2_engine": {"name_key": "m_2_engine_name", "size_class": "M", "horsepower": 310, "weight_kg": 150, "price": 10000}}, "breaks": {"xs_0_breaks": {"name_key": "xs_0_breaks_name", "size_class": "XS", "braking_power": 1.0, "price": 0}, "xs_1_breaks": {"name_key": "xs_1_breaks_name", "size_class": "XS", "braking_power": 1.2, "price": 500}, "xs_2_breaks": {"name_key": "xs_2_breaks_name", "size_class": "XS", "braking_power": 1.4, "price": 1000}, "s_0_breaks": {"name_key": "s_0_breaks_name", "size_class": "S", "braking_power": 1.1, "price": 700}, "s_1_breaks": {"name_key": "s_1_breaks_name", "size_class": "S", "braking_power": 1.3, "price": 1200}, "s_2_breaks": {"name_key": "s_2_breaks_name", "size_class": "S", "braking_power": 1.5, "price": 1800}, "m_0_breaks": {"name_key": "m_0_breaks_name", "size_class": "M", "braking_power": 1.2, "price": 1500}, "m_1_breaks": {"name_key": "m_1_breaks_name", "size_class": "M", "braking_power": 1.4, "price": 2200}, "m_2_breaks": {"name_key": "m_2_breaks_name", "size_class": "M", "braking_power": 1.6, "price": 3000}}, "boosts": {"xs_0_boost": {"name_key": "xs_0_boost_name", "size_class": "XS", "boost_power": 50, "price": 0}, "xs_1_boost": {"name_key": "xs_1_boost_name", "size_class": "XS", "boost_power": 70, "price": 600}, "xs_2_boost": {"name_key": "xs_2_boost_name", "size_class": "XS", "boost_power": 90, "price": 1200}, "s_0_boost": {"name_key": "s_0_boost_name", "size_class": "S", "boost_power": 80, "price": 900}, "s_1_boost": {"name_key": "s_1_boost_name", "size_class": "S", "boost_power": 100, "price": 1500}, "s_2_boost": {"name_key": "s_2_boost_name", "size_class": "S", "boost_power": 120, "price": 2100}, "m_0_boost": {"name_key": "m_0_boost_name", "size_class": "M", "boost_power": 110, "price": 1800}, "m_1_boost": {"name_key": "m_1_boost_name", "size_class": "M", "boost_power": 130, "price": 2500}, "m_2_boost": {"name_key": "m_2_boost_name", "size_class": "M", "boost_power": 150, "price": 3500}}}, "opponents": {"opponent_easy": {"name_key": "opponent_easy_name", "car_model": "car_1", "difficulty": "BEGINNER"}, "opponent_medium": {"name_key": "opponent_medium_name", "car_model": "car_6", "difficulty": "AMATEUR"}, "opponent_hard": {"name_key": "opponent_hard_name", "car_model": "car_12", "difficulty": "PRO"}, "opponent_expert": {"name_key": "opponent_expert_name", "car_model": "car_17", "difficulty": "ELITE"}}}