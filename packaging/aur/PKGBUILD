# Maintainer: <PERSON><PERSON><PERSON>2713 <<EMAIL>>
# <AUTHOR> <EMAIL>

pkgname=ca-racing
_app_version="$(python get_app_version.py | tail -n 1)"
# Sanitize version for Arch (replace - with _)
pkgver="${_app_version//-/_}"
pkgrel=1
pkgdesc="Top-down 2D racing game written in Python"
arch=('any')
url="https://github.com/piotrek1372/ca-racing"
license=('MIT')

depends=('python-pygame' 'python-numpy' 'python-pillow' 'python-pytmx')
makedepends=('python' 'python-pip' 'python-setuptools')

# Source code from GitHub release
_urlver="${pkgver/_/-}"
source=("$pkgname-$_urlver.tar.gz::https://github.com/piotrek1372/$pkgname/archive/refs/tags/v$_urlver.tar.gz"
        "ca-racing.desktop"
        "ca-racing.sh")
sha256sums=('207673becefcaff48e12b13e607bc38678aa0529ab92b4a057441c498ac0e895'
            '1daadbed224442b479eb435bb7927a3c16353acbf25eff04d7348202b07cf3f3'
            'ded2d55a2da0ccd2a3273130d3ed41c2e6d6e5b6f0e9c0d7f8cbbbf5365316d5')

build() {
    cd "$srcdir/CA-Racing-$_urlver"
    # No build steps needed for a Python source package
    echo "Skipping build process..."
}

package() {
    # Install the application source code
    install -d "$pkgdir/opt/$pkgname"
    cp -r "$srcdir/CA-Racing-$_urlver/"* "$pkgdir/opt/$pkgname/"

    # Install Python dependencies
    pip install -r "$pkgdir/opt/$pkgname/requirements.txt" --target "$pkgdir/opt/$pkgname/vendor" --no-user

    # Install the wrapper script
    install -Dm755 "$srcdir/ca-racing.sh" "$pkgdir/usr/bin/$pkgname"

    # Install icon and desktop file
    install -Dm644 "$srcdir/CA-Racing-$_urlver/assets/images/icon.png" "$pkgdir/usr/share/pixmaps/$pkgname.png"
    install -Dm644 "$srcdir/ca-racing.desktop" "$pkgdir/usr/share/applications/$pkgname.desktop"
}
