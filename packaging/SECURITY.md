# Bezpieczeństwo i Zarządzanie Sekretami w CI/CD

Ten dokument opisuje, jak zarz<PERSON><PERSON><PERSON> kluczami i sekretami w procesie wydawniczym.

## 1. GitHub Secrets

Aby proces CI/CD działał be<PERSON>, nie przechowuj haseł ani kluczy w kodzie. Użyj **GitHub Secrets** (Settings -> Secrets and variables -> Actions).

Wymagane sekrety (jeśli chcesz w pełni zautomatyzować publikację):

*   `AUR_SSH_KEY`: Klucz prywatny SSH do publikacji w AUR (jeśli automatyzujesz ten krok).
*   `GPG_PRIVATE_KEY`: Klucz prywatny GPG do podpisywania pakietów (opcjonalne, ale zalecane).
*   `GPG_PASSPHRASE`: Hasło do klucza GPG.

## 2. Podpisywanie Pakietów (GPG)

Podpisywanie pakietów `.deb` i `.rpm` zwiększa zaufanie użytkowników.

### Generowanie klucza GPG
Na lokalnej maszynie:
```bash
gpg --full-generate-key
```
Wybierz RSA (4096 bitów).

### Eksport klucza
```bash
# Znajdź ID klucza
gpg --list-secret-keys --keyid-format LONG

# Eksportuj klucz prywatny (dodaj to do GitHub Secrets jako GPG_PRIVATE_KEY)
gpg --armor --export-secret-keys <KEY_ID>
```

### Konfiguracja w GitHub Actions
Aby użyć GPG w workflow, dodaj krok importu przed budowaniem pakietów:

```yaml
- name: Import GPG Key
  uses: crazy-max/ghaction-import-gpg@v6
  with:
    gpg_private_key: ${{ secrets.GPG_PRIVATE_KEY }}
    passphrase: ${{ secrets.GPG_PASSPHRASE }}
```

Następnie dodaj flagi podpisywania do polecenia `fpm`:
```bash
fpm -s dir -t deb ... --gpg <KEY_ID> ...
```

## 3. Tokeny API

Workflow używa `GITHUB_TOKEN` (automatycznie generowany) do tworzenia Release. Nie musisz nic konfigurować, chyba że chcesz publikować w zewnętrznych repozytoriach (np. PyPI), wtedy dodaj `PYPI_API_TOKEN`.
