# CA-Racing Multiplayer Lobby System
## Complete Implementation Guide

### Overview

The CA-Racing multiplayer lobby system provides a comprehensive room-based multiplayer experience with real-time updates, pagination, and player management. This guide covers the complete architecture and usage.

---

## Features

### 🎮 Core Features

1. **Room Management**
   - Display up to 20 rooms simultaneously
   - Pagination for >20 rooms
   - Real-time room status updates
   - Room states: WAITING, RACING, FINISHED

2. **Player Actions**
   - Join available rooms
   - Spectate full or racing rooms
   - Create custom rooms
   - Leave rooms
   - Ready status management

3. **Room Display**
   - Shows ALL rooms (empty, partial, full)
   - Color-coded by status:
     - 🟢 Green: Empty rooms
     - 🟡 Yellow: Waiting for players
     - 🔴 Red: Full rooms
     - 🟠 Orange: Racing in progress
   
4. **Real-time Updates**
   - Automatic room list refresh
   - Player join/leave notifications
   - Room state changes
   - Chat messages

5. **Pagination System**
   - Maximum 20 rooms displayed per page
   - Next/Previous page navigation
   - Page indicator (e.g., "Page 1/5")
   - Most recent rooms shown first

---

## Architecture

### Server Side (`server.py`)

#### RoomManager Class

```python
class RoomManager:
    MAX_DISPLAYED_ROOMS = 20  # Maximum rooms per page
    
    def create_room(name, host_name, track_name) -> Room
    def get_room_list(limit, page) -> List[Dict]
    def join_room(player, room_id) -> bool
    def leave_room(player_id) -> None
    def get_room_count() -> int
```

**Key Methods:**

1. **`get_room_list(limit=20, page=0)`**
   - Returns paginated room list
   - Most recent rooms first
   - Includes all room states (empty to full)

2. **`create_room(name, host_name, track_name)`**
   - Creates new room
   - Adds to creation order tracking
   - Returns Room object

3. **Room Data Structure:**
```python
{
    "room_id": int,
    "name": str,
    "players_count": int,
    "max_players": int,
    "host_name": str,
    "track_name": str,
    "state": str,  # "waiting", "racing", "finished"
    "is_full": bool,
    "is_empty": bool
}
```

#### Message Handlers

The server handles these message types:

1. **`request_room_list`**
   - Request: `{ "type": "request_room_list", "page": 0 }`
   - Response: Room list with pagination info

2. **`create_room`**
   - Request: `{ "type": "create_room", "room_name": "...", "host_name": "...", "track_name": "..." }`
   - Auto-joins creator to room

3. **`join_room`**
   - Request: `{ "type": "join_room", "room_id": 123 }`
   - Validates room availability
   - Broadcasts player join to room

4. **`leave_room`**
   - Request: `{ "type": "leave_room" }`
   - Notifies other players
   - Deletes empty rooms

5. **`spectate_room`**
   - Request: `{ "type": "spectate_room", "room_id": 123 }`
   - View-only mode for full/racing rooms

6. **`chat_message`**
   - Request: `{ "type": "chat_message", "message": "...", "in_room": bool }`
   - Broadcasts to lobby or room

---

### Client Side (`src/ui/multiplayer_lobby.py`)

#### MultiplayerLobby Class

```python
class MultiplayerLobby:
    MAX_DISPLAYED_ROOMS = 20
    
    def update_rooms(rooms_data: List[Dict])
    def request_room_list_threaded(page: int)
    def draw()
    def handle_event(event)
```

**UI Components:**

1. **Room List Panel (Left Side)**
   - Scrollable room list
   - 6 rooms visible at once
   - Color-coded backgrounds
   - Click to join/spectate

2. **Chat Panel (Right Side)**
   - Last 20 messages
   - Lobby chat / Room chat
   - Message wrapping

3. **Control Buttons**
   - Create Room (C key or button)
   - Refresh (updates room list)
   - Previous Page / Next Page
   - Scroll indicator

4. **Create Room Dialog**
   - Modal overlay
   - Room name input
   - Track selection
   - Enter to create, Esc to cancel

**Keyboard Controls:**

- `C` - Open create room dialog
- `ENTER` - Activate chat input / Send message
- `ESC` - Leave room / Cancel dialog
- `SPACE` - Toggle ready status (in room)
- Mouse wheel - Scroll room list

---

## Protocol (`src/network/protocol.py`)

### Packet Types

```python
class PacketType(IntEnum):
    ROOM_LIST = 13
    CREATE_ROOM = 14
    JOIN_ROOM = 15
    LEAVE_ROOM = 16
    CHAT_MESSAGE = 17
    SPECTATE_ROOM = 18
    ROOM_UPDATED = 19
```

### Factory Methods

```python
Packets.room_list(rooms, total_rooms, page, server_time)
Packets.create_room(room_name, host_name, track_name, client_time)
Packets.join_room(room_id, client_time)
Packets.leave_room(client_time)
Packets.spectate_room(room_id, client_time)
Packets.room_updated(room_id, players_count, state, server_time)
Packets.chat_message(sender, message, in_room, server_time)
```

---

## Real-Time Updates

### Server Broadcast System

The server automatically broadcasts room list updates when:

1. **Player Joins Room**
   - All lobby players receive updated room list
   - Room players receive "player_joined" message

2. **Player Leaves Room**
   - All lobby players receive updated room list
   - Room players receive "player_left" message

3. **Room Created**
   - All lobby players receive updated room list
   - Creator auto-joins room

4. **Room Deleted**
   - Occurs when last player leaves
   - All lobby players receive updated room list

### Client Update Handling

```python
def setup_lobby_callbacks(session, lobby):
    def on_room_list(message):
        rooms_data = message.get('rooms', [])
        total_rooms = message.get('total_rooms', 0)
        lobby.update_rooms(rooms_data)
        lobby.set_total_rooms(total_rooms)
```

---

## Usage Examples

### Server Startup

```bash
python server.py
```

Server will:
- Listen on port 443 (configurable)
- Start UDP broadcast for LAN discovery
- Accept client connections
- Manage room lifecycle

### Client Connection

```python
from src.game.multiplayer_session import MultiplayerSession
from src.ui.multiplayer_lobby import MultiplayerLobby, setup_lobby_callbacks

# Create session
session = MultiplayerSession(app, slot_id, server_ip, server_port)

# Start async connection
await session.start()

# Create lobby
lobby = MultiplayerLobby(screen, session)

# Setup callbacks
setup_lobby_callbacks(session, lobby)

# Request initial room list
lobby.request_room_list_threaded(page=0)

# Game loop
while running:
    for event in pygame.event.get():
        lobby.handle_event(event)
    
    lobby.update(dt)
    lobby.draw()
```

---

## Testing

### Test Scenarios

1. **Empty Lobby**
   - Start server
   - Connect client
   - Should show "No rooms available"
   - Press C to create room

2. **Room Creation**
   - Press C
   - Enter room name
   - Press Enter
   - Should auto-join created room

3. **Multiple Rooms**
   - Create 25 rooms
   - Should paginate (2 pages: 20 + 5)
   - Test next/previous buttons

4. **Join Room**
   - Create room from client A
   - Connect client B
   - Client B clicks room
   - Both should see each other

5. **Full Room**
   - Fill room with 2 players
   - Third player should see "FULL" status
   - Click right side to spectate

6. **Real-time Updates**
   - Open 2 clients
   - Create room on client A
   - Client B should see new room immediately
   - Join from client B
   - Both see player counts update

7. **Chat System**
   - Press Enter to activate chat
   - Type message
   - Press Enter to send
   - Both lobby and room chat work

8. **Leave Room**
   - Join room
   - Press Esc
   - Return to lobby
   - Other player sees "player_left"

---

## Performance Considerations

### Server

- **Room Limit**: No hard limit, but only 20 displayed per page
- **Tick Rate**: 60 Hz for game simulation
- **Heartbeat**: 30s timeout, 5s check interval
- **Broadcast**: Async gather for parallel sends

### Client

- **UI Updates**: 60 FPS rendering
- **Network**: Async background thread
- **Memory**: Room list capped at 20 items
- **Chat**: Limited to 50 messages in memory

### Bandwidth

```python
# Typical bandwidth per connection:
- Room List: ~2-5 KB (compressed if >5 rooms)
- Join/Leave: ~100 bytes
- Chat Message: ~50-200 bytes
- Heartbeat: ~50 bytes every 30s
```

---

## Troubleshooting

### Issue: Rooms not appearing

**Solution:**
```python
# Check if room list request is sent
lobby.request_room_list_threaded(0)

# Verify server response
# Check server logs for: "Requested room list from server"
```

### Issue: Can't join room

**Possible causes:**
1. Room is full (2/2 players)
2. Room is already racing
3. Network disconnection

**Check:**
```python
if not session.is_connected():
    # Reconnect
    await session.start()
```

### Issue: Chat not working

**Check:**
```python
# Ensure callbacks are setup
setup_lobby_callbacks(session, lobby)

# Verify message handler
logger.info(f"Chat message: {message}")
```

### Issue: Pagination not working

**Check:**
```python
# Verify total_rooms is set
lobby.set_total_rooms(total)

# Check max_pages calculation
max_pages = (total_rooms + 20 - 1) // 20
```

---

## Advanced Configuration

### Changing Room Limit

**Server (`server.py`):**
```python
class RoomManager:
    MAX_DISPLAYED_ROOMS = 30  # Change from 20 to 30
```

**Client (`multiplayer_lobby.py`):**
```python
class MultiplayerLobby:
    MAX_DISPLAYED_ROOMS = 30  # Match server value
```

### Changing Room Capacity

```python
class Room:
    MAX_PLAYERS = 4  # Change from 2 to 4 players
```

### Custom Room Properties

```python
# Add to Room class
self.track_difficulty = track_difficulty
self.custom_rules = custom_rules

# Include in get_room_list()
room_list.append({
    # ... existing fields ...
    "track_difficulty": room.track_difficulty,
    "custom_rules": room.custom_rules
})
```

---

## Security Considerations

1. **Room Name Validation**
   - Limit length to 30 characters
   - Sanitize special characters
   - Prevent SQL injection (if using DB)

2. **Rate Limiting**
   - Limit room creation: 1 per 5 seconds per player
   - Limit chat messages: 10 per minute per player
   - Limit room list requests: 1 per second

3. **Authentication**
   - Consider adding player authentication
   - Validate player_id on server
   - Prevent impersonation

---

## Future Enhancements

1. **Search/Filter**
   - Filter by room name
   - Filter by track
   - Filter by player count

2. **Sorting**
   - Sort by players (most/least)
   - Sort by creation time
   - Sort by room name

3. **Room Settings**
   - Private rooms (password)
   - Max players per room
   - Custom game modes
   - Time limits

4. **Statistics**
   - Total online players
   - Active races
   - Server uptime
   - Average ping

5. **Matchmaking**
   - Auto-match players by skill
   - Quick join (random room)
   - Ranked matches

---

## API Reference

### Server Methods

```python
class GameServer:
    async def send_room_list(player, page=0)
    async def broadcast_room_list()
    async def broadcast_to_lobby(message, exclude=None)
    async def broadcast_to_room(room, message, exclude=None)
```

### Client Methods

```python
class MultiplayerLobby:
    def update_rooms(rooms_data)
    def set_total_rooms(total)
    def add_chat_message(message)
    def request_room_list_threaded(page)
    async def _create_room(room_name, track_name)
    async def _join_room(room_id)
    async def _spectate_room(room_id)
    async def _leave_room()
    async def _send_chat_message(message)
```

---

## Conclusion

The CA-Racing multiplayer lobby system provides a robust, scalable solution for managing game rooms with real-time updates and comprehensive player management. The system supports:

✅ Up to 20 rooms displayed at once  
✅ Unlimited total rooms with pagination  
✅ Real-time player join/leave updates  
✅ Empty, partial, and full room display  
✅ Join and spectate functionality  
✅ Room creation with custom settings  
✅ Chat system (lobby and room)  
✅ Responsive UI with keyboard/mouse controls  

For questions or issues, refer to the troubleshooting section or check the server/client logs for detailed debug information.
