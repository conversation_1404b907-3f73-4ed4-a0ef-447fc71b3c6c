# ======================================
# CA-Racing Docker Ignore
# ======================================
# Files and directories to exclude from Docker build context

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
ENV/
env/
.venv

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~
.DS_Store

# Git
.git/
.gitignore
.gitattributes

# Documentation (not needed in container)
docs/
docs_ai/
*.md
!requirements.txt

# Test files
test_*.py
tests/
*_test.py

# Assets (game client assets not needed on server)
assets/
maps/*.png
maps/*.tmx
maps/*.tsx

# Packaging
packaging/
*.deb
*.rpm
*.spec
*.AppImage

# Build artifacts
ca-racing.spec
setup_env.sh

# Data files (game data, should be mounted or in separate volume)
data/game_data.json
data/settings.json
data/lang/

# UI files (not needed on server)
src/ui/

# Game client files (not needed on server)
src/game/camera.py
src/game/car_audio.py
src/game/particles.py
src/game/player.py
src/game/session.py
src/game/race.py
src/game/garage/
src/tools/

# Other
*.log
.env
.env.*
docker-compose.override.yml

# Keep only essential server files:
# - server.py (entry point)
# - src/network/* (networking code)
# - src/game/physics_sync.py (physics synchronization)
# - src/physics/* (physics engine)
# - requirements.txt (dependencies)
