#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Quick-Fix Script for TMX Tile Properties using direct XML manipulation.

This script iterates through a specified layer of a TMX file and adds a
custom property to each tile in the corresponding tileset if it's not already present.
It uses xml.etree.ElementTree for robust, low-level manipulation, bypassing
the limitations of game-focused libraries like pytmx for file editing.

Usage:
    python -m src.tools.quick_fix_map_properties --map_file maps/map_0.tmx
"""

import argparse
import sys
import logging
import xml.etree.ElementTree as ET
from typing import Set

# --- Logger Configuration ---
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    stream=sys.stdout,
)
logger = logging.getLogger("TmxPropertyFixerXML")

def add_property_to_tiles_on_layer_xml(
    tmx_path: str,
    layer_name: str,
    prop_name: str,
    prop_value: str
):
    """
    Adds a specified property to all unique tiles on a given layer by directly
    manipulating the TMX file's XML structure.

    This function is idempotent; it will not add a property if it already exists
    with the correct value.

    Args:
        tmx_path (str): Path to the input/output TMX file.
        layer_name (str): The name of the layer to scan for tiles.
        prop_name (str): The name of the property to add (e.g., 'type').
        prop_value (str): The value of the property to set (e.g., 'dashed_line').
    """
    try:
        tree = ET.parse(tmx_path)
        root = tree.getroot()
        logger.info(f"Successfully parsed XML from TMX map: {tmx_path}")
    except FileNotFoundError:
        logger.error(f"Error: TMX file not found at '{tmx_path}'")
        return
    except ET.ParseError as e:
        logger.error(f"Error: Failed to parse XML in '{tmx_path}': {e}")
        return

    # Find the target layer and extract unique GIDs
    target_layer = root.find(f".//layer[@name='{layer_name}']")
    if target_layer is None:
        logger.error(f"Error: Layer '{layer_name}' not found in the TMX file.")
        return

    data_element = target_layer.find('data')
    if data_element is None or data_element.text is None:
        logger.warning(f"Layer '{layer_name}' has no data element or is empty. No changes made.")
        return
    
    # Assuming CSV encoding for simplicity, as it's the most common human-readable format
    gids_on_layer: Set[int] = {int(gid) for gid in data_element.text.strip().split(',') if gid.strip() and int(gid) != 0}
    
    if not gids_on_layer:
        logger.warning(f"No non-zero tile GIDs found on layer '{layer_name}'. No changes made.")
        return

    logger.info(f"Found {len(gids_on_layer)} unique tile GIDs on layer '{layer_name}': {gids_on_layer}")

    # Find all tilesets and their first GID
    tilesets = root.findall('tileset')
    if not tilesets:
        logger.error("No tilesets found in the TMX file.")
        return

    changes_made = False
    for gid in gids_on_layer:
        # Determine which tileset this GID belongs to
        target_tileset = None
        firstgid = -1
        for ts in tilesets:
            current_firstgid = int(ts.get('firstgid', '1'))
            if gid >= current_firstgid:
                target_tileset = ts
                firstgid = current_firstgid
        
        if target_tileset is None:
            logger.warning(f"Could not find a corresponding tileset for GID '{gid}'. Skipping.")
            continue

        tile_id = str(gid - firstgid)
        tile_element = target_tileset.find(f"tile[@id='{tile_id}']")

        if tile_element is None:
            # Tile element does not exist, so we must create it
            tile_element = ET.SubElement(target_tileset, 'tile', {'id': tile_id})
            logger.info(f"Created new <tile id='{tile_id}'> element.")

        properties_element = tile_element.find('properties')
        if properties_element is None:
            # Properties container does not exist, create it
            properties_element = ET.SubElement(tile_element, 'properties')

        # Check if the specific property already exists
        prop_exists = False
        for prop in properties_element.findall('property'):
            if prop.get('name') == prop_name and prop.get('value') == prop_value:
                prop_exists = True
                break
        
        if not prop_exists:
            # Add the new property
            ET.SubElement(properties_element, 'property', {'name': prop_name, 'value': prop_value})
            logger.info(f"Added property '{prop_name}={prop_value}' to tile with GID '{gid}' (local id '{tile_id}').")
            changes_made = True

    if changes_made:
        # Write the changes back to the file
        tree.write(tmx_path, encoding='utf-8', xml_declaration=True)
        logger.info(f"Successfully saved changes to '{tmx_path}'.")
    else:
        logger.info("No changes were needed; all required properties already exist.")


def main():
    """Main execution function for the CLI tool."""
    parser = argparse.ArgumentParser(
        description="Batch-add a property to tiles on a layer in a TMX file using direct XML manipulation."
    )
    parser.add_argument(
        "--map_file",
        type=str,
        required=True,
        help="Path to the .tmx file to be modified."
    )
    parser.add_argument(
        "--layer_name",
        type=str,
        default="droga",
        help="Name of the layer to process."
    )
    parser.add_argument(
        "--property_name",
        type=str,
        default="type",
        help="The name of the property to add to the tiles."
    )
    parser.add_argument(
        "--property_value",
        type=str,
        default="dashed_line",
        help="The value to set for the specified property."
    )
    args = parser.parse_args()

    add_property_to_tiles_on_layer_xml(
        args.map_file,
        args.layer_name,
        args.property_name,
        args.property_value
    )

if __name__ == "__main__":
    main()
