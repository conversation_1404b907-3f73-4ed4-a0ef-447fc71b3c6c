name: Publish to AUR

on:
  release:
    types: [published]
jobs:
  aur-publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v4

      - name: Add AUR to known hosts
        run: |
          mkdir -p ~/.ssh
          ssh-keyscan -t ed25519 aur.archlinux.org >> ~/.ssh/known_hosts

      - name: Setup SSH Agent
        uses: webfactory/ssh-agent@v0.9.0
        with:
          ssh-private-key: ${{ secrets.AUR_SSH_PRIVATE_KEY }}

      - name: Update PKGBUILD version
        run: |
          VERSION="${{ github.ref_name }}"
          VERSION="${VERSION#v}"
          echo "Updating PKGBUILD version to $VERSION"
          sed -i "s|_app_version=.*|_app_version=\"$VERSION\"|" packaging/aur/PKGBUILD
          # Verify the change
          grep "_app_version=" packaging/aur/PKGBUILD

      - name: Publish to AUR
        uses: KSXGitHub/github-actions-deploy-aur@v2.7.2
        with:
          pkgname: ca-racing
          pkgbuild: ./packaging/aur/PKGBUILD
          assets: ./packaging/aur/ca-racing.desktop ./packaging/aur/ca-racing.sh
          commit_username: ${{ secrets.AUR_USERNAME }}
          commit_email: ${{ secrets.AUR_EMAIL }}
          ssh_private_key: ${{ secrets.AUR_SSH_PRIVATE_KEY }}
          commit_message: "Update to v${{ github.event.release.tag_name }}"
