import pygame as pg
from typing import Tuple

class ScreenFader:
    def __init__(self, screen_size: Tuple[int, int], fade_duration: float = 0.5):
        self.screen_size = screen_size
        self.fade_duration = fade_duration
        self.fade_surface = pg.Surface(self.screen_size, pg.SRCALPHA)
        self.alpha = 0
        self.state = 'IDLE' # IDLE, FADING_OUT, FADING_IN
        self.fade_timer = 0.0

    def start_fade_out(self):
        self.state = 'FADING_OUT'
        self.fade_timer = 0.0

    def start_fade_in(self):
        self.state = 'FADING_IN'
        self.fade_timer = self.fade_duration

    def update(self, dt: float):
        if self.state == 'FADING_OUT':
            self.fade_timer += dt
            if self.fade_timer >= self.fade_duration:
                self.fade_timer = self.fade_duration
                self.state = 'IDLE'
        elif self.state == 'FADING_IN':
            self.fade_timer -= dt
            if self.fade_timer <= 0:
                self.fade_timer = 0
                self.state = 'IDLE'
        
        self.alpha = (self.fade_timer / self.fade_duration) * 255
        self.fade_surface.fill((0, 0, 0, self.alpha))

    def draw(self, screen: pg.Surface):
        if self.alpha > 0:
            screen.blit(self.fade_surface, (0, 0))

    def is_fading(self) -> bool:
        return self.state != 'IDLE'

    def is_fully_faded(self) -> bool:
        return self.fade_timer == self.fade_duration and self.state != 'FADING_IN'
