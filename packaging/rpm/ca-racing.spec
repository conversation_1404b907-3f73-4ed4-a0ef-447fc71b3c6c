Name:       ca-racing
Version:    %{_version}
Release:    1%{?dist}
Summary:    Gra wyścigowa 2D typu top-down
License:    MIT
URL:        https://github.com/piotrek1372/ca-racing
Source0:    ca-racing.tar.gz
Packager:   Piotrek2713 <<EMAIL>>

%description
CA-Racing to dynamiczna gra wyścigowa napisana w Pythonie.

%prep
%setup -q -n CA-Racing

%build
# Budowanie odbywa się poza rpmbuild (PyInstaller)

%install
mkdir -p %{buildroot}/opt/%{name}
mkdir -p %{buildroot}/usr/bin
install -m 755 CA-Racing %{buildroot}/opt/%{name}/ca-racing
ln -s /opt/%{name}/ca-racing %{buildroot}/usr/bin/ca-racing

%files
/opt/%{name}/ca-racing
/usr/bin/ca-racing

%changelog
* Mon Jan 13 2026 Piotrek2713 <<EMAIL>> - 1.0.0-demo
- Demo release showcasing complete multiplayer system
- Added client-side prediction and server reconciliation
- Implemented room-lobby system with 60Hz server tick rate
- Added comprehensive multiplayer documentation suite
- Docker support for containerized server deployment
- New network protocol module with state synchronization
- Physics sync module with state buffering and interpolation

* Sat Jan 11 2026 Piotrek2713 <<EMAIL>> - 0.1.1-alpha
- Fixed critical audio channel conflicts causing white noise
- Added professional audio mastering system
- Improved audio stability and performance
- Added physics module with engine physics
- Enhanced documentation and test coverage

* Mon Jan 06 2026 Piotrek2713 <<EMAIL>> - 0.1.0-alpha
- Initial release
