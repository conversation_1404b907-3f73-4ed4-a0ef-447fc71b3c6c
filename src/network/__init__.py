"""
CA-Racing Network Module
========================
Advanced multiplayer networking with client-side prediction and server reconciliation.

Key Components:
- NetworkClient: Async client with prediction & reconciliation
- NetworkGameLoop: Helper for Pygame integration
- Protocol: Binary protocol with compression
- LagSimulator: Test lag compensation
- PacketBuilder: Efficient packet serialization
"""

from .client_handler import NetworkClient, NetworkGameLoop
from .protocol import (
    PacketType,
    PacketBuilder,
    Packets,
    LagSimulator,
    InputState,
    PlayerState,
    Transform,
    Velocity,
    estimate_bandwidth,
    get_protocol_stats
)

__all__ = [
    # Client
    'NetworkClient',
    'NetworkGameLoop',
    
    # Protocol
    'PacketType',
    'PacketBuilder',
    'Packets',
    'LagSimulator',
    
    # Data Structures
    'InputState',
    'PlayerState',
    'Transform',
    'Velocity',
    
    # Utilities
    'estimate_bandwidth',
    'get_protocol_stats',
]

__version__ = '2.0.0'
