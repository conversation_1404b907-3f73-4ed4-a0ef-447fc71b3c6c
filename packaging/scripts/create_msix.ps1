# packaging/scripts/create_msix.ps1
param (
    [string]$Version = "*******",
    [string]$CertificatePath,
    [string]$CertificatePassword
)

$ErrorActionPreference = "Stop"

Write-Host "Starting MSIX Packaging Process..."

# Helper function to find Windows SDK tools
function Get-WindowsSdkTool {
    param ([string]$ToolName)
    
    $KitsRoot = "C:\Program Files (x86)\Windows Kits\10\bin"
    if (Test-Path $KitsRoot) {
        # Find the latest version
        $LatestVersion = Get-ChildItem $KitsRoot | Where-Object { $_.PSIsContainer -and $_.Name -match "^\d+\.\d+\.\d+\.\d+$" } | Sort-Object Name -Descending | Select-Object -First 1
        if ($LatestVersion) {
            $ToolPath = Join-Path $LatestVersion.FullName "x64\$ToolName"
            if (Test-Path $ToolPath) {
                Write-Host "   - Found $ToolName at: $ToolPath"
                return $ToolPath
            }
        }
    }
    Write-Warning "   - Could not find $ToolName in Windows Kits directory. Falling back to PATH."
    return $ToolName
}

# Helper function to sanitize version for MSIX (Must be Quad format A.B.C.D)
function Get-SanitizedVersion {
    param ([string]$InputVersion)
    
    # Remove 'v' prefix
    if ($InputVersion.StartsWith("v")) { $InputVersion = $InputVersion.Substring(1) }
    
    # Remove suffix (everything after first -)
    $BaseVersion = $InputVersion.Split("-")[0]
    
    # Split into components
    $Parts = $BaseVersion.Split(".")
    
    # Ensure 4 parts
    while ($Parts.Count -lt 4) {
        $Parts += "0"
    }
    # Take only first 4 parts
    $Parts = $Parts[0..3]
    
    return [string]::Join(".", $Parts)
}

# Define paths
$ProjectRoot = Resolve-Path "$PSScriptRoot\..\.."
$DistDir = Join-Path $ProjectRoot "dist"
$LayoutDir = Join-Path $DistDir "msix_layout"
$AssetsDir = Join-Path $LayoutDir "Assets"
$ManifestSource = Join-Path $ProjectRoot "packaging\windows\AppxManifest.xml"
$ExeSource = Join-Path $DistDir "CA-Racing.exe"
$MsixOutput = Join-Path $DistDir "CA-Racing.msix"

# 1. Prepare Layout Directory
Write-Host "   - Preparing layout directory: $LayoutDir"
if (Test-Path $LayoutDir) { Remove-Item $LayoutDir -Recurse -Force }
New-Item -ItemType Directory -Path $LayoutDir | Out-Null
New-Item -ItemType Directory -Path $AssetsDir | Out-Null

# 2. Copy Executable and Dependencies
Write-Host "   - Copying binaries..."
if (-not (Test-Path $ExeSource)) {
    Write-Error "Executable not found at $ExeSource. Please run build_dist.sh first."
}
Copy-Item $ExeSource -Destination $LayoutDir
# Copy the internal folder if it exists (PyInstaller one-dir mode)
if (Test-Path "$DistDir\_internal") {
    Copy-Item "$DistDir\_internal" -Destination $LayoutDir -Recurse
}

# 3. Copy Manifest
Write-Host "   - Copying AppxManifest.xml..."
Copy-Item $ManifestSource -Destination "$LayoutDir\AppxManifest.xml"

# Update version in manifest
$SanitizedVersion = Get-SanitizedVersion $Version
Write-Host "   - Original Version: $Version"
Write-Host "   - MSIX Version:     $SanitizedVersion"

$ManifestContent = Get-Content "$LayoutDir\AppxManifest.xml"
$ManifestContent = $ManifestContent -replace 'Version="*******"', "Version=""$SanitizedVersion"""
Set-Content -Path "$LayoutDir\AppxManifest.xml" -Value $ManifestContent

# 4. Prepare Assets (Icons)
Write-Host "   - Preparing assets..."
$SourceIcon = Join-Path $ProjectRoot "assets\images\icon.png"
if (Test-Path $SourceIcon) {
    Copy-Item $SourceIcon -Destination "$AssetsDir\StoreLogo.png"
    Copy-Item $SourceIcon -Destination "$AssetsDir\Square150x150Logo.png"
    Copy-Item $SourceIcon -Destination "$AssetsDir\Square44x44Logo.png"
} else {
    Write-Warning "Source icon not found at $SourceIcon"
}

# 5. Create MSIX Package
Write-Host "   - Locating MakeAppx.exe..."
$MakeAppx = Get-WindowsSdkTool "makeappx.exe"

Write-Host "   - Running MakeAppx..."
& $MakeAppx pack /d $LayoutDir /p $MsixOutput /o
if ($LASTEXITCODE -ne 0) { Write-Error "MakeAppx failed!" }

# 6. Sign Package (if certificate provided)
if ($CertificatePath -and (Test-Path $CertificatePath)) {
    Write-Host "   - Locating SignTool.exe..."
    $SignTool = Get-WindowsSdkTool "signtool.exe"
    
    Write-Host "   - Signing MSIX package..."
    & $SignTool sign /f $CertificatePath /p $CertificatePassword /fd SHA256 /tr http://timestamp.digicert.com /td SHA256 $MsixOutput
    if ($LASTEXITCODE -ne 0) { Write-Error "SignTool failed!" }
    Write-Host "Package signed successfully."
} else {
    Write-Warning "Certificate not provided. MSIX package is unsigned."
}

Write-Host "MSIX created at: $MsixOutput"
