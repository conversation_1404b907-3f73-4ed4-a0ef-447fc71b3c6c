import pygame as pg
from src.constants import *
from src.ui.widgets import <PERSON><PERSON>, SegmentedControl
from src.core.assets import get_car_sprite
from src.core.game_data import (
    get_all_tracks,
    get_track_data,
    get_track_display_name,
    get_all_opponents,
    get_opponent_data,
    get_opponent_display_name
)
import logging


class RaceSelectMenu:
    """
    Race selection menu where players choose track and opponent before racing.
    """

    def __init__(self, app, player, start_race_callback, return_callback):
        self.app = app
        self.player = player
        self.start_race_callback = start_race_callback
        self.return_callback = return_callback
        self.screen = app.screen

        # Fonts
        self.title_font = pg.font.SysFont('Consolas', 50, bold=True)
        self.info_font = pg.font.SysFont('Consolas', 24)
        self.small_font = pg.font.SysFont('Consolas', 18)

        # Selection state
        self.selected_track = None
        self.selected_opponent = None
        
        # Map difficulty to opponents
        self.opponents = get_all_opponents()
        self.difficulty_levels = ["EASY", "MEDIUM", "HARD"]
        
        # Add EXPERT if we have enough opponents
        if len(self.opponents) >= 4:
            self.difficulty_levels.append("EXPERT")
            
        self.selected_difficulty = "MEDIUM"
        self.difficulty_multipliers = {
            "EASY": 1.0,
            "MEDIUM": 1.5,
            "HARD": 2.0,
            "EXPERT": 3.0
        }
        
        self.tracks = get_all_tracks()
        if self.tracks:
            # Select the first available track (skip track_1)
            self.selected_track = None
            for track_id in self.tracks:
                track_data = get_track_data(track_id)
                # Pomiń zablokowane i niekompletne
                if track_data and not track_data.get('locked', False):
                    self.selected_track = track_id  # ✅ To jest "track_0", nie "0"
                    break
            if self.selected_track is None and self.tracks:
                self.selected_track = self.tracks[0]
        else:
            self.selected_track = None

        logging.info(f"[MENU] Initial track selection: {self.selected_track}")
        
        # Initial selection based on difficulty
        self._update_opponent_from_difficulty()

        # UI elements
        self.buttons = []
        self.difficulty_control = None
        self.track_slots = []  # (rect, track_id)
        self.opponent_slots = []  # (rect, opponent_id)
        # difficulty_slots removed

        # Feedback
        self.feedback_message = ""
        self.feedback_color = TEXT_MAIN
        self.feedback_timer = 0

        self.init_ui()

    def init_ui(self):
        """Initialize buttons and UI elements."""
        self.buttons = []
        cx = self.screen.get_width() // 2
        h = self.screen.get_height()
        w = self.screen.get_width()

        # Difficulty Segmented Control
        # Position needs to match _draw_opponent_selection logic: panel_x = screen.get_width() - 450
        panel_x = w - 450
        panel_y = 100
        diff_y = panel_y + 50
        
        # Width for control
        ctrl_w = 370 # Slightly smaller than panel width (400)
        ctrl_h = 40
        ctrl_x = panel_x + (400 - ctrl_w) // 2
        
        self.difficulty_control = SegmentedControl(
            pg.Rect(ctrl_x, diff_y, ctrl_w, ctrl_h),
            self.difficulty_levels,
            self.selected_difficulty,
            self._on_difficulty_change,
            app=self.app
        )

        # Back button
        self.buttons.append(Button(
            self.app.lang.get("menu_back"),
            (150, h - 50),
            self._on_back,
            app=self.app,
            custom_color=ACCENT_RED
        ))

        # Start Race button
        self.buttons.append(Button(
            self.app.lang.get("race_start"),
            (cx, h - 50),
            self._on_start_race,
            app=self.app,
            custom_color=ACCENT_GREEN
        ))

    def _on_difficulty_change(self, value):
        self.selected_difficulty = value
        self._update_opponent_from_difficulty()

    def _update_opponent_from_difficulty(self):
        """Select opponent based on difficulty level."""
        if not self.opponents:
            return

        try:
            idx = self.difficulty_levels.index(self.selected_difficulty)
            if 0 <= idx < len(self.opponents):
                self.selected_opponent = self.opponents[idx]
            else:
                self.selected_opponent = self.opponents[-1]
        except ValueError:
            # Fallback to first opponent if difficulty not found
            self.selected_opponent = self.opponents[0]

    def _on_back(self):
        self.app.audio.play_sfx('ui_back')
        self.return_callback()

    def _on_start_race(self):
        # Check if player has a car selected
        if not self.player.current_car:
            self._show_feedback(self.app.lang.get("race_no_car"), ACCENT_RED)
            self.app.audio.play_sfx('ui_error')
            return

        if self.selected_track and self.selected_opponent:
            self.app.audio.play_sfx('ui_select')
            self.start_race_callback(self.selected_track, self.selected_opponent, self.selected_difficulty)

    def _show_feedback(self, message, color=TEXT_MAIN):
        self.feedback_message = message
        self.feedback_color = color
        self.feedback_timer = 120

    def update(self, event):
        """Handle input events."""
        for btn in self.buttons:
            btn.handle_event(event)
            
        if self.difficulty_control:
            self.difficulty_control.handle_event(event)

        if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
            self._handle_click(event.pos)

    def _handle_click(self, mouse_pos):
        """Handle clicks on selection slots."""
        # Check track slots
        for rect, track_id in self.track_slots:
            if rect.collidepoint(mouse_pos):
                if self.selected_track != track_id:
                    self.selected_track = track_id
                    self.app.audio.play_sfx('ui_hover')
                return
        
        # Opponent selection is now handled by difficulty control

    def draw(self, screen):
        """Main draw method."""
        screen.fill(BG_COLOR)
        self.track_slots = []
        self.opponent_slots = []
        
        # Update feedback timer
        if self.feedback_timer > 0:
            self.feedback_timer -= 1
            if self.feedback_timer <= 0:
                self.feedback_message = ""

        # Title with shadow
        title_text = self.app.lang.get("race_title")
        # Shadow
        shadow_surf = self.title_font.render(title_text, True, (15, 15, 20))
        shadow_rect = shadow_surf.get_rect(center=(screen.get_width() // 2 + 3, 53))
        screen.blit(shadow_surf, shadow_rect)
        # Main title
        title_surf = self.title_font.render(title_text, True, ACCENT_GOLD)
        title_rect = title_surf.get_rect(center=(screen.get_width() // 2, 50))
        screen.blit(title_surf, title_rect)

        # Draw track selection (left side)
        self._draw_track_selection(screen)

        # Draw opponent selection (right side)
        self._draw_opponent_selection(screen)

        # Draw race info panel (bottom center)
        self._draw_race_info(screen)

        # Feedback message
        if self.feedback_message:
            fb_surf = self.info_font.render(self.feedback_message, True, self.feedback_color)
            fb_rect = fb_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() - 120))
            screen.blit(fb_surf, fb_rect)

        # Buttons
        for btn in self.buttons:
            btn.draw(screen)

    def _draw_track_selection(self, screen):
        """Draw track selection panel on the left."""
        mouse_pos = pg.mouse.get_pos()

        # Panel
        panel_x = 50
        panel_y = 100
        panel_w = 400
        panel_h = 380

        pg.draw.rect(screen, PANEL_BG, (panel_x, panel_y, panel_w, panel_h), border_radius=10)
        pg.draw.rect(screen, TEXT_DIM, (panel_x, panel_y, panel_w, panel_h), 2, border_radius=10)

        # Title
        title = self.app.lang.get("race_select_track")
        title_surf = self.info_font.render(title, True, TEXT_MAIN)
        screen.blit(title_surf, (panel_x + 15, panel_y + 10))

        # Track slots
        slot_y = panel_y + 50
        for track_id in self.tracks:
            track_data = get_track_data(track_id)
            if not track_data:
                continue

            is_locked = track_id == 'track_1'
            slot_rect = pg.Rect(panel_x + 15, slot_y, panel_w - 30, 100)
            is_selected = track_id == self.selected_track
            is_hovered = slot_rect.collidepoint(mouse_pos) and not is_locked

            # Background
            if is_locked:
                bg_color = (40, 40, 40)
                border_color = (60, 60, 60)
            else:
                bg_color = ACCENT_BLUE if is_selected else (BUTTON_HOVER_COLOR if is_hovered else (60, 60, 70))
                border_color = ACCENT_GOLD if is_selected else TEXT_DIM

            pg.draw.rect(screen, bg_color, slot_rect, border_radius=8)
            pg.draw.rect(screen, border_color, slot_rect, 2, border_radius=8)

            # Track Preview (Zoom on start position)
            info_x_offset = 15
            if 'maps' in self.app.assets and self.app.assets['maps']:
                map_imgs = self.app.assets['maps']
                start_pos = track_data.get('start_pos',)
                
                # Create a square crop around start position
                zoom_size = 384  # 3 tiles wide/high for a good zoom
                crop_rect = pg.Rect(0, 0, zoom_size, zoom_size)
                crop_rect.center = start_pos
                
                # Clamp crop rect to map bounds
                for map_img in map_imgs:
                    map_rect = map_img.get_rect()
                    crop_rect.clamp_ip(map_rect)
                
                try:
                    # Use subsurface to get the zoomed part
                    preview_surf = map_img.subsurface(crop_rect).copy()
                    preview_surf = pg.transform.scale(preview_surf, (80, 80))
                    
                    # Draw preview
                    preview_x = slot_rect.x + 10
                    preview_y = slot_rect.y + 10
                    screen.blit(preview_surf, (preview_x, preview_y))
                    
                    # Border for the preview
                    pg.draw.rect(screen, (100, 100, 110), (preview_x - 1, preview_y - 1, 82, 82), 1)
                    
                    # Offset other info to the right
                    info_x_offset = 105
                except:
                    # If we can't create a preview, just draw a placeholder
                    preview_surf = pg.Surface((80, 80))
                    preview_surf.fill((50, 50, 60))
                    pg.draw.rect(preview_surf, TEXT_DIM, (0, 0, 80, 80), 2)
                    screen.blit(preview_surf, (slot_rect.x + 10, slot_rect.y + 10))
                    info_x_offset = 105
            
            # Difficulty
            diff = track_data.get('difficulty', 1)
            diff_text = "★" * diff + "☆" * (3 - diff)
            diff_surf = self.small_font.render(f"{self.app.lang.get('race_difficulty')}: {diff_text}", True, ACCENT_GOLD)
            screen.blit(diff_surf, (slot_rect.x + info_x_offset, slot_rect.y + 15))

            # Laps
            laps = track_data.get('laps', 3)
            laps_surf = self.small_font.render(f"{self.app.lang.get('race_laps')}: {laps}", True, TEXT_DIM)
            screen.blit(laps_surf, (slot_rect.x + info_x_offset, slot_rect.y + 40))

            # Base reward
            reward = track_data.get('base_reward', 500)
            reward_surf = self.small_font.render(f"${reward}", True, ACCENT_GREEN)
            screen.blit(reward_surf, (slot_rect.x + slot_rect.width - 80, slot_rect.y + 40))

            if is_locked:
                # Draw "Map not ready" message below the slot
                msg = self.app.lang.get("map_not_ready")
                msg_surf = self.small_font.render(msg, True, ACCENT_RED)
                msg_rect = msg_surf.get_rect(center=(slot_rect.centerx, slot_rect.bottom + 15))
                screen.blit(msg_surf, msg_rect)
                
                slot_y += 110 + 30 # Extra space for message
            else:
                self.track_slots.append((slot_rect, track_id))
                slot_y += 110 # Move to the next slot

    def _draw_opponent_selection(self, screen):
        """Draw opponent selection panel on the right."""
        mouse_pos = pg.mouse.get_pos()

        # Panel
        panel_x = screen.get_width() - 450
        panel_y = 100
        panel_w = 400
        panel_h = 380

        pg.draw.rect(screen, PANEL_BG, (panel_x, panel_y, panel_w, panel_h), border_radius=10)
        pg.draw.rect(screen, TEXT_DIM, (panel_x, panel_y, panel_w, panel_h), 2, border_radius=10)

        # Title
        title = self.app.lang.get("race_select_opponent")
        title_surf = self.info_font.render(title, True, TEXT_MAIN)
        screen.blit(title_surf, (panel_x + 15, panel_y + 10))

        # Difficulty Selection (Top of panel)
        # Using SegmentedControl
        if self.difficulty_control:
            self.difficulty_control.draw(screen)

        # Selected Opponent Display
        if self.selected_opponent:
            opp_data = get_opponent_data(self.selected_opponent)
            if opp_data:
                info_y = panel_y + 120
                
                # Container
                info_rect = pg.Rect(panel_x + 20, info_y, panel_w - 40, 150)
                pg.draw.rect(screen, (60, 60, 70), info_rect, border_radius=10)
                pg.draw.rect(screen, ACCENT_BLUE, info_rect, 2, border_radius=10)
                
                # Label
                lbl = self.small_font.render(self.app.lang.get("race_opponent") + ":", True, TEXT_DIM)
                screen.blit(lbl, (info_rect.x + 20, info_rect.y + 20))
                
                # Name
                name = get_opponent_display_name(self.selected_opponent, self.app.lang)
                name_surf = self.info_font.render(name, True, TEXT_MAIN)
                screen.blit(name_surf, (info_rect.x + 20, info_rect.y + 50))
                
                # Skill
                skill = opp_data.get('skill', 1.0)
                skill_bars = int(skill * 5)
                skill_text = "▮" * skill_bars + "▯" * (6 - skill_bars)
                skill_surf = self.small_font.render(f"Skill: {skill_text}", True, ACCENT_GOLD)
                screen.blit(skill_surf, (info_rect.x + 20, info_rect.y + 90))
                
                # Multiplier
                mult = opp_data.get('reward_mult', 1.0)
                mult_surf = self.small_font.render(f"Reward: x{mult:.1f}", True, ACCENT_GREEN)
                mult_rect = mult_surf.get_rect(bottomright=(info_rect.right - 20, info_rect.bottom - 20))
                screen.blit(mult_surf, mult_rect)

    def _draw_race_info(self, screen):
        """Draw race info panel showing selected race details."""
        if not self.selected_track or not self.selected_opponent:
            return

        track_data = get_track_data(self.selected_track)
        opp_data = get_opponent_data(self.selected_opponent)

        if not track_data or not opp_data:
            return

        # Calculate reward
        base_reward = track_data.get('base_reward', 500)
        # Use difficulty multiplier directly (opponent is selected based on difficulty, so multipliers match)
        diff_mult = self.difficulty_multipliers.get(self.selected_difficulty, 1.0)
        
        total_reward = int(base_reward * diff_mult)

        # Info panel
        panel_x = 500
        panel_y = 500
        panel_w = screen.get_width() - 1000
        panel_h = 80

        pg.draw.rect(screen, PANEL_BG, (panel_x, panel_y, panel_w, panel_h), border_radius=10)
        pg.draw.rect(screen, ACCENT_GOLD, (panel_x, panel_y, panel_w, panel_h), 2, border_radius=10)

        # Reward display
        reward_label = self.app.lang.get("race_reward")
        reward_text = f"{reward_label}: ${total_reward}"
        reward_surf = self.info_font.render(reward_text, True, ACCENT_GREEN)
        reward_rect = reward_surf.get_rect(center=(panel_x + panel_w // 2, panel_y + 25))
        screen.blit(reward_surf, reward_rect)

        # Player car display
        if self.player.current_car:
            car_sprite = get_car_sprite(self.app.assets, self.player.current_car)
            if car_sprite:
                scaled = pg.transform.scale(car_sprite, (40, 64))  # Maintain 5:8 aspect ratio
                screen.blit(scaled, (panel_x + 20, panel_y + 15))
