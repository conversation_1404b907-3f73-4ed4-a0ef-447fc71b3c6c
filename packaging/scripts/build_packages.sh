#!/bin/bash
set -e

# Version configuration
VERSION="$1"

# Validate and sanitize version
# If version is empty or looks like a git ref (e.g. refs/heads/main), force a valid semantic version
if [[ -z "$VERSION" || "$VERSION" == refs/* ]]; then
    echo "⚠️ Invalid or missing version argument '$VERSION'. Enforcing target version: 0.1.0~alpha"
    VERSION="0.1.0~alpha"
fi

# Paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"

echo "📦 Starting Package Build for version $VERSION..."

# Build DEB package
if command -v dpkg-deb &> /dev/null; then
    bash "$SCRIPT_DIR/build_deb.sh" "$VERSION"
else
    echo "⚠️ dpkg-deb not found. Skipping DEB package build."
fi

# Build RPM package
if command -v rpmbuild &> /dev/null; then
    bash "$SCRIPT_DIR/build_rpm.sh" "$VERSION"
else
    echo "⚠️ rpmbuild not found. Skipping RPM package build."
fi

echo "✅ All packages built successfully."