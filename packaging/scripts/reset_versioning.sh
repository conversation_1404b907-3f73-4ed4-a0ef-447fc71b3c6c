#!/bin/bash

# ==============================================================================
# Skrypt: reset_versioning.sh
# Opis:   Kompleksowy reset historii wersjonowania projektu.
#         1. Usuwa wszystkie wydania (Releases) na GitHub.
#         2. Usuwa wszystkie tagi lokalne i zdalne.
#         3. Tworzy i wypycha nowy tag startowy (v0.1.0-alpha).
# Autor:  CA-Racing Dev Team
# ==============================================================================

set -e # Zakończ natychmiast w przypadku błędu

# --- Konfiguracja ---
NEW_VERSION="v0.1.0-alpha"
REMOTE_NAME="origin"

# --- Kolory i formatowanie ---
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# --- Funkcje pomocnicze ---

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[OK]${NC} $1"
}

log_warn() {
    echo -e "${YELLOW}[WARN]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_requirements() {
    log_info "Sprawdzanie wymagań wstępnych..."

    if ! command -v git &> /dev/null; then
        log_error "Git nie jest zainstalowany."
        exit 1
    fi

    if ! command -v gh &> /dev/null; then
        log_error "GitHub CLI (gh) nie jest zainstalowane. Zainstaluj je, aby zarządzać wydaniami."
        log_error "Instrukcja: https://cli.github.com/"
        exit 1
    fi

    if ! gh auth status &> /dev/null; then
        log_error "Nie jesteś zalogowany w GitHub CLI. Uruchom 'gh auth login' i spróbuj ponownie."
        exit 1
    fi

    log_success "Wszystkie wymagania spełnione."
}

confirm_action() {
    echo -e "${YELLOW}!!! UWAGA - STREFA NIEBEZPIECZNA !!!${NC}"
    echo -e "${YELLOW}Ten skrypt wykona następujące destrukcyjne operacje:${NC}"
    echo -e "  1. Usunie WSZYSTKIE wydania (Releases) z GitHuba."
    echo -e "  2. Usunie WSZYSTKIE tagi lokalne i zdalne."
    echo -e "  3. Zresetuje wersję projektu do ${GREEN}${NEW_VERSION}${NC}."
    echo -e "${RED}Tej operacji nie można cofnąć!${NC}"
    echo ""
    read -p "Czy na pewno chcesz kontynuować? (wpisz 'TAK' aby potwierdzić): " confirm

    if [[ "$confirm" != "TAK" ]]; then
        log_warn "Operacja anulowana przez użytkownika."
        exit 0
    fi
}

delete_github_releases() {
    log_info "Pobieranie listy wydań z GitHuba..."
    
    # Pobierz listę tagów wydań (limit 1000 powinien wystarczyć dla większości projektów)
    releases=$(gh release list --limit 1000 --json tagName --jq '.[].tagName')

    if [ -z "$releases" ]; then
        log_success "Brak wydań na GitHubie do usunięcia."
        return
    fi

    echo "$releases" | while read -r tag; do
        if [ -n "$tag" ]; then
            log_info "Usuwanie wydania GitHub: $tag"
            # --cleanup-tag usuwa również powiązany tag git na remote
            gh release delete "$tag" --yes --cleanup-tag || log_warn "Nie udało się usunąć wydania $tag (może już nie istnieć)."
        fi
    done
    
    log_success "Wyczyszczono wydania na GitHubie."
}

delete_git_tags() {
    log_info "Czyszczenie pozostałych tagów Git..."

    # 1. Usuwanie tagów lokalnych
    if git tag | grep -q .; then
        git tag | xargs git tag -d > /dev/null
        log_success "Usunięto wszystkie tagi lokalne."
    else
        log_info "Brak lokalnych tagów."
    fi

    # 2. Usuwanie tagów zdalnych (tych, które nie były podpięte pod Releases)
    # Pobieramy listę refów tagów
    remote_tags=$(git ls-remote --tags $REMOTE_NAME | awk '{print $2}' | sed 's/refs\/tags\///' | grep -v '\^{}' || true)

    if [ -n "$remote_tags" ]; then
        log_info "Znaleziono osierocone tagi zdalne. Usuwanie..."
        # Usuwamy tagi w paczkach, aby przyspieszyć proces
        echo "$remote_tags" | xargs -n 10 git push --delete $REMOTE_NAME > /dev/null 2>&1 || true
        log_success "Usunięto tagi zdalne."
    else
        log_info "Brak zdalnych tagów do usunięcia."
    fi
}

fix_permissions() {
    # Zachowanie logiki z oryginalnego skryptu dla spójności CI
    log_info "Weryfikacja uprawnień plików skryptowych..."
    
    TARGET_SCRIPT="packaging/scripts/create_repo.sh"
    
    if [ -f "$TARGET_SCRIPT" ]; then
        chmod +x "$TARGET_SCRIPT"
        git update-index --chmod=+x "$TARGET_SCRIPT" 2>/dev/null || true
        
        if ! git diff --cached --quiet; then
            log_warn "Wykryto brakujące uprawnienia wykonywania dla $TARGET_SCRIPT."
            git commit -m "fix(ci): add execution permission to create_repo.sh"
            log_success "Utworzono commit naprawczy."
        fi
    fi
}

create_initial_version() {
    log_info "Inicjalizacja nowej wersji: $NEW_VERSION"

    # Tworzenie annotated tag
    git tag -a "$NEW_VERSION" -m "Initial Release: $NEW_VERSION - Hard Reset"
    log_success "Utworzono lokalny tag $NEW_VERSION."

    log_info "Wypychanie tagu do $REMOTE_NAME..."
    git push $REMOTE_NAME "$NEW_VERSION"
    
    log_success "Nowy tag został wypchnięty."
}

# --- Główna pętla programu ---

main() {
    check_requirements
    confirm_action
    
    echo ""
    delete_github_releases
    delete_git_tags
    fix_permissions
    create_initial_version
    
    echo ""
    echo -e "${GREEN}==================================================${NC}"
    echo -e "${GREEN}   SUKCES! Reset wersjonowania zakończony.      ${NC}"
    echo -e "${GREEN}   Obecna wersja: $NEW_VERSION                  ${NC}"
    echo -e "${GREEN}==================================================${NC}"
}

main
