import os
import glob
import math
from xml.etree import ElementTree as ET

# --- Configuration ---
MAP_PATH = "maps/map_0.tmx"
TARGET_LAYER_NAME = "Krawędzie"
TILE_WIDTH = 64
TILE_HEIGHT = 64
INNER_WALL_OFFSET = 14
OUTER_WALL_OFFSET = 22

# --- Manual Path Definition ---
# This sequence is manually derived by analyzing maps/map_0.tmx
# Actions: 'start', 'straight', 'turn_right', 'turn_left', 'finish'
# Directions are relative to grid coordinates (y increases downwards)
track_sequence = [
    # Start straight
    {'grid': (1, 8), 'action': 'start', 'dir': (0, -1)},
    {'grid': (1, 7), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (1, 6), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (1, 5), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (1, 4), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (1, 3), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (1, 2), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (1, 1), 'action': 'turn_right', 'dir': (1, 0)},
    # Top straight
    {'grid': (2, 1), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (3, 1), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (4, 1), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (5, 1), 'action': 'turn_right', 'dir': (0, 1)},
    # Short straight down
    {'grid': (5, 2), 'action': 'straight', 'dir': (0, 1)},
    {'grid': (5, 3), 'action': 'straight', 'dir': (0, 1)},
    {'grid': (5, 4), 'action': 'straight', 'dir': (0, 1)},
    {'grid': (5, 5), 'action': 'turn_left', 'dir': (-1, 0)},
    # Middle straight (left)
    {'grid': (4, 5), 'action': 'straight', 'dir': (-1, 0)},
    {'grid': (3, 5), 'action': 'straight', 'dir': (-1, 0)},
    {'grid': (2, 5), 'action': 'straight', 'dir': (-1, 0)},
    {'grid': (1, 5), 'action': 'turn_left', 'dir': (0, 1)}, # This is the same tile as a previous one, but action is different
    # Down to bottom
    {'grid': (1, 6), 'action': 'straight', 'dir': (0, 1)},
    {'grid': (1, 7), 'action': 'turn_right', 'dir': (1, 0)},
    # Bottom straight
    {'grid': (2, 7), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (3, 7), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (4, 7), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (5, 7), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (6, 7), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (7, 7), 'action': 'turn_left', 'dir': (0, -1)},
    # Up the S-bend
    {'grid': (7, 6), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (7, 5), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (7, 4), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (7, 3), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (7, 2), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (7, 1), 'action': 'turn_right', 'dir': (1, 0)},
    # Top-right straight
    {'grid': (8, 1), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (9, 1), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (10, 1), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (11, 1), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (12, 1), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (13, 1), 'action': 'straight', 'dir': (1, 0)},
    {'grid': (14, 1), 'action': 'turn_right', 'dir': (0, 1)},
    # Down the right side
    {'grid': (14, 2), 'action': 'straight', 'dir': (0, 1)},
    {'grid': (14, 3), 'action': 'straight', 'dir': (0, 1)},
    {'grid': (14, 4), 'action': 'straight', 'dir': (0, 1)},
    {'grid': (14, 5), 'action': 'straight', 'dir': (0, 1)},
    {'grid': (14, 6), 'action': 'straight', 'dir': (0, 1)},
    {'grid': (14, 7), 'action': 'turn_left', 'dir': (-1, 0)},
    # Bottom-right straight
    {'grid': (13, 7), 'action': 'straight', 'dir': (-1, 0)},
    {'grid': (12, 7), 'action': 'straight', 'dir': (-1, 0)},
    {'grid': (11, 7), 'action': 'straight', 'dir': (-1, 0)},
    {'grid': (10, 7), 'action': 'straight', 'dir': (-1, 0)},
    {'grid': (9, 7), 'action': 'straight', 'dir': (-1, 0)},
    {'grid': (8, 7), 'action': 'straight', 'dir': (-1, 0)},
    {'grid': (7, 7), 'action': 'turn_left', 'dir': (0, -1)}, # Another repeated tile
    # Finish line approach (re-uses part of the S-bend path)
    {'grid': (6, 7), 'action': 'straight', 'dir': (-1, 0)},
    {'grid': (6, 6), 'action': 'turn_right', 'dir': (0, -1)},
    {'grid': (6, 5), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (6, 4), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (6, 3), 'action': 'straight', 'dir': (0, -1)},
    {'grid': (6, 2), 'action': 'turn_left', 'dir': (-1, 0)},
    {'grid': (5, 2), 'action': 'finish', 'dir': (0, 1)}, # Finish line
]


def cleanup_old_files():
    """Deletes garbage files from previous attempts."""
    files_to_delete = [
        "src/tools/precise_concentric_bounds.py",
        "src/tools/calibrated_bounds_v2.py",
        "src/tools/generate_tmx_bounds.py",
    ]
    debug_images = glob.glob("debug_*.png")

    for f in files_to_delete + debug_images:
        try:
            os.remove(f)
            print(f"Deleted: {f}")
        except FileNotFoundError:
            print(f"Not found, skipping: {f}")
        except Exception as e:
            print(f"Error deleting {f}: {e}")

def get_tile_center(grid_pos):
    """Calculates the pixel coordinates of the center of a tile."""
    x = grid_pos[0] * TILE_WIDTH + TILE_WIDTH / 2
    y = grid_pos[1] * TILE_HEIGHT + TILE_HEIGHT / 2
    return x, y

def generate_spine_points():
    """Generates a list of center-points for the track spine."""
    return [get_tile_center(item['grid']) for item in track_sequence]

def offset_point(point, direction, distance):
    """Offsets a point by a distance in a given direction."""
    return point[0] + direction[0] * distance, point[1] + direction[1] * distance

def generate_walls(spine_points):
    """Generates inner and outer wall points from the spine."""
    inner_wall = []
    outer_wall = []

    for i in range(len(spine_points) - 1):
        p1 = spine_points[i]
        p2 = spine_points[i+1]

        # Vector for the segment
        dx = p2[0] - p1[0]
        dy = p2[1] - p1[1]

        # Normalize the vector
        length = math.sqrt(dx*dx + dy*dy)
        if length == 0: continue
        udx, udy = dx/length, dy/length

        # Perpendicular vector (for offset)
        pdx, pdy = -udy, udx

        # Offset points for this segment
        inner_wall.append(offset_point(p1, (pdx, pdy), INNER_WALL_OFFSET))
        outer_wall.append(offset_point(p1, (pdx, pdy), -OUTER_WALL_OFFSET))

        # Add the end of the segment as well
        if i == len(spine_points) - 2:
            inner_wall.append(offset_point(p2, (pdx, pdy), INNER_WALL_OFFSET))
            outer_wall.append(offset_point(p2, (pdx, pdy), -OUTER_WALL_OFFSET))


    return inner_wall, outer_wall

def format_points_for_tmx(points):
    """Formats a list of (x, y) tuples into a string for TMX polygon."""
    return " ".join([f"{p[0]:.2f},{p[1]:.2f}" for p in points])


def update_tmx_file(inner_wall, outer_wall):
    """Updates the TMX file with the new wall polygons."""
    try:
        tree = ET.parse(MAP_PATH)
        root = tree.getroot()

        # Find the target object group (layer)
        target_layer = None
        for layer in root.findall('objectgroup'):
            if layer.get('name') == TARGET_LAYER_NAME:
                target_layer = layer
                break

        if target_layer is None:
            print(f"Error: Layer '{TARGET_LAYER_NAME}' not found in {MAP_PATH}")
            return

        # Clear existing objects in the layer
        for obj in list(target_layer):
            target_layer.remove(obj)

        # Create new wall objects
        inner_wall_str = format_points_for_tmx(inner_wall)
        outer_wall_str = format_points_for_tmx(outer_wall)

        # TMX objects are relative to (0,0), so we don't need to offset them.
        # The polygon points are in world coordinates.
        inner_obj = ET.Element('object', id=str(100), name='Inner_Wall', x="0", y="0")
        ET.SubElement(inner_obj, 'polygon', {'points': inner_wall_str})

        outer_obj = ET.Element('object', id=str(101), name='Outer_Wall', x="0", y="0")
        ET.SubElement(outer_obj, 'polygon', {'points': outer_wall_str})

        target_layer.append(inner_obj)
        target_layer.append(outer_obj)

        # Write back to the file
        tree.write(MAP_PATH)
        print(f"Successfully updated '{TARGET_LAYER_NAME}' in {MAP_PATH}")

    except FileNotFoundError:
        print(f"Error: Map file not found at {MAP_PATH}")
    except ET.ParseError:
        print(f"Error: Could not parse {MAP_PATH}")
    except Exception as e:
        print(f"An unexpected error occurred: {e}")


def main():
    """Main execution function."""
    print("--- Starting Track Builder ---")

    # Part 1: System Cleanup
    print("\n--- Running Cleanup ---")
    cleanup_old_files()

    # Part 2 & 3: Generate Geometry
    print("\n--- Generating Track Geometry ---")
    spine = generate_spine_points()
    inner_wall, outer_wall = generate_walls(spine)
    print(f"Generated {len(spine)} spine points.")
    print(f"Generated {len(inner_wall)} inner wall points.")
    print(f"Generated {len(outer_wall)} outer wall points.")

    # Part 4: Update TMX
    print("\n--- Updating TMX File ---")
    update_tmx_file(inner_wall, outer_wall)

    print("\n--- Track Builder Finished ---")


if __name__ == "__main__":
    main()
