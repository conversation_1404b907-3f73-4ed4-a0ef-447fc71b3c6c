# CA-Racing Linux Repository Setup

This guide explains how to add the CA-Racing repository to your system to install the game using your standard package manager (`apt` or `dnf`).

## Prerequisites

You need `curl` and `gpg` installed on your system.

## 1. Debian / Ubuntu / Mint (`apt`)

### Step 1: Add the GPG Key
First, download and trust the repository signing key:

```bash
curl -fsSL https://piotrek1372.github.io/ca-racing/deb/Release.gpg | sudo gpg --dearmor -o /etc/apt/trusted.gpg.d/ca-racing.gpg
```

### Step 2: Add the Repository
Create a new list file for apt:

```bash
echo "deb [signed-by=/etc/apt/trusted.gpg.d/ca-racing.gpg] https://piotrek1372.github.io/ca-racing/deb stable main" | sudo tee /etc/apt/sources.list.d/ca-racing.list
```

### Step 3: Install
Update your cache and install the game:

```bash
sudo apt update
sudo apt install ca-racing
```

---

## 2. Fedora / RHEL / CentOS (`dnf` / `yum`)

### Step 1: Create the Repository Config
Create a new repo file in `/etc/yum.repos.d/`:

```bash
sudo tee /etc/yum.repos.d/ca-racing.repo <<EOF
[ca-racing]
name=CA-Racing Repository
baseurl=https://piotrek1372.github.io/ca-racing/rpm
enabled=1
gpgcheck=1
gpgkey=https://piotrek1372.github.io/ca-racing/rpm/repodata/repomd.xml.key
EOF
```

*Note: The GPG key URL assumes you have exported your public key to the root of the repo or use the default GPG export behavior. If you haven't exported the public key to a file yet, you might need to disable gpgcheck temporarily or point it to a valid key URL.*

### Step 2: Install
Install the game:

```bash
sudo dnf install ca-racing
```

---

## 3. Hosting the Repository (For Maintainers)

Once you have generated the `repo/` directory using `create_repo.sh`, you need to host it so clients can access it.

### Option A: GitHub Pages (Automated)
This is the easiest method as it's free and handles SSL automatically. We provide a script to automate this process.

1.  **Run the Deployment Script:**
    This script will take the generated `repo/` directory and push it to the `gh-pages` branch.
    ```bash
    ./packaging/scripts/deploy_to_pages.sh
    ```

2.  **Enable Pages:**
    *   Go to GitHub Repo Settings -> **Pages**.
    *   Under **Build and deployment** -> **Source**, select **Deploy from a branch**.
    *   Select `gh-pages` branch and `/ (root)` folder.
    *   Click **Save**.

3.  **URL:** Your repo will be at `https://<username>.github.io/<repo-name>/`.

### Option B: Nginx / Apache (Self-Hosted)
If you have a VPS or dedicated server.

1.  **Transfer files:**
    ```bash
    rsync -avz repo/ user@your-server:/var/www/html/ca-racing-repo/
    ```
2.  **Nginx Configuration:**
    ```nginx
    server {
        listen 80;
        server_name repo.example.com;
        root /var/www/html/ca-racing-repo;
        
        location / {
            autoindex on; # Optional: allows browsing directories
        }
    }
    ```
3.  **Update Client Configs:**
    Replace `https://piotrek1372.github.io/ca-racing` with `http://repo.example.com` in the client instructions above.

---

## CI/CD Configuration

To enable automatic publishing:

1.  **Generate a GPG Key:**
    ```bash
    gpg --full-generate-key
    ```
2.  **Export Keys:**
    ```bash
    # Private key (for GitHub Secret)
    gpg --armor --export-secret-keys <KEY_ID> > private.key
    
    # Public key (for verifying installs)
    gpg --armor --export <KEY_ID> > public.key
    ```
3.  **GitHub Secrets:**
    Go to your repository Settings -> Secrets and Variables -> Actions -> New Repository Secret.
    *   `GPG_PRIVATE_KEY`: Content of `private.key`
    *   `GPG_PASSPHRASE`: The passphrase you set for the key.
