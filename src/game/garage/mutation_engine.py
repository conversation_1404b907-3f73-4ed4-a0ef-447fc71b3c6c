# -*- coding: utf-8 -*-
"""
Ten plik zawiera implementację silnika mutacji (MutationEngine),
kt<PERSON><PERSON> odpowiada za ewolucję pojazdu na podstawie danych z wyścigów.
"""
import random
from .vehicle_grid import VehicleGrid
from .vehicle_cell import VehicleCell

class MutationEngine:
    """
    Zarządza logiką ewolucji pojazdu w oparciu o reguły
    generowane na podstawie "Bio-Danych" z wyścigu.
    """
    def __init__(self, vehicle_grid: VehicleGrid, race_bio_data: dict):
        self.grid = vehicle_grid
        self.bio_data = race_bio_data
        self.rules = self._generate_rules_from_bio_data()

    def _generate_rules_from_bio_data(self):
        """
        Generuje dynamiczne reguły ewolucji na podstawie danych z wyścigu.
        Klucze w słowniku `rules` mogą być używane do sterowania
        różnymi aspektami mutacji.
        """
        rules = {
            "tread_mutation_chance": 0.1,
            "tread_target_type": "base",
            "armor_growth_chance": 0.1,
            "armor_target_type": "base"
        }

        # Przykład: Jeśli pojazd spędził >50% czasu na błocie,
        # zwiększ szansę na mutację opon w kierunku "Mud-Evolved Treads".
        if self.bio_data.get("mud_time_percentage", 0) > 0.5:
            rules["tread_mutation_chance"] = 0.75
            rules["tread_target_type"] = "MudEvolvedTreads"

        # Przykład: Duża liczba kolizji zwiększa szansę na wzmocnienie pancerza.
        if self.bio_data.get("collision_count", 0) > 10:
            rules["armor_growth_chance"] = 0.6
            rules["armor_target_type"] = "ReinforcedPlating"

        return rules

    def evolve_vehicle(self):
        """
        Przeprowadza jeden cykl ewolucji pojazdu.

        Tworzy nową siatkę, aby zmiany w jednej komórce nie wpływały
        na obliczenia dla innych komórek w tej samej generacji.
        """
        new_grid = self.grid.from_json(self.grid.to_json()) # Głęboka kopia siatki

        for x, y, cell in self.grid.iter_cells():
            neighbors = self.grid.get_neighbors(x, y)
            new_cell_type = self._apply_rules_to_cell(cell, neighbors)
            if new_cell_type:
                new_grid.set_cell(x, y, VehicleCell.create(new_cell_type))

        # Można tu również dodać logikę "narodzin" komórek na pustych polach
        # inspirowaną Grą w Życie Conwaya.

        return new_grid

    def _apply_rules_to_cell(self, cell: VehicleCell, neighbors: list):
        """
        Aplikuje reguły do pojedynczej komórki.
        Zwraca nowy typ komórki lub None, jeśli brak zmian.
        """
        # Prosta, przykładowa logika mutacji.
        # W docelowej implementacji można to znacznie bardziej rozbudować.

        # Mutacja opon
        if "Tread" in cell.cell_type or cell.cell_type == "base": # Załóżmy, że opony mają "Tread" w nazwie
            if random.random() < self.rules["tread_mutation_chance"]:
                return self.rules["tread_target_type"]

        # Mutacja pancerza
        if "Plating" in cell.cell_type or cell.cell_type == "base": # Załóżmy, że pancerz ma "Plating" w nazwie
             if random.random() < self.rules["armor_growth_chance"]:
                return self.rules["armor_target_type"]

        return None
