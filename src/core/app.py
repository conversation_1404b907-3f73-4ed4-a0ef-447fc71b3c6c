"""
CA-Racing - Main Application
============================
Main application class managing game loop, state transitions, and core subsystems.

✅ MULTIPLAYER FIX v1.2 - FINAL:
- Fixed race start in multiplayer mode
- Race object properly assigned to multiplayer_session
- Player data (name, car, parts) loaded from SinglePlayer
- Track ID properly received from server
- Fixed logging error with player.current_car
- ✅ CRITICAL FIX: Proper update/draw routing when in_race=True
"""
import os
import sys
from typing import Optional, Dict, Any, List, Tuple
import ctypes
import platform
import logging
from src.core.game_data import get_all_tracks, normalize_track_id

import pygame as pg

from src.constants import APP_NAME, APP_VERSION, ASSETS_DIR, RESOLUTIONS, init_display_constants
from src.core.assets import load_game_assets
from src.core.audio import AudioManager
from src.core.data_manager import DataManager
from src.core.locale import LanguageManager
from src.game.session import GameSession
from src.ui.effects import ScreenFader
from src.ui.menu_main import MainMenu
from src.ui.menu_settings import GlobalSettingsMenu
from src.network.embedded_server import stop_localhost_server

logger = logging.getLogger(__name__)


class GameApp:
    """
    The main application class that manages the game loop, state transitions,
    and core subsystems (audio, assets, settings).
    """

    def __init__(self) -> None:
        """Initialize the game application."""
        logger.info(f"=== {APP_NAME} v{APP_VERSION} ===")
        logger.info(f"Platform: {platform.system()} {platform.release()}")
        logger.info(f"Python: {sys.version}")

        # Initialize pygame
        pg.init()

        # ✅ AUDIO CONFIGURATION
        try:
            pg.mixer.quit()
            pg.mixer.init(frequency=48000, size=-16, channels=2, buffer=2048)
            logger.info("✅ Audio system initialized (48000Hz, buffer=2048)")
        except pg.error as e:
            logger.error(f"❌ Audio initialization failed: {e}")

        # Initialize managers
        self.data_manager: DataManager = DataManager()
        self.lang: LanguageManager = LanguageManager()

        # Load global settings
        self.global_settings: Dict[str, Any] = self.data_manager.load_global_settings()
        self.lang.load_language(self.global_settings.get("language", "en"))

        # Apply graphics settings
        self.screen: Optional[pg.Surface] = None
        self.clock: pg.time.Clock = pg.time.Clock()
        self.running: bool = True
        
        self.apply_graphics(self.global_settings)

        # ✅ CRITICAL: Initialize display constants AFTER creating display
        init_display_constants()

        # Set window title and icon
        pg.display.set_caption(f"{APP_NAME} v{APP_VERSION}")
        try:
            icon_path = os.path.join(ASSETS_DIR, 'ui', 'icon.png')
            if os.path.exists(icon_path):
                icon = pg.image.load(icon_path)
                pg.display.set_icon(icon)
        except Exception as e:
            logger.warning(f"Could not load icon: {e}")

        # Initialize FPS font
        try:
            self.fps_font = pg.font.SysFont('Consolas', 20, bold=True)
        except Exception:
            self.fps_font = pg.font.Font(None, 20)

        # Load assets AFTER display initialization
        try:
            self.assets: Dict[str, Any] = load_game_assets()
        except Exception as e:
            print(f"[MAIN] Critical Asset Error: {e}")
            logger.error(f"[MAIN] Critical Asset Error: {e}", exc_info=True)
            self.assets = {'sfx': {}, 'music': {}}

        # Initialize Audio Manager and start music
        self.audio: AudioManager = AudioManager(self)
        self.audio.play_music('main_theme')

        # Initialize UI components
        self.state: str = 'MENU'
        self.menu: MainMenu = MainMenu(self)
        self.settings: GlobalSettingsMenu = GlobalSettingsMenu(self, self.return_to_menu)
        self.session: Optional[GameSession] = None

        # Screen fader for transitions
        if self.screen:
            self.fader: ScreenFader = ScreenFader(self.screen.get_size())
        else:
            self.fader = ScreenFader((1280, 720))

        # Multiplayer lobby state
        self.multiplayer_session = None
        self.multiplayer_lobby = None
        self.multiplayer_connecting = False

    def start_multiplayer_race(self, track_id, race_info=None):
        """
        ✅ FIXED v1.2: Start a multiplayer race session.
        
        This method is called when the server broadcasts race_start signal.
        It creates the Race object and properly assigns it to multiplayer_session.
        
        Args:
            track_id: str - ID of the track to race on (from server)
            race_info: dict - Optional race info from server (for spawn positions)
        """
        from src.core.game_data import normalize_track_id, get_track_data, get_all_tracks
        
        track_id = normalize_track_id(track_id)
        logging.info(f"[APP] Starting multiplayer race: track_id={track_id}")
        
        # Upewnij się, że mamy sesję singleplayer do pobrania danych gracza
        if not self.session or not isinstance(self.session, GameSession):
            logging.info("[APP] No single player session, creating temporary one for player data")
            
            # Use slot_id from multiplayer_session if available, otherwise default to 1
            slot_id = 1
            if self.multiplayer_session and hasattr(self.multiplayer_session, 'slot_id'):
                slot_id = self.multiplayer_session.slot_id
                
            # Create GameSession directly to avoid changing state to 'GAME'
            # self.start_game_session(slot_id) would change state to 'GAME'
            self.session = GameSession(self, slot_id)
        
        # Pobierz dane gracza z sesji singleplayer (nazwa, samochód, części)
        player = self.session.player
        
        # ✅ FIX: Assign player_id from multiplayer session to player object for spawn logic
        if self.multiplayer_session and self.multiplayer_session.player_id:
            player.player_id = self.multiplayer_session.player_id
            # Determine host status if possible (e.g. from race_info or session)
            # For now, we rely on player_id hash in Race class if is_host is not set
            if race_info:
                # Check if we injected is_local_host in multiplayer_session
                if 'is_local_host' in race_info:
                    player.is_host = race_info['is_local_host']
                    logging.info(f"[APP] Player host status set from race_info: {player.is_host}")
                elif 'players' in race_info:
                    # Fallback: Try to find our player in the list to get more info if needed
                    pass

        # Walidacja danych gracza
        if not player.current_car:
            logging.error("[APP] Player has no car selected!")
            # Wybierz pierwszy dostępny samochód
            if player.garage:
                first_car = player.garage[0]
                if isinstance(first_car, dict):
                    player.current_car = first_car.get('model_id') or first_car.get('name')
                else:
                    player.current_car = first_car
                logging.info(f"[APP] Auto-selected car: {player.current_car}")
            else:
                # Fallback to default car if garage is empty
                player.current_car = "car_0"
                logging.info(f"[APP] Auto-selected default car: {player.current_car}")
                # logging.error("[APP] Player has no cars at all!")
                # raise ValueError("Player has no cars available for race")
        
        from src.game.race import Race
        
        def on_race_complete():
            """Callback when race finishes"""
            logging.info("[APP] Multiplayer race complete. Returning to lobby.")
            
            # Wyczyść wyścig z multiplayer_session
            if self.multiplayer_session:
                self.multiplayer_session.race = None
                self.multiplayer_session.in_race = False
                self.multiplayer_session.in_lobby = True
                self.multiplayer_session.ready = False
            
            # Przywróć stan lobby
            self.state = 'MULTIPLAYER_LOBBY'
            
            # ✅ FIX: Force a garbage collection to clean up race resources
            import gc
            gc.collect()

        try:
            # Walidacja mapy
            track_data = get_track_data(track_id)
            if track_data is None:
                logging.error(f"[APP] Track data not found for: {track_id}")
                all_tracks = get_all_tracks()
                track_id = all_tracks[0] if all_tracks else "track_0"
                logging.info(f"[APP] Using fallback track: {track_id}")

            # ✅ KLUCZOWA POPRAWKA: Utwórz obiekt Race
            race_session = Race(
                self,
                player,  # Przekazujemy obiekt Player z danymi (nazwa, samochód, części)
                track_id,
                opponent_id=None,  # Brak AI w multiplayer
                on_complete_callback=on_race_complete,
                difficulty='MEDIUM',
                multiplayer=True  # CRITICAL: wyłącza AI
            )
            
            # ✅ KLUCZOWA POPRAWKA: Przypisz wyścig do multiplayer_session
            if self.multiplayer_session:
                self.multiplayer_session.race = race_session
                logging.info("[APP] ✅ Race assigned to multiplayer_session.race")
                
                # ✅ CRITICAL FIX: Change app state to MULTIPLAYER_RACE
                # Note: The state is actually handled by MULTIPLAYER_LOBBY state checking in_race flag
                # But setting it here explicitly helps with debugging
                # self.state = 'MULTIPLAYER_RACE' 
                # logging.info("[APP] ✅ State changed to MULTIPLAYER_RACE")
            else:
                logging.error("[APP] ❌ No multiplayer_session available!")
                raise RuntimeError("No multiplayer session active")
            
            # ✅ FIX v1.1: Bezpieczne logowanie car_id
            car_info = "None"
            if player.current_car:
                if isinstance(player.current_car, str):
                    car_info = player.current_car
                elif hasattr(player.current_car, 'car_id'):
                    car_info = player.current_car.car_id
                else:
                    car_info = str(player.current_car)
            
            logging.info(f"[APP] ✅ Multiplayer race started successfully: {track_id}")
            logging.info(f"[APP]    Player: {player.name}")
            logging.info(f"[APP]    Car: {car_info}")
            
        except Exception as e:
            logging.error(f"[APP] ❌ Failed to start multiplayer race: {e}")
            import traceback
            logging.error(traceback.format_exc())
            
            # Przywróć stan lobby w przypadku błędu
            if self.multiplayer_session:
                self.multiplayer_session.in_race = False
                self.multiplayer_session.in_lobby = True
            raise

    def apply_graphics(self, s: Dict[str, Any]) -> None:
        """Apply graphics settings to the display."""
        idx = s.get("resolution_idx", 0)
        idx = idx if idx < len(RESOLUTIONS) else 0
        w, h = RESOLUTIONS[idx]
        flags = pg.DOUBLEBUF
        if s.get("fullscreen"):
            flags |= pg.FULLSCREEN
        self.screen = pg.display.set_mode((w, h), flags)

    def start_game_session(self, slot_id: int) -> None:
        """Start a new game session."""
        slots = self.data_manager.check_save_slots()
        if not slots[slot_id]['exists']:
            if not self.data_manager.create_new_save(slot_id):
                return
        self.session = GameSession(self, slot_id)
        self.state = 'GAME'

    def change_state(self, new_session) -> None:
        """Switch to a new session (e.g. MultiplayerSession)."""
        self.session = new_session
        self.state = 'GAME'

    def close_session(self) -> None:
        """Close the current game session and return to the main menu."""
        self.session = None
        self.state = 'MENU'
        self.audio.play_music('main_theme')
        self.menu.init_main_view()

    def open_global_settings(self) -> None:
        """Switch state to the global settings menu."""
        self.state = 'SETTINGS'
        self.settings.init_main_view()

    def return_to_menu(self) -> None:
        """Return to the main menu from other states."""
        self.state = 'MENU'
        self.menu.init_main_view()

    def quit_game(self) -> None:
        """Signal the application to quit."""
        self.running = False

    def draw_fps(self) -> None:
        """Draw FPS counter in top-left corner if enabled."""
        if self.global_settings.get("show_fps", False) and self.screen:
            try:
                fps = int(self.clock.get_fps())
                fps_text = self.fps_font.render(f"FPS: {fps}", True, (0, 255, 0))
                self.screen.blit(fps_text, (10, 10))
            except pg.error:
                try:
                    self.fps_font = pg.font.SysFont('Consolas', 20, bold=True)
                except Exception:
                    pass

    def run(self) -> None:
        """✅ FIX v1.2: Main application loop with proper multiplayer race handling."""
        while self.running:
            dt = self.clock.tick(self.global_settings.get("max_fps", 60)) / 1000.0

            # Collect events for batch processing
            events = pg.event.get()
            
            # Handle events
            for event in events:
                if event.type == pg.QUIT:
                    self.running = False
                elif self.state == 'MENU':
                    self.menu.update(event)
                elif self.state == 'SETTINGS':
                    self.settings.update(event)
                elif self.state == 'MULTIPLAYER_LOBBY':
                    # ✅ FIX v1.2: Check if racing, pass events to race
                    if self.multiplayer_session and self.multiplayer_session.is_in_race():
                        # In race - pass events to race via multiplayer_session
                        pass  # Events handled in update below
                    elif self.multiplayer_lobby:
                        # In lobby
                        self.multiplayer_lobby.handle_event(event)
                        
                        if event.type == pg.KEYDOWN and event.key == pg.K_ESCAPE:
                            if not self.multiplayer_lobby.in_room and not self.multiplayer_lobby.chat_active:
                                self.exit_multiplayer_lobby()

            # ✅ FIX v1.2: Update state - proper multiplayer race handling
            if self.state == 'GAME' and self.session:
                self.session.update(events, dt)
            elif self.state == 'MULTIPLAYER_LOBBY':
                # ✅ CRITICAL FIX: Always update session to check for pending race start
                if self.multiplayer_session:
                    self.multiplayer_session.update(events, dt)

                # Update lobby UI only if NOT in race
                if self.multiplayer_lobby and (not self.multiplayer_session or not self.multiplayer_session.is_in_race()):
                    self.multiplayer_lobby.update(dt)

            # ✅ FIX v1.2: Render - proper multiplayer race handling
            if self.screen:
                if self.state == 'MENU':
                    self.menu.draw(self.screen)
                elif self.state == 'SETTINGS':
                    self.settings.draw(self.screen)
                elif self.state == 'MULTIPLAYER_LOBBY':
                    # ✅ CRITICAL FIX: Check if we're racing
                    if self.multiplayer_session and self.multiplayer_session.is_in_race():
                        # We're racing! Draw the race
                        self.multiplayer_session.draw(self.screen)
                    elif self.multiplayer_lobby:
                        # We're in lobby
                        self.multiplayer_lobby.draw()
                    
                    if self.multiplayer_connecting:
                        self._draw_connecting_overlay()
                        
                elif self.state == 'GAME' and self.session:
                    self.session.draw()

                self.draw_fps()
                self.fader.draw(self.screen)
                pg.display.flip()

        # Cleanup
        if self.session:
            self.session.cleanup()

        # Stop embedded server if running
        try:
            stop_localhost_server()
        except Exception as e:
            logger.error(f"Error stopping embedded server: {e}")

        pg.quit()
    
    def exit_multiplayer_lobby(self):
        """Exit multiplayer lobby and return to menu"""
        logger.info("Exiting multiplayer lobby")
        
        if self.multiplayer_lobby:
            try:
                self.multiplayer_lobby.cleanup()
            except Exception as e:
                logger.error(f"Error cleaning up lobby: {e}")
        
        if self.multiplayer_session:
            try:
                self.multiplayer_session.stop()
            except Exception as e:
                logger.error(f"Error disconnecting: {e}")
        
        self.multiplayer_session = None
        self.multiplayer_lobby = None
        self.multiplayer_connecting = False
        self.return_to_menu()
    
    def _draw_connecting_overlay(self):
        """Draw 'Connecting...' overlay"""
        if not self.screen:
            return
        
        overlay = pg.Surface(self.screen.get_size())
        overlay.set_alpha(128)
        overlay.fill((0, 0, 0))
        self.screen.blit(overlay, (0, 0))
        
        font = pg.font.Font(None, 64)
        text = font.render("Connecting to server...", True, (255, 255, 255))
        text_rect = text.get_rect(center=(self.screen.get_width() // 2, self.screen.get_height() // 2))
        self.screen.blit(text, text_rect)
        
        dots_count = (pg.time.get_ticks() // 500) % 4
        dots = "." * dots_count
        dots_text = font.render(dots, True, (255, 255, 255))
        dots_rect = dots_text.get_rect(center=(self.screen.get_width() // 2, self.screen.get_height() // 2 + 60))
        self.screen.blit(dots_text, dots_rect)