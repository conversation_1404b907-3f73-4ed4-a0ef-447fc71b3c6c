# -*- coding: utf-8 -*-
"""
Ten plik definiuje podstawowe bloki budulcowe pojazdu - "Komórki Pojazdu" (VehicleCell).
"""
import json

class VehicleCell:
    """
    Klasa bazowa dla wszystkich części (komórek) pojazdu.
    """
    def __init__(self, cell_type="base", mass=10, grip=1.0, aerodynamics=1.0):
        self.cell_type = cell_type
        self.stats = {
            "mass": mass,
            "grip": grip,
            "aerodynamics": aerodynamics
        }
        self.mutation_paths = {}  # Słownik możliwych ewolucji

    def add_mutation_path(self, target_type, conditions):
        """
        Definiuje możliwą ścieżkę ewolucji dla tej komórki.
        'conditions' to funkcja lambda lub obiekt, który sprawdza bio-dane.
        """
        self.mutation_paths[target_type] = conditions

    @staticmethod
    def create(cell_type, *args, **kwargs):
        """
        Fabryka do tworzenia konkretnych typów komórek.
        """
        # Używamy słownika do mapowania typów na klasy, aby uniknąć długich if-elif
        cell_classes = {
            "MudEvolvedTreads": MudEvolvedTreads,
            "ReinforcedPlating": ReinforcedPlating,
            "base": VehicleCell
        }
        cls = cell_classes.get(cell_type, VehicleCell)
        return cls(*args, **kwargs)

    def to_dict(self):
        """Zwraca reprezentację słownikową komórki."""
        return {"type": self.cell_type, "stats": self.stats}

    @classmethod
    def from_dict(cls, data):
        """Tworzy instancję komórki na podstawie danych słownikowych."""
        cell = cls.create(data.get('type', 'base'))
        cell.stats = data.get('stats', {})
        return cell

# --- Przykładowe, wyspecjalizowane komórki ---

class MudEvolvedTreads(VehicleCell):
    """
    Specjalistyczna komórka - opony zoptymalizowane do jazdy po błocie.
    """
    def __init__(self):
        super().__init__(cell_type="MudEvolvedTreads", mass=15, grip=1.8, aerodynamics=0.8)

class ReinforcedPlating(VehicleCell):
    """
    Specjalistyczna komórka - wzmocniony pancerz.
    """
    def __init__(self):
        super().__init__(cell_type="ReinforcedPlating", mass=25, grip=0.9, aerodynamics=1.2)
