#!/bin/bash
set -e

# Get script directory
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" &> /dev/null && pwd )"
cd "$SCRIPT_DIR"

# Configuration
PKGNAME="ca-racing"
AUR_REPO_URL="ssh://<EMAIL>/${PKGNAME}.git"
AUR_DIR="ca-racing-aur"

echo "🚀 Starting AUR publishing process for $PKGNAME..."
echo "📂 Working in: $(pwd)"

# Check if PKGBUILD exists
if [ ! -f "PKGBUILD" ]; then
    echo "❌ Error: PKGBUILD not found in $SCRIPT_DIR."
    exit 1
fi

# Always regenerate .SRCINFO to ensure it matches PKGBUILD
echo "🔄 Regenerating .SRCINFO..."
makepkg --printsrcinfo > .SRCINFO

# Clone AUR repository
if [ -d "$AUR_DIR" ]; then
    echo "📂 Removing existing $AUR_DIR directory..."
    rm -rf "$AUR_DIR"
fi

echo "📥 Cloning AUR repository..."
git clone "$AUR_REPO_URL" "$AUR_DIR"

# Copy files
echo "📋 Copying PKGBUILD and .SRCINFO..."
cp PKGBUILD "$AUR_DIR/"
cp .SRCINFO "$AUR_DIR/"

# Navigate to AUR directory
cd "$AUR_DIR"

# Check status
if [ -z "$(git status --porcelain)" ]; then
    echo "✨ No changes to commit."
else
    # Commit and push
    echo "💾 Committing changes..."
    git add PKGBUILD .SRCINFO
    git commit -m "Update to version $(grep 'pkgver=' PKGBUILD | cut -d= -f2)"
    
    echo "📤 Pushing to AUR..."
    # Ensure we are pushing to master, as AUR requires it
    git push origin HEAD:master
    
    echo "✅ Successfully published to AUR!"
    echo "🔗 https://aur.archlinux.org/packages/$PKGNAME"
fi

cd ..
rm -rf "$AUR_DIR"
echo "🧹 Cleaned up temporary directory."