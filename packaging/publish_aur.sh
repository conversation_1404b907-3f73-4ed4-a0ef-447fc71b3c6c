#!/bin/bash
set -e

echo "🚀 Publishing to AUR..."

# 1. Check if we have the PKGBUILD
if [ ! -f "dist_pkg/PKGBUILD" ]; then
    echo "❌ PKGBUILD not found in dist_pkg/. Run the build first."
    exit 1
fi

# 2. Clone AUR repo (assuming user has rights)
if [ ! -d "aur-repo" ]; then
    echo "📥 Cloning AUR repository..."
    git clone ssh://<EMAIL>/ca-racing.git aur-repo
fi

# 3. Update PKGBUILD
echo "📝 Updating PKGBUILD..."
cp dist_pkg/PKGBUILD aur-repo/PKGBUILD
cd aur-repo

# 4. Generate .SRCINFO
echo "ℹ️ Generating .SRCINFO..."
makepkg --printsrcinfo > .SRCINFO

# 5. Commit and Push
echo "📤 Pushing to AUR..."
git add PKGBUILD .SRCINFO
git commit -m "Update to version $(grep pkgver PKGBUILD | cut -d= -f2)"
git push

echo "✅ Successfully published to AUR!"
