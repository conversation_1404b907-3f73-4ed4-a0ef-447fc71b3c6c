# GPG Key Setup for GitHub Actions

This guide describes the process of generating a GPG key pair (RSA 4096), exporting the private key, and configuring secrets in the GitHub repository. This is necessary for automatically signing packages (RPM, DEB) and repositories.

## 1. Generating a GPG Key

Open a terminal and run the following command to start the interactive key generation process:

```bash
gpg --full-generate-key
```

Follow the on-screen instructions:
1.  **Key type:** Select `(1) RSA and RSA` (default).
2.  **Key size:** Enter `4096`.
3.  **Validity:** Select `0` (key does not expire) or set a period according to your preference. Confirm by typing `y`.
4.  **Identification data:**
    *   **Real name:** E.g., `Piotrek2713`
    *   **Email address:** `<EMAIL>`
    *   **Comment:** You can leave it empty or enter e.g., `CA-Racing Signing Key`
    *   Confirm by typing `O` (Okay).
5.  **Passphrase:** You will be asked to provide a password protecting the private key. **Remember this password!** It will be needed in GitHub Actions.

## 2. Key Identification (Key ID)

After generating the key, you need to find its ID. Use the command:

```bash
gpg --list-secret-keys --keyid-format LONG
```

You will get a result similar to this:

```text
sec   rsa4096/3AA5C34371567BD2 2026-01-04 [SC]
      C890... (fingerprint) ...
uid                 [ultimate] Piotrek2713 <<EMAIL>>
ssb   rsa4096/...
```

In the example above, the Key ID is `3AA5C34371567BD2` (the string after `rsa4096/`).

## 3. Exporting the Private Key

For GitHub Actions to sign packages on your behalf, you must export the private key in ASCII-armored format.

Replace `YOUR_KEY_ID` with your ID from the previous step:

```bash
gpg --armor --export-secret-keys YOUR_KEY_ID > private.key
```

Display the file content (you will need to copy everything, including the lines `-----BEGIN PGP PRIVATE KEY BLOCK-----` and `-----END...`):

```bash
cat private.key
```

## 4. Configuring Secrets on GitHub

1.  Go to your repository on GitHub.
2.  Select the **Settings** tab.
3.  In the menu on the left, select **Secrets and variables** -> **Actions**.
4.  Click the **New repository secret** button.

Add two secrets:

### Secret 1: Private Key
*   **Name:** `GPG_PRIVATE_KEY`
*   **Secret:** Paste the entire content of the `private.key` file (generated in step 3).

### Secret 2: Passphrase
*   **Name:** `GPG_PASSPHRASE`
*   **Secret:** Enter the password you set during key generation in step 1.

## 5. Verification

After adding the secrets, the next time the `Release Build` workflow runs (e.g., after pushing a new tag), the "Import GPG key" step will be executed, and the packages will be digitally signed.
