"""
Additional methods for Room class in server.py
These should be added to the Room class after the start_heartbeat_monitor method.
"""

def load_waypoints(self):
    """
    Load waypoints from TMX map file.
    
    Waypoints are stored in map metadata or as object layer.
    For now, we'll use a simple default waypoint list.
    TODO: Parse from actual TMX file using pytmx
    """
    # Default waypoints for map_0 (example coordinates)
    # In production, this should parse the TMX file
    waypoint_presets = {
        'map_0.tmx': [
            (400, 300), (800, 300), (1200, 400), 
            (1400, 800), (1200, 1200), (800, 1400),
            (400, 1200), (200, 800)
        ],
        'map_1.tmx': [
            (500, 400), (900, 500), (1100, 900),
            (900, 1300), (500, 1300), (300, 900)
        ]
    }
    
    self.waypoints = waypoint_presets.get(
        self.track_name,
        [(400, 300), (800, 300), (800, 800), (400, 800)]  # Fallback
    )
    
    logger.info(f"Room {self.room_id}: Loaded {len(self.waypoints)} waypoints for {self.track_name}")

def calculate_rankings(self) -> List[Dict]:
    """
    Calculate race rankings based on waypoint system.
    
    Ranking logic:
    1. Most laps completed (descending)
    2. Highest current waypoint index (descending)
    3. Shortest distance to next waypoint (ascending)
    
    Returns:
        List of ranking dicts sorted by position
    """
    if not self.players or not self.waypoints:
        return []
    
    player_stats = []
    
    for player_id, player in self.players.items():
        # Calculate distance to next waypoint
        next_waypoint_idx = player.current_waypoint % len(self.waypoints)
        next_wp_x, next_wp_y = self.waypoints[next_waypoint_idx]
        
        distance_to_next = math.sqrt(
            (player.x - next_wp_x) ** 2 +
            (player.y - next_wp_y) ** 2
        )
        
        player_stats.append({
            'player_id': player_id,
            'player_name': player.player_name,
            'laps': player.lap,
            'waypoint': player.current_waypoint,
            'distance': distance_to_next,
            'finished': player.finished
        })
    
    # Sort by: laps (desc), waypoint (desc), distance (asc)
    sorted_stats = sorted(
        player_stats,
        key=lambda p: (-p['laps'], -p['waypoint'], p['distance'])
    )
    
    # Assign positions
    for idx, stat in enumerate(sorted_stats):
        stat['position'] = idx + 1
    
    return sorted_stats

async def start_physics_broadcast(self):
    """
    Broadcast physics updates at 30Hz during race.
    
    This sends:
    - All player positions, velocities, and states
    - Current race rankings
    """
    interval = 1.0 / self.PHYSICS_BROADCAST_RATE
    
    try:
        while self.state == RoomState.RACING and len(self.players) > 0:
            start_time = time.time()
            
            # Collect all player states
            states = {}
            for player_id, player in self.players.items():
                states[player_id] = {
                    'x': player.x,
                    'y': player.y,
                    'angle': player.angle,
                    'velocity_x': player.velocity_x,
                    'velocity_y': player.velocity_y,
                    'is_drifting': player.is_drifting,
                    'lap': player.lap,
                    'current_waypoint': player.current_waypoint,
                    'finished': player.finished
                }
            
            # Broadcast physics sync
            await self.broadcast_to_all({
                'type': 'physics_sync',
                'timestamp': time.time(),
                'states': states
            })
            
            #Calculate and broadcast rankings
            rankings = self.calculate_rankings()
            await self.broadcast_to_all({
                'type': 'rankings',
                'data': rankings,
                'timestamp': time.time()
            })
            
            # Wait for next tick
            elapsed = time.time() - start_time
            sleep_time = max(0, interval - elapsed)
            await asyncio.sleep(sleep_time)
            
    except asyncio.CancelledError:
        logger.info(f"Physics broadcast cancelled for Room {self.room_id}")
    except Exception as e:
        logger.error(f"Error in physics broadcast: {e}")

async def broadcast_to_all(self, message: Dict):
    """Broadcast message to all players in room"""
    tasks = []
    for player in self.players.values():
        if not player.writer.is_closing():
            try:
                data = json.dumps(message) + '\\n'
                player.writer.write(data.encode())
                tasks.append(player.writer.drain())
            except Exception as e:
                logger.error(f"Failed to send to {player.player_id}: {e}")
    
    if tasks:
        await asyncio.gather(*tasks, return_exceptions=True)
