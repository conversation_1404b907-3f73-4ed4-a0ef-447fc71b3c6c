#!/bin/bash

# ==============================================================================
# Skrypt do wdrażania serwera CA-Racing na maszynę wirtualną Google Cloud
# ==============================================================================
#
# Użycie:
# 1. Wypełnij poniższe zmienne swoimi danymi.
# 2. Nadaj skryptowi uprawnienia do wykonania: chmod +x deploy_to_gcp.sh
# 3. Uruchom skrypt: ./deploy_to_gcp.sh
#
# ==============================================================================

# --- KONFIGURACJA ---
# Zastąp poniższe wartości swoimi danymi z Google Cloud.

# Nazwa Twojej instancji VM (np. "ca-racing-server-vm")
INSTANCE_NAME="twoja-nazwa-instancji"

# Strefa, w której znajduje się Twoja instancja (np. "europe-central2-a")
ZONE="twoja-strefa"

# Ścieżka do katalogu na serwerze, gdzie znajdują się pliki gry
# (np. "/home/<USER>/CA-Racing")
REMOTE_DIR="/sciezka/do/katalogu/na/serwerze"

# Nazwa skryptu uruchamiającego serwer na maszynie wirtualnej
# (jeśli używasz, np. "start_server.sh")
SERVER_START_SCRIPT="start_server.sh"
# --- KONIEC KONFIGURACJI ---


# Sprawdzenie, czy zmienne zostały ustawione
if [[ "$INSTANCE_NAME" == "twoja-nazwa-instancji" ]] || [[ "$ZONE" == "twoja-strefa" ]] || [[ "$REMOTE_DIR" == "/sciezka/do/katalogu/na/serwerze" ]]; then
  echo "!!! BŁĄD: Proszę wypełnić zmienne INSTANCE_NAME, ZONE i REMOTE_DIR w skrypcie."
  exit 1
fi

echo ">>> Rozpoczynanie wdrażania na instancję: $INSTANCE_NAME..."

# 1. Kopiowanie zaktualizowanego pliku server.py
echo "1/3: Kopiowanie server.py..."
gcloud compute scp ./server.py "$INSTANCE_NAME":"$REMOTE_DIR/server.py" --zone="$ZONE"
if [ $? -ne 0 ]; then
    echo "!!! BŁĄD: Kopiowanie server.py nie powiodło się."
    exit 1
fi

# 2. Kopiowanie całego katalogu src
# Usuwamy najpierw stary katalog, aby uniknąć problemów z pozostałymi plikami
echo "2/3: Usuwanie starego katalogu src na serwerze..."
gcloud compute ssh "$INSTANCE_NAME" --zone="$ZONE" --command="rm -rf '$REMOTE_DIR/src'"

echo "2/3: Kopiowanie nowego katalogu src..."
gcloud compute scp --recurse ./src/ "$INSTANCE_NAME":"$REMOTE_DIR/src" --zone="$ZONE"
if [ $? -ne 0 ]; then
    echo "!!! BŁĄD: Kopiowanie katalogu src nie powiodło się."
    exit 1
fi

# 3. Restartowanie serwera
echo "3/3: Restartowanie aplikacji serwera..."

# Ta komenda zatrzyma istniejący proces serwera i uruchomi go ponownie w tle.
# Używamy `pkill`, aby zatrzymać proces Pythona uruchamiający serwer.
# Następnie uruchamiamy go ponownie za pomocą `nohup`, aby działał po zamknięciu sesji SSH.
gcloud compute ssh "$INSTANCE_NAME" --zone="$ZONE" --command=" \
    pkill -f 'python3 server.py' || echo 'Serwer nie był uruchomiony.'; \
    cd '$REMOTE_DIR' && \
    nohup python3 server.py > server.log 2>&1 & \
"

if [ $? -ne 0 ]; then
    echo "!!! OSTRZEŻENIE: Restart serwera mógł się nie powidzieć. Sprawdź logi na maszynie."
else
    echo ">>> Wdrożenie zakończone sukcesem!"
    echo ">>> Serwer został zrestartowany. Możesz sprawdzić logi poleceniem:"
    echo "gcloud compute ssh $INSTANCE_NAME --zone=$ZONE --command='tail -f $REMOTE_DIR/server.log'"
fi
