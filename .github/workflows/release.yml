name: Release and Publish

on:
  release:
    types: [published]
  workflow_dispatch:

permissions:
  contents: write
  pages: write
  id-token: write

jobs:
  build-linux:
    name: Build Linux
    runs-on: ubuntu-latest
    env:
      GPG_PRIVATE_KEY: ${{ secrets.GPG_PRIVATE_KEY }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install System Dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y rpm dpkg-dev createrepo-c build-essential libgl1-mesa-dev libasound2-dev libsdl2-dev

      - name: Install Python Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Import GPG Key
        if: env.GPG_PRIVATE_KEY != ''
        id: import_gpg
        uses: crazy-max/ghaction-import-gpg@v6
        with:
          gpg_private_key: ${{ secrets.GPG_PRIVATE_KEY }}
          passphrase: ${{ secrets.GPG_PASSPHRASE }}

      - name: Get Version
        id: get_version
        run: |
          if [[ "${{ github.ref_type }}" == "tag" ]]; then
            VERSION="${{ github.ref_name }}"
            VERSION="${VERSION#v}"
          else
            VERSION="0.0.0-dev"
          fi
          echo "VERSION=$VERSION" >> $GITHUB_OUTPUT

      - name: Make scripts executable
        run: chmod +x packaging/scripts/*.sh

      - name: Build Standalone Binary
        run: ./packaging/scripts/build_dist.sh

      - name: Create Tarball
        run: |
          VERSION="${{ steps.get_version.outputs.VERSION }}"
          mv dist/CA-Racing dist/ca-racing
          mkdir -p dist_tar
          tar -czf "dist_tar/ca-racing-${VERSION}-x86_64.tar.gz" -C dist ca-racing
          mv dist/ca-racing dist/CA-Racing

      - name: Build Packages (DEB, RPM)
        run: |
          ./packaging/scripts/build_packages.sh "${{ steps.get_version.outputs.VERSION }}"

      - name: Upload Linux Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: linux-artifacts
          path: |
            dist_tar/*.tar.gz
            artifacts/*.deb
            artifacts/*.rpm

  build-windows:
    name: Build Windows
    runs-on: windows-latest
    env:
      WINDOWS_PFX_BASE64: ${{ secrets.WINDOWS_PFX_BASE64 }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt

      - name: Make scripts executable
        shell: bash
        run: chmod +x packaging/scripts/*.sh

      - name: Build Executable
        shell: bash
        run: ./packaging/scripts/build_dist.sh

      - name: Build Windows Installer
        shell: powershell
        run: |
          choco install innosetup --no-progress
          $Version = "${{ github.ref_name }}"
          if ("${{ github.ref_type }}" -eq "tag") {
            if ($Version -match "^v") { $Version = $Version.Substring(1) }
          } else {
            $Version = "*******"
          }
          & "C:\Program Files (x86)\Inno Setup 6\ISCC.exe" /DMyAppVersion="$Version" packaging/windows/setup_script.iss

      - name: Create ZIP
        shell: powershell
        run: |
          $Version = "${{ github.ref_name }}"
          if ("${{ github.ref_type }}" -eq "tag") {
            if ($Version -match "^v") { $Version = $Version.Substring(1) }
          } else {
            $Version = "0.0.0-dev"
          }
          Compress-Archive -Path dist\CA-Racing.exe -DestinationPath "dist\ca-racing-${Version}-win64.zip"

      - name: Decode Signing Certificate
        if: env.WINDOWS_PFX_BASE64 != ''
        shell: powershell
        run: |
          $pfx_cert_byte = [System.Convert]::FromBase64String("${{ secrets.WINDOWS_PFX_BASE64 }}")
          [IO.File]::WriteAllBytes("$env:RUNNER_TEMP\cert.pfx", $pfx_cert_byte)

      - name: Create and Sign MSIX
        if: env.WINDOWS_PFX_BASE64 != ''
        shell: powershell
        run: |
          $Version = "${{ github.ref_name }}"
          if ("${{ github.ref_type }}" -eq "tag") {
            if ($Version -match "^v") { $Version = $Version.Substring(1) }
          } else {
            $Version = "0.0.0.0"
          }
          if ($Version -match "^\d+\.\d+\.\d+$") { $Version = "$Version.0" }
          
          ./packaging/scripts/create_msix.ps1 -Version $Version -CertificatePath "$env:RUNNER_TEMP\cert.pfx" -CertificatePassword "${{ secrets.WINDOWS_PFX_PASSWORD }}"

      - name: Upload Windows Artifacts
        uses: actions/upload-artifact@v4
        with:
          name: windows-artifacts
          path: |
            dist/*-setup.exe
            dist/*.zip
            dist/*.msix

  publish-release:
    name: Publish Release
    needs: [build-linux, build-windows]
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    steps:
      - name: Download Linux Artifacts
        uses: actions/download-artifact@v4
        with:
          name: linux-artifacts
          path: artifacts/linux

      - name: Download Windows Artifacts
        uses: actions/download-artifact@v4
        with:
          name: windows-artifacts
          path: artifacts/windows

      - name: Create Release
        uses: softprops/action-gh-release@v2
        with:
          files: |
            artifacts/linux/*
            artifacts/windows/*
        env:
          GITHUB_TOKEN: ${{ secrets.GITHUB_TOKEN }}

  deploy-repo:
    name: Deploy Repository
    needs: build-linux
    runs-on: ubuntu-latest
    if: startsWith(github.ref, 'refs/tags/')
    env:
      GPG_PRIVATE_KEY: ${{ secrets.GPG_PRIVATE_KEY }}
    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Download Linux Artifacts
        uses: actions/download-artifact@v4
        with:
          name: linux-artifacts
          path: artifacts

      - name: Import GPG Key
        if: env.GPG_PRIVATE_KEY != ''
        uses: crazy-max/ghaction-import-gpg@v6
        with:
          gpg_private_key: ${{ secrets.GPG_PRIVATE_KEY }}
          passphrase: ${{ secrets.GPG_PASSPHRASE }}

      - name: Install Repo Tools
        if: env.GPG_PRIVATE_KEY != ''
        run: |
          sudo apt-get update
          sudo apt-get install -y dpkg-dev createrepo-c

      - name: Create Repository Structure
        if: env.GPG_PRIVATE_KEY != ''
        env:
          GPG_PRIVATE_KEY: ${{ secrets.GPG_PRIVATE_KEY }}
          GPG_PASSPHRASE: ${{ secrets.GPG_PASSPHRASE }}
        run: ./packaging/scripts/create_repo.sh

      - name: Deploy to GitHub Pages
        if: env.GPG_PRIVATE_KEY != ''
        uses: peaceiris/actions-gh-pages@v4
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./repo
          keep_files: true
          user_name: 'github-actions[bot]'
          user_email: 'github-actions[bot]@users.noreply.github.com'
