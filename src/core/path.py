import os
import platform
import sys

def get_user_data_dir(app_name: str = "CA-Racing") -> str:
    """
    Get the platform-specific user data directory for the application.

    This function determines the appropriate directory for storing user-specific
    data (like save files and settings) based on the operating system,
    following common conventions. It creates the directory if it doesn't exist.

    - Windows: %APPDATA%\\CA-Racing
    - macOS:   ~/Library/Application Support/CA-Racing
    - Linux:   ~/.local/share/CA-Racing

    Args:
        app_name: The name of the application, used to create the directory.

    Returns:
        The absolute path to the user data directory. If creation fails,
        it falls back to a 'data' directory in the current working directory.
    """
    system = platform.system()
    
    if system == "Windows":
        base_path = os.getenv('APPDATA', os.path.expanduser("~"))
    elif system == "Darwin":  # macOS
        base_path = os.path.expanduser("~/Library/Application Support")
    else:  # Linux and other Unix-like systems
        base_path = os.getenv('XDG_DATA_HOME', os.path.expanduser("~/.local/share"))
        
    full_path = os.path.join(base_path, app_name)
    
    if not os.path.exists(full_path):
        try:
            os.makedirs(full_path, exist_ok=True)
        except OSError:
            print(f"Warning: Could not create data directory {full_path}. "
                  "Falling back to local 'data' folder.", file=sys.stderr)
            return os.path.abspath("data")
            
    return full_path
