#!/bin/bash
set -e

# Configuration
REPO_ROOT="repo"
DEB_DIR="$REPO_ROOT/deb"
RPM_DIR="$REPO_ROOT/rpm"
ARTIFACTS_DIR="artifacts"
GPG_KEY_ID="${GPG_KEY_ID:-}" # Set via env var in CI

echo "🏭 Starting Repository Creation..."

# --- 0. GPG Setup (CI Only) ---
if [ -n "$GPG_PRIVATE_KEY" ]; then
    echo "🔐 Importing GPG key..."
    echo "$GPG_PRIVATE_KEY" | gpg --import
    
    # If KEY_ID not provided, try to grab the last imported key
    if [ -z "$GPG_KEY_ID" ]; then
        GPG_KEY_ID=$(gpg --list-secret-keys --with-colons | grep sec | tail -n1 | cut -d: -f5)
    fi
    echo "🔑 Using GPG Key ID: $GPG_KEY_ID"
fi

# --- 1. Prepare Directory Structure ---
mkdir -p "$DEB_DIR"
mkdir -p "$RPM_DIR"

# Copy artifacts to repo dirs
if [ -d "$ARTIFACTS_DIR" ]; then
    echo "📦 Moving artifacts..."
    cp "$ARTIFACTS_DIR"/*.deb "$DEB_DIR/" 2>/dev/null || echo "No DEB files found"
    cp "$ARTIFACTS_DIR"/*.rpm "$RPM_DIR/" 2>/dev/null || echo "No RPM files found"
else
    echo "⚠️ Artifacts directory not found!"
    exit 1
fi

# --- 2. Debian Repository (APT) ---
echo "🐧 Generating APT repository..."

if command -v dpkg-scanpackages &> /dev/null; then
    cd "$DEB_DIR"

    # Generate Packages file
    dpkg-scanpackages . /dev/null > Packages
    gzip -k -f Packages

    # Generate Release file
    cat > Release <<EOF
Origin: CA-Racing
Label: CA-Racing Repository
Suite: stable
Codename: stable
Architectures: amd64
Components: main
Description: Official repository for CA-Racing game
EOF

    # Calculate hashes for Release file
    echo "MD5Sum:" >> Release
    md5sum Packages Packages.gz >> Release
    echo "SHA256:" >> Release
    sha256sum Packages Packages.gz >> Release

    # Sign the Release file
    if [ -n "$GPG_KEY_ID" ]; then
        echo "✍️ Signing APT Release file..."
        rm -f Release.gpg InRelease
        gpg --default-key "$GPG_KEY_ID" -abs -o Release.gpg Release
        gpg --default-key "$GPG_KEY_ID" --clearsign -o InRelease Release
    else
        echo "⚠️ GPG_KEY_ID not set, skipping APT signing"
    fi
    cd - > /dev/null
else
    echo "⚠️ dpkg-scanpackages not found. Skipping APT repository generation."
fi

# --- 3. RPM Repository (YUM/DNF) ---
echo "🎩 Generating RPM repository..."

RPM_REPO_GENERATED=false
if command -v createrepo_c &> /dev/null; then
    # Sign RPMs individually
    if [ -n "$GPG_KEY_ID" ]; then
        echo "✍️ Signing RPM packages..."
        # Create rpm macros for signing
        cat > ~/.rpmmacros <<EOF
%_signature gpg
%_gpg_path /root/.gnupg
%_gpg_name $GPG_KEY_ID
%_gpgbin /usr/bin/gpg
EOF
        
        # In some CI envs, we need to export GPG_TTY
        export GPG_TTY=$(tty)
        
        # Sign all rpms
        rpm --addsign "$RPM_DIR"/*.rpm || echo "⚠️ RPM signing failed. Continuing without signed RPMs."
    else
        echo "⚠️ GPG_KEY_ID not set, skipping RPM signing"
    fi

    # Create repodata
    if createrepo_c "$RPM_DIR"; then
        RPM_REPO_GENERATED=true
        echo "✅ RPM repository created using createrepo_c at $RPM_DIR"
        echo "DEBUG: Contents of $RPM_DIR after createrepo_c:"
        ls -la "$RPM_DIR"
        echo "DEBUG: Contents of $RPM_DIR/repodata after createrepo_c:"
        ls -la "$RPM_DIR/repodata" || true # Use || true to prevent script from failing if repodata is not immediately visible
    else
        echo "❌ createrepo_c failed to create repository."
    fi
else
    echo "⚠️ createrepo_c not found. Skipping RPM repository generation."
fi

# --- 4. Export Public Keys ---
if [ -n "$GPG_KEY_ID" ]; then
    echo "🔑 Exporting Public Key..."
    # Ensure repodata directory exists before attempting to export key
    if [ "$RPM_REPO_GENERATED" = true ]; then
        mkdir -p "$RPM_DIR/repodata"
        if [ -d "$RPM_DIR/repodata" ]; then
            gpg --armor --export "$GPG_KEY_ID" > "$RPM_DIR/repodata/repomd.xml.key"
            echo "✅ Public GPG key exported for RPM repository."
        else
            echo "❌ Failed to create repodata directory for GPG key export."
        fi
    else
        echo "⚠️ Skipping public GPG key export for RPM repo: RPM repository not generated."
    fi
    
    # Export for general use (always, if GPG_KEY_ID is present)
    gpg --armor --export "$GPG_KEY_ID" > "$REPO_ROOT/public.key"
    echo "✅ Public GPG key exported to $REPO_ROOT/public.key."
fi

echo "✅ Repository generation complete!"
echo "📂 Output in: $REPO_ROOT"