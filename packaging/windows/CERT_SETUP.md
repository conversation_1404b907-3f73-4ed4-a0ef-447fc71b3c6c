# Signing Certificate Setup (MSIX)

To make automated MSIX package building work, you must generate a certificate, export it to PFX format, and then add it as a secret in GitHub Actions.

## Option A: Windows (PowerShell)

### 1. Generating a Self-Signed Certificate

Run the following command in PowerShell as Administrator. Replace "Piotrek2713" with the publisher name defined in `AppxManifest.xml` (`Publisher` section).

```powershell
$cert = New-SelfSignedCertificate -Type Custom -Subject "CN=Piotrek2713" -KeyUsage DigitalSignature -FriendlyName "CA-Racing Dev Cert" -CertStoreLocation "Cert:\CurrentUser\My" -TextExtension @("2.5.29.37={text}1.3.6.1.5.5.7.3.3", "2.5.29.19={text}")
```

### 2. Export to PFX File

Set a password for the certificate and export it.

```powershell
$password = ConvertTo-SecureString -String "YourSecretPassword123" -Force -AsPlainText
Export-PfxCertificate -Cert $cert -FilePath "C:\Temp\CA-Racing.pfx" -Password $password
```

### 3. Encoding to Base64

GitHub Actions requires uploading the binary file as a Base64 text string.

```powershell
$pfxBytes = [System.IO.File]::ReadAllBytes("C:\Temp\CA-Racing.pfx")
$base64 = [System.Convert]::ToBase64String($pfxBytes)
$base64 | Set-Content "C:\Temp\CA-Racing_Base64.txt"
```

---

## Option B: Linux (OpenSSL)

If you are working on Linux (e.g., Arch), you can use `openssl`.

### 1. Generating Key and Certificate

```bash
openssl req -x509 -newkey rsa:4096 -keyout key.pem -out cert.pem -days 365 -nodes -subj "/CN=Piotrek2713"
```

### 2. Conversion to PFX

During this step, you will be asked to provide an export password. Remember it – this will be your `WINDOWS_PFX_PASSWORD`.

```bash
openssl pkcs12 -export -out ca-racing.pfx -inkey key.pem -in cert.pem
```

### 3. Encoding to Base64

```bash
base64 -w 0 ca-racing.pfx > ca-racing_base64.txt
```

Copy the content of the `ca-racing_base64.txt` file to the `WINDOWS_PFX_BASE64` secret.

---

## GitHub Secrets Configuration

1. Go to your repository on GitHub -> **Settings** -> **Secrets and variables** -> **Actions**.
2. Add a new secret:
   *   **Name:** `WINDOWS_PFX_BASE64`
   *   **Value:** (paste Base64 file content)
3. Add a second secret:
   *   **Name:** `WINDOWS_PFX_PASSWORD`
   *   **Value:** (password set during export)

## Important
Self-Signed certificates require the end user to install the certificate in the "Trusted People" or "Trusted Root Certification Authorities" store to be able to install the MSIX package. For publication in the Microsoft Store, the certificate will be assigned by the store (for .msixupload packages) or you must purchase a certificate from a trusted provider (e.g., DigiCert).
