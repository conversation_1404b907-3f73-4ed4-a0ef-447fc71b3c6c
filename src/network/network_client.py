"""
network_client.py - Fixed Network Client with Handshake Support
================================================================
Handles network communication with the game server in dedicated threads.

✅ v2.2 FIX - HANDSHAKE SUPPORT:
- Added client_hello handshake immediately after connection
- Fixed compatibility with server's handshake requirement
- Maintains all existing thread-based functionality
"""

import socket
import json
import threading
import time
import logging
import struct
import zlib

try:
    from src.network.protocol import PacketBuilder, PacketType
    HAS_PROTOCOL = False  # Temporarily disable binary protocol for debugging
except ImportError:
    HAS_PROTOCOL = False
    # Define minimal PacketType if missing to avoid crashes
    class PacketType:
        ROOM_LIST = 14
        HEARTBEAT = 4
        CLIENT_HELLO = 0 # Assuming 0 for welcome/hello

logger = logging.getLogger(__name__)


class NetworkClient:
    """
    Handles network communication with the game server in dedicated threads.
    - Establishes connection and manages message serialization/deserialization.
    - Runs a separate thread for receiving data to avoid blocking the main game loop.
    - Runs a dedicated heartbeat thread to keep the connection alive.
    - ✅ NEW: Sends client_hello handshake to server
    """
    HEARTBEAT_INTERVAL = 5.0  # Seconds

    def __init__(self, host, port=8444):
        self.host = host
        self.port = port
        self.socket = None
        self.connected = False
        self.running = False
        self._lock = threading.Lock()
        
        # Threads
        self.receive_thread = None
        self.heartbeat_thread = None
        
        # Callbacks
        self.on_message_callback = None
        self.on_disconnect_callback = None

    def connect(self):
        """
        Connects to the server and starts background threads for I/O and heartbeats.
        ✅ NEW: Sends client_hello handshake immediately after connection.
        Returns True on success, False on failure.
        """
        try:
            self.socket = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
            self.socket.settimeout(10)
            self.socket.connect((self.host, self.port))
            
            # ✅ FIX: Send client_hello handshake BEFORE marking as connected
            try:
                handshake = {"type": "client_hello", "version": "1.0", "timestamp": time.time()}
                
                if HAS_PROTOCOL:
                    # Send Binary Handshake (Type 0 = WELCOME/HELLO)
                    packet = PacketBuilder.build(0, handshake)
                    self.socket.sendall(packet)
                    logger.info("Sent BINARY client_hello handshake")
                else:
                    # Fallback to text
                    handshake_data = json.dumps(handshake) + "\n"
                    self.socket.sendall(handshake_data.encode('utf-8'))
                    logger.info("Sent TEXT client_hello handshake")
            except Exception as e:
                logger.error(f"Failed to send handshake: {e}")
                self.socket.close()
                return False
            
            self.connected = True
            self.running = True
            
            # Start background threads
            self.receive_thread = threading.Thread(target=self._receive_loop, daemon=True)
            self.receive_thread.start()
            logger.info("Receive thread started")

            self.heartbeat_thread = threading.Thread(target=self._heartbeat_loop, daemon=True)
            self.heartbeat_thread.start()
            logger.info("Heartbeat thread started")
            
            # Relax timeout for the receive loop to allow periodic checks
            self.socket.settimeout(1.0)
            
            logger.info(f"Successfully connected to server {self.host}:{self.port}")
            return True
            
        except (socket.timeout, socket.error, ConnectionRefusedError) as e:
            logger.error(f"Failed to connect to {self.host}:{self.port}: {e}")
            self.disconnect()
            return False

    def _get_packet_type_from_string(self, msg_type: str) -> int:
        """Map string message type to binary PacketType int"""
        mapping = {
            'heartbeat': 4,
            'request_room_list': 14, # Using ROOM_LIST ID for request
            'join_room': 16,
            'leave_room': 17,
            'client_hello': 0,
            'create_room': 15,
            'spectate_room': 19,
            'chat_message': 18,
            'input': 22,
            'ping': 30,
            'ready': 10,
        }
        return mapping.get(msg_type, 255) # 255 = Unknown

    def send_message(self, message: dict):
        """
        Serializes a message to JSON and sends it to the server.
        Thread-safe. Handles Binary/Text mode based on availability.
        """
        if not self.connected:
            logger.warning("Cannot send message, not connected.")
            return False
            
        try:
            with self._lock:
                if self.socket:
                    if HAS_PROTOCOL:
                        # Binary Send
                        msg_type = message.get('type')
                        p_type = self._get_packet_type_from_string(msg_type)
                        packet = PacketBuilder.build(p_type, message)
                        self.socket.sendall(packet)
                    else:
                        # Text Send
                        json_data = json.dumps(message) + "\n"
                        self.socket.sendall(json_data.encode('utf-8'))
            return True
        except (OSError, BrokenPipeError) as e:
            logger.error(f"Failed to send message: {e}. Disconnecting.")
            self._trigger_disconnect()
            return False
        except Exception as e:
            logger.error(f"An unexpected error occurred during send: {e}")
            self._trigger_disconnect()
            return False

    def _heartbeat_loop(self):
        """
        Periodically sends a heartbeat to the server to prevent timeouts.
        Runs in a dedicated daemon thread.
        """
        while self.running:
            try:
                time.sleep(self.HEARTBEAT_INTERVAL)
                if self.connected:
                    # ✅ FIX: Changed from 'heartbeat_response' to 'heartbeat' for consistency
                    self.send_message({"type": "heartbeat", "timestamp": time.time()})
            except Exception as e:
                logger.error(f"Heartbeat loop encountered an error: {e}")
                break
        logger.info("Heartbeat loop stopped.")

    def _recv_exactly(self, n):
        logger.debug(f"Trying to receive {n} bytes...")
        data = b''
        while len(data) < n:
            if not self.running: return None
            try:
                packet = self.socket.recv(n - len(data))
                if not packet: return None
                data += packet
                logger.debug(f"Received {len(packet)} bytes, total: {len(data)}/{n}")
            except socket.timeout:
                logger.debug("Socket timeout in _recv_exactly")
                continue
            except Exception as e:
                logger.debug(f"Exception in _recv_exactly: {e}")
                return None
        logger.debug(f"Successfully received {len(data)} bytes")
        return data

    def _map_packet_type(self, p_type: int) -> str:
        """Map binary packet type to string (for receive injection)"""
        mapping = {
            4: 'heartbeat',
            14: 'room_list', # Important!
            16: 'room_assigned',
            2: 'player_joined',
            3: 'player_left',
            0: 'connected', # WELCOME
            11: 'race_start',
            20: 'room_state',
            21: 'room_data_update',
            18: 'chat_message',
            40: 'error',
        }
        return mapping.get(p_type, f'unknown_{p_type}')

    def _receive_loop(self):
        """
        Listens for incoming data from the server.
        Handles Binary (PacketBuilder) protocol.
        Runs in a dedicated daemon thread.
        """
        logger.info("Starting receive loop...")
        logger.info(f"HAS_PROTOCOL = {HAS_PROTOCOL}")
        while self.running:
            try:
                if HAS_PROTOCOL:
                    logger.debug("Using BINARY protocol mode")
                    # BINARY MODE
                    # Read 6 bytes header
                    header = self._recv_exactly(6)
                    if not header:
                        if self.running:
                            logger.warning("Server closed connection.")
                        break

                    p_type, compressed, length = struct.unpack('!BBI', header)
                    logger.debug(f"Received packet: type={p_type}, compressed={compressed}, length={length}")
                    
                    # Read payload
                    payload_data = self._recv_exactly(length)
                    if not payload_data:
                        break
                    
                    if compressed:
                        payload_data = zlib.decompress(payload_data)
                    
                    try:
                        message = json.loads(payload_data.decode('utf-8'))
                    except json.JSONDecodeError:
                        logger.error("Failed to decode JSON payload")
                        continue
                    
                    # Inject type if missing
                    if 'type' not in message:
                        message['type'] = self._map_packet_type(p_type)
                    
                    # Handling for ROOM_LIST
                    if p_type == 14: # ROOM_LIST
                        logger.info(f"[NETWORK] Received {len(message.get('rooms', []))} rooms from server.")
                        
                else:
                    # TEXT MODE (Fallback)
                    logger.debug("Using TEXT protocol mode")
                    data = self.socket.recv(4096)
                    if not data:
                        logger.debug("No data received, server closed connection")
                        break

                    logger.debug(f"Received {len(data)} bytes of text data")

                    # Parse JSON messages separated by newlines
                    text_data = data.decode('utf-8')
                    lines = text_data.strip().split('\n')

                    for line in lines:
                        if line.strip():
                            try:
                                message = json.loads(line)
                                logger.debug(f"Parsed text message: {message.get('type', 'unknown')}")
                            except json.JSONDecodeError as e:
                                logger.warning(f"Failed to parse JSON: {e}")
                                continue
                
                if self.on_message_callback:
                    logger.debug(f"Calling message callback with: {message.get('type', 'unknown')}")
                    self.on_message_callback(message)
                else:
                    logger.warning("No message callback set, dropping message")
                    
            except socket.timeout:
                continue
            except (ConnectionResetError, OSError) as e:
                if self.running:
                    logger.error(f"Connection error in receive loop: {e}")
                break
            except Exception as e:
                if self.running:
                    logger.error(f"Unexpected error in receive loop: {e}")
                break
        
        self._trigger_disconnect()
        logger.info("Receive loop stopped.")

    def _trigger_disconnect(self):
        """Internal method to handle the disconnection sequence."""
        if not self.connected:
            return
        
        self.connected = False
        self.running = False
        
        try:
            if self.socket:
                self.socket.close()
                logger.info("Socket closed.")
        except Exception as e:
            logger.error(f"Error closing socket: {e}")
        
        if self.on_disconnect_callback:
            try:
                self.on_disconnect_callback()
            except Exception as e:
                logger.error(f"Error in disconnect callback: {e}")

    def disconnect(self):
        """
        Manually disconnect from the server and stop all threads.
        """
        logger.info("Disconnecting from server...")
        self._trigger_disconnect()
        
        # Wait for threads to finish (with timeout)
        if self.receive_thread and self.receive_thread.is_alive():
            self.receive_thread.join(timeout=2.0)
        if self.heartbeat_thread and self.heartbeat_thread.is_alive():
            self.heartbeat_thread.join(timeout=2.0)
        
        logger.info("Disconnected successfully.")

    def is_connected(self):
        """Check if currently connected to server"""
        return self.connected
    
    def set_callbacks(self, on_message, on_disconnect):
        """
        Set callback functions for network events.
        
        Args:
            on_message: Callback for received messages (takes dict parameter)
            on_disconnect: Callback for disconnection events
        """
        self.on_message_callback = on_message
        self.on_disconnect_callback = on_disconnect