"""
Game Data Lookup Module
=======================
Provides functions to look up display names and attributes for cars and parts.
Uses internal IDs for save file compatibility while exposing localized display names.
"""
import json
import os

# Game data cache
_game_data = None


def _load_game_data():
    """Loads the game data from data/game_data.json."""
    global _game_data
    if _game_data is not None:
        return _game_data
    
    data_path = os.path.join(os.path.dirname(__file__), '..', '..', 'data', 'game_data.json')
    try:
        with open(data_path, 'r', encoding='utf-8') as f:
            _game_data = json.load(f)
    except Exception as e:
        print(f"[GAME_DATA] Error loading game_data.json: {e}")
        _game_data = {"cars": {}, "parts": {"engines": {}, "breaks": {}, "boosts": {}}}
    
    return _game_data


def get_car_data(car_id):
    """
    Returns the full car data dict for a given car ID.
    Returns None if car not found.
    """
    data = _load_game_data()
    return data.get("cars", {}).get(car_id)


def get_car_display_name(car_id, lang_manager):
    """
    Returns the localized display name for a car.
    Falls back to car_id if not found.
    
    Args:
        car_id: Internal car ID (e.g., "car_0")
        lang_manager: LanguageManager instance for localization
    
    Returns:
        Localized display name string
    """
    car_data = get_car_data(car_id)
    if car_data and "name_key" in car_data:
        return lang_manager.get(car_data["name_key"])
    return car_id  # Fallback to internal ID


def get_car_size_class(car_id):
    """
    Returns the size class for a car from game data.
    Falls back to calculation if not in data.
    """
    car_data = get_car_data(car_id)
    if car_data and "size_class" in car_data:
        return car_data["size_class"]
    
    # Fallback: extract index and determine class
    if isinstance(car_id, dict):
        car_id = car_id.get('model_id') or car_id.get('name', '')
    
    try:
        parts = car_id.split('_')
        if len(parts) >= 2:
            index = int(parts[1])
            if index < 6:
                return 'XS'
            elif index < 12:
                return 'S'
            elif index < 18:
                return 'M'
    except (ValueError, IndexError, AttributeError):
        pass
    
    return 'XS'  # Default fallback


def get_part_data(part_id, part_type=None):
    """
    Returns the full part data dict for a given part ID.
    
    Args:
        part_id: Internal part ID (e.g., "xs_0_engine")
        part_type: Optional type hint ("engines", "breaks", "boosts")
    
    Returns:
        Part data dict or None if not found
    """
    data = _load_game_data()
    parts = data.get("parts", {})
    
    # If type hint provided, search that category first
    if part_type:
        return parts.get(part_type, {}).get(part_id)
    
    # Search all categories
    for category in ["engines", "breaks", "boosts"]:
        if part_id in parts.get(category, {}):
            return parts[category][part_id]
    
    return None


def get_part_display_name(part_id, lang_manager, part_type=None):
    """
    Returns the localized display name for a part.
    Falls back to part_id if not found.
    
    Args:
        part_id: Internal part ID (e.g., "xs_0_engine")
        lang_manager: LanguageManager instance for localization
        part_type: Optional type hint ("engines", "breaks", "boosts")
    
    Returns:
        Localized display name string
    """
    part_data = get_part_data(part_id, part_type)
    if part_data and "name_key" in part_data:
        return lang_manager.get(part_data["name_key"])
    return part_id  # Fallback to internal ID


def get_part_size_class(part_id, part_type=None):
    """
    Returns the size class for a part from game data.
    Falls back to extraction from ID if not in data.
    """
    part_data = get_part_data(part_id, part_type)
    if part_data and "size_class" in part_data:
        return part_data["size_class"]
    
    # Fallback: extract from ID prefix
    if not part_id or not isinstance(part_id, str):
        return None
    
    parts = part_id.lower().split('_')
    if len(parts) >= 1:
        size = parts[0].upper()
        if size in ('XS', 'S', 'M', 'L', 'XL'):
            return size
    
    return None


def is_part_compatible(car_id, part_id):
    """
    Checks if a part is compatible with a car based on their size classes.
    """
    car_class = get_car_size_class(car_id)
    part_class = get_part_size_class(part_id)

    if part_class is None:
        return False

    return car_class == part_class


def get_car_price(car_id):
    """
    Returns the price for a car.
    Returns 0 if car not found.
    """
    car_data = get_car_data(car_id)
    if car_data:
        return car_data.get("price", 0)
    return 0


def get_part_price(part_id, part_type=None):
    """
    Returns the price for a part.
    Returns 0 if part not found.
    """
    part_data = get_part_data(part_id, part_type)
    if part_data:
        return part_data.get("price", 0)
    return 0


def get_all_cars():
    """
    Returns a list of all car IDs available in the game.
    """
    data = _load_game_data()
    return list(data.get("cars", {}).keys())


def get_all_parts(part_type=None):
    """
    Returns a list of all part IDs, optionally filtered by type.

    Args:
        part_type: Optional filter ("engines", "breaks", "boosts")

    Returns:
        List of part IDs
    """
    data = _load_game_data()
    parts = data.get("parts", {})

    if part_type:
        return list(parts.get(part_type, {}).keys())

    # Return all parts from all categories
    all_parts = []
    for category in ["engines", "breaks", "boosts"]:
        all_parts.extend(parts.get(category, {}).keys())
    return all_parts


def get_parts_by_size_class(size_class, part_type=None):
    """
    Returns parts filtered by size class.

    Args:
        size_class: Size class to filter by ("XS", "S", "M")
        part_type: Optional type filter ("engines", "breaks", "boosts")

    Returns:
        List of part IDs matching the criteria
    """
    all_parts = get_all_parts(part_type)
    return [p for p in all_parts if get_part_size_class(p) == size_class]


# --- TRACK/MAP DATA ---

def get_all_tracks():
    """Returns a list of all track IDs available in the game."""
    data = _load_game_data()
    return list(data.get("tracks", {}).keys())


def get_track_data(track_id):
    """Returns the full track data dict for a given track ID."""
    data = _load_game_data()
    return data.get("tracks", {}).get(track_id)


def get_track_display_name(track_id, lang_manager):
    """Returns the localized display name for a track."""
    track_data = get_track_data(track_id)
    if track_data and "name_key" in track_data:
        return lang_manager.get(track_data["name_key"])
    return track_id


# --- OPPONENT DATA ---

def get_all_opponents():
    """Returns a list of all opponent IDs."""
    data = _load_game_data()
    return list(data.get("opponents", {}).keys())


def get_opponent_data(opponent_id):
    """Returns the opponent data dict."""
    data = _load_game_data()
    return data.get("opponents", {}).get(opponent_id)


def get_opponent_display_name(opponent_id, lang_manager):
    """Returns the localized display name for an opponent."""
    opp_data = get_opponent_data(opponent_id)
    if opp_data and "name_key" in opp_data:
        return lang_manager.get(opp_data["name_key"])
    return opponent_id


# --- CAR STATS CALCULATION ---

def get_car_stats(car_id, mounted_parts=None):
    """
    Calculates the effective stats for a car with mounted parts.

    Args:
        car_id: The car's model_id
        mounted_parts: Dict of mounted parts {"engine": "xs_0_engine", "breaks": ...}

    Returns:
        Dict with calculated stats: max_speed, acceleration, braking, boost_power
    """
    # Base stats depend on car size class
    size_class = get_car_size_class(car_id)

    base_stats = {
        'XS': {'max_speed': 200, 'acceleration': 150, 'braking': 100, 'boost_power': 0},
        'S': {'max_speed': 280, 'acceleration': 200, 'braking': 140, 'boost_power': 0},
        'M': {'max_speed': 380, 'acceleration': 280, 'braking': 200, 'boost_power': 0}
    }

    stats = base_stats.get(size_class, base_stats['XS']).copy()

    if mounted_parts:
        # Apply engine power
        if 'engine' in mounted_parts:
            engine_data = get_part_data(mounted_parts['engine'], 'engines')
            if engine_data:
                stats['acceleration'] += engine_data.get('power', 0)
                stats['max_speed'] += engine_data.get('power', 0) // 2

        # Apply brakes
        if 'breaks' in mounted_parts:
            brake_data = get_part_data(mounted_parts['breaks'], 'breaks')
            if brake_data:
                stats['braking'] += brake_data.get('braking', 0)

        # Apply boost
        if 'boost' in mounted_parts:
            boost_data = get_part_data(mounted_parts['boost'], 'boosts')
            if boost_data:
                stats['boost_power'] = boost_data.get('boost_power', 0)

    return stats
