#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
centerline_generator.py

Professional CLI tool for batch-generating high-fidelity AI navigation data 
(centerline and track width) for CA-Racing. It operates by rendering TMX map 
layers and performing luminance-based image segmentation to isolate the
drivable asphalt surface, ensuring maximum accuracy.

This module can be used as a script or imported to use the CenterlineGenerator class.
"""

import os
import json
import argparse
import collections
import warnings
import sys
from pathlib import Path
from typing import List, Tuple, Dict, Any, Optional, Set, Deque
import xml.etree.ElementTree as ET

# --- Graceful Dependency Importing ---
try:
    with warnings.catch_warnings():
        warnings.filterwarnings("ignore", category=UserWarning, module='pkg_resources')
        import pygame
    import pytmx
    import numpy as np
    from PIL import Image, ImageDraw
    from skimage.morphology import skeletonize
    from scipy.spatial import cKDTree
    from scipy.interpolate import splprep, splev
    from tqdm import tqdm
    import cv2
except ImportError as e:
    print(f"Error: A required library is missing: {e.name}", file=sys.stderr)
    print("Please install all dependencies with:", file=sys.stderr)
    print("pip install pygame pytmx numpy Pillow scikit-image scipy tqdm opencv-python", file=sys.stderr)
    sys.exit(1)

# --- Dynamic Path Constants ---
PROJECT_ROOT = Path(__file__).parent.parent.parent
MAPS_DIR = PROJECT_ROOT / 'maps'
OUTPUT_NAV_DIR = PROJECT_ROOT / 'assets' / 'navigation'

# Type alias for clarity: (row, col) or (y, x)
Point = Tuple[int, int]
# Type alias for graph: {Point: List[Point]}
Graph = Dict[Point, List[Point]]

def catmull_rom_spline(points: np.ndarray, num_samples: int = 100, is_loop: bool = False) -> np.ndarray:
    """
    Vectorized implementation of Catmull-Rom spline interpolation for smooth racing lines.
    
    :param points: Array of (x, y) coordinates.
    :param num_samples: Total number of points in the resulting curve.
    :param is_loop: Whether the curve should be closed.
    :return: Interpolated points as a numpy array.
    """
    if len(points) < 2:
        return points
    
    # Prepare points for interpolation
    if is_loop:
        # For a loop, we wrap around to ensure C1 continuity at the junction
        p = np.vstack([points[-1], points, points[0], points[1]])
    else:
        # For an open path, we duplicate endpoints to handle boundaries
        p = np.vstack([points[0], points, points[-1]])
    
    n_segments = len(p) - 3
    if n_segments <= 0:
        return points
        
    # Calculate samples per segment to reach target num_samples
    samples_per_segment = max(2, num_samples // n_segments)
    t = np.linspace(0, 1, samples_per_segment, endpoint=False)
    
    t2 = t * t
    t3 = t2 * t
    
    # Catmull-Rom basis functions (Matrix form)
    # [t^3 t^2 t 1] * 0.5 * [[-1, 3, -3, 1], [2, -5, 4, -1], [-1, 0, 1, 0], [0, 2, 0, 0]]
    f1 = 0.5 * (-t3 + 2*t2 - t)
    f2 = 0.5 * (3*t3 - 5*t2 + 2)
    f3 = 0.5 * (-3*t3 + 4*t2 + t)
    f4 = 0.5 * (t3 - t2)
    
    segments = []
    for i in range(n_segments):
        p0, p1, p2, p3 = p[i:i+4]
        # Vectorized segment calculation using broadcasting
        segment = (f1[:, np.newaxis] * p0 + 
                   f2[:, np.newaxis] * p1 + 
                   f3[:, np.newaxis] * p2 + 
                   f4[:, np.newaxis] * p3)
        segments.append(segment)
    
    # Add the final point to close the curve or finish the path
    if is_loop:
        segments.append(p[1:2])
    else:
        segments.append(p[-2:-1])
        
    return np.concatenate(segments, axis=0)

class CenterlineGenerator:
    """
    Advanced racing centerline generator using image-based skeletonization 
    and topological graph analysis.
    """
    def __init__(self, tmx_path: str, road_layer_name: Optional[str] = None):
        """
        Initializes the CenterlineGenerator.

        :param tmx_path: Path to the .tmx file.
        :param road_layer_name: Optional name of the tile layer. If None, auto-detection is used.
        """
        self.tmx_path = Path(tmx_path)
        if not self.tmx_path.exists():
            raise FileNotFoundError(f"TMX file not found: {self.tmx_path}")
            
        # pytmx requires a display surface to load images properly if using pygame loader
        self.tmx_data = pytmx.util_pygame.load_pygame(str(self.tmx_path))
        self.road_layer_name = road_layer_name if road_layer_name else self._find_track_layer()
        self.tile_w = self.tmx_data.tilewidth
        self.tile_h = self.tmx_data.tileheight
        self.width_px = self.tmx_data.width * self.tile_w
        self.height_px = self.tmx_data.height * self.tile_h
        print(f"Auto-detected track layer: '{self.road_layer_name}' ({self.width_px}x{self.height_px} px)")

    def _find_track_layer(self) -> str:
        """
        Intelligently finds the track layer name using semantic matching.
        """
        tile_layers = [layer.name for layer in self.tmx_data.visible_layers if isinstance(layer, pytmx.TiledTileLayer)]
        
        if not tile_layers:
            raise ValueError("No tile layers found in the map.")

        KEYWORDS = ['road', 'roads', 'track', 'droga', 'trasa', 'nawierzchnia', 'asphalt']
        found_layers = [name for name in tile_layers if any(keyword in name.lower() for keyword in KEYWORDS)]

        if len(found_layers) == 1:
            return found_layers[0]
        
        if not found_layers and len(tile_layers) == 1:
            return tile_layers[0]

        if len(found_layers) > 1:
            priority_layers = [name for name in found_layers if name.lower() in KEYWORDS or 'main' in name.lower()]
            if len(priority_layers) == 1:
                return priority_layers[0]
            
            raise ValueError(f"Multiple potential track layers found: {', '.join(found_layers)}")
        
        raise ValueError(f"Could not auto-detect track layer. Available: {', '.join(tile_layers)}")

    def _render_track_mask(self) -> np.ndarray:
        """
        Renders the track layer and converts it into a binary mask.
        Optimized using NumPy and OpenCV.
        """
        surface = pygame.Surface((self.width_px, self.height_px), pygame.SRCALPHA)
        surface.fill((0, 0, 0, 0))

        try:
            layer = self.tmx_data.get_layer_by_name(self.road_layer_name)
        except KeyError:
            raise ValueError(f"Layer '{self.road_layer_name}' not found.")

        # Render tiles
        for x, y, image in layer.tiles():
            if image:
                surface.blit(image, (x * self.tile_w, y * self.tile_h))
        
        # Convert to grayscale mask
        track_array = pygame.surfarray.array3d(surface).transpose(1, 0, 2)
        grayscale = cv2.cvtColor(track_array, cv2.COLOR_RGB2GRAY)
        
        # Thresholding
        _, mask = cv2.threshold(grayscale, 1, 255, cv2.THRESH_BINARY)
        
        # Morphological cleaning to ensure topological stability
        # Erosion prevents parallel track segments from merging
        kernel = cv2.getStructuringElement(cv2.MORPH_ELLIPSE, (5, 5))
        mask = cv2.erode(mask, kernel, iterations=1)
        # Closing to fill small holes in the asphalt
        mask = cv2.morphologyEx(mask, cv2.MORPH_CLOSE, kernel)
        
        return (mask / 255).astype(np.uint8)

    def _validate_connectivity(self, mask: np.ndarray) -> bool:
        """
        Validates track connectivity using BFS (Flood Fill).
        """
        points = np.argwhere(mask > 0)
        if len(points) == 0:
            return False
            
        start_node = tuple(points[0])
        visited = {start_node}
        queue: Deque[Point] = collections.deque([start_node])
        
        rows, cols = mask.shape
        while queue:
            r, c = queue.popleft()
            for dr, dc in [(-1,0), (1,0), (0,-1), (0,1)]:
                nr, nc = r + dr, c + dc
                if 0 <= nr < rows and 0 <= nc < cols and mask[nr, nc] > 0:
                    if (nr, nc) not in visited:
                        visited.add((nr, nc))
                        queue.append((nr, nc))
        
        coverage = len(visited) / len(points)
        print(f"   - Track connectivity coverage: {coverage:.2%}")
        return coverage >= 0.95

    def _skeletonize(self, mask: np.ndarray) -> np.ndarray:
        """
        Applies skeletonization to find the centerline.
        """
        skeleton = skeletonize(mask.astype(bool))
        return skeleton.astype(np.uint8)

    def _analyze_skeleton_graph(self, skeleton: np.ndarray) -> Tuple[Graph, List[Point], List[Point]]:
        """
        Analyzes the skeleton to build a graph and identify junctions/endpoints.
        """
        points = np.argwhere(skeleton > 0)
        if len(points) == 0:
            return {}, [], []

        adj: Graph = {tuple(p): [] for p in points}
        
        # Use 3x3 kernel to count neighbors (8-connectivity)
        kernel = np.array([[1, 1, 1], [1, 0, 1], [1, 1, 1]], dtype=np.uint8)
        neighbor_counts = cv2.filter2D(skeleton, -1, kernel, borderType=cv2.BORDER_CONSTANT)
        
        endpoints = []
        junctions = []
        
        rows, cols = skeleton.shape
        for r, c in points:
            pt = (r, c)
            count = neighbor_counts[r, c]
            
            if count == 1:
                endpoints.append(pt)
            elif count > 2:
                junctions.append(pt)
                
            # Build adjacency list
            for dr in [-1, 0, 1]:
                for dc in [-1, 0, 1]:
                    if dr == 0 and dc == 0: continue
                    nr, nc = r + dr, c + dc
                    if 0 <= nr < rows and 0 <= nc < cols and skeleton[nr, nc] > 0:
                        adj[pt].append((nr, nc))
        
        return adj, endpoints, junctions

    def _prune_skeleton(self, adj: Graph, min_length: int = 20) -> Graph:
        """
        Removes short dead-end branches from the skeleton graph.
        """
        pruned_adj = {k: list(v) for k, v in adj.items()}
        
        changed = True
        while changed:
            changed = False
            endpoints = [node for node, neighbors in pruned_adj.items() if len(neighbors) == 1]
            
            for start_node in endpoints:
                # Trace branch
                path = [start_node]
                curr = start_node
                while len(pruned_adj.get(curr, [])) == 1 or (len(pruned_adj.get(curr, [])) == 2 and curr != start_node):
                    neighbors = pruned_adj[curr]
                    next_node = None
                    for n in neighbors:
                        if len(path) == 1 or n != path[-2]:
                            next_node = n
                            break
                    if next_node is None or next_node in path: break
                    path.append(next_node)
                    curr = next_node
                    if len(pruned_adj.get(curr, [])) > 2: break # Hit junction
                
                if len(path) < min_length and len(pruned_adj.get(curr, [])) > 1:
                    # Prune this branch
                    for node in path[:-1]:
                        if node in pruned_adj:
                            for neighbor in pruned_adj[node]:
                                if neighbor in pruned_adj:
                                    pruned_adj[neighbor] = [n for n in pruned_adj[neighbor] if n != node]
                            del pruned_adj[node]
                    changed = True
        
        return pruned_adj

    def _find_main_loop(self, adj: Graph) -> List[Point]:
        """
        Finds the longest continuous path or loop in the graph.
        Optimized by identifying all simple paths between junctions.
        """
        if not adj: return []
        
        # 1. Identify junctions and endpoints
        nodes = list(adj.keys())
        special_nodes = [n for n in nodes if len(adj[n]) != 2]
        if not special_nodes:
            # It's a single closed loop with no junctions
            start_node = nodes[0]
            path = [start_node]
            curr = adj[start_node][0]
            while curr != start_node:
                path.append(curr)
                next_nodes = [n for n in adj[curr] if n != path[-2]]
                if not next_nodes: break
                curr = next_nodes[0]
            path.append(start_node)
            return path

        # 2. Extract all segments (paths between special nodes)
        segments = []
        visited_edges = set()
        for start_node in special_nodes:
            for neighbor in adj[start_node]:
                edge = tuple(sorted((start_node, neighbor)))
                if edge in visited_edges: continue
                
                # Trace segment
                segment = [start_node, neighbor]
                curr = neighbor
                while len(adj[curr]) == 2:
                    next_node = [n for n in adj[curr] if n != segment[-2]][0]
                    segment.append(next_node)
                    curr = next_node
                
                segments.append(segment)
                # Mark all edges in segment as visited
                for i in range(len(segment)-1):
                    visited_edges.add(tuple(sorted((segment[i], segment[i+1]))))

        # 3. Find the longest sequence of segments forming a path or loop
        # For racing tracks, we usually want the longest cycle.
        # Simple heuristic: find the longest connected component of segments
        if not segments: return []
        
        # Sort segments by length and pick the longest one as a base
        segments.sort(key=len, reverse=True)
        main_path = segments[0]
        
        # Try to extend it if it's not a loop
        while main_path[0] != main_path[-1]:
            extended = False
            # Try to attach a segment to the end
            for seg in segments:
                if seg == main_path: continue
                if seg[0] == main_path[-1] and seg[-1] not in main_path[1:]:
                    main_path.extend(seg[1:])
                    extended = True
                    break
                elif seg[-1] == main_path[-1] and seg[0] not in main_path[1:]:
                    main_path.extend(seg[-2::-1])
                    extended = True
                    break
            if not extended: break
            
        return main_path

    def _smooth_and_simplify(self, points: List[Point], is_loop: bool) -> List[Tuple[float, float]]:
        """
        Smoothes the path using Catmull-Rom splines and scales to world coordinates.
        """
        if len(points) < 2:
            return [((p[1] + 0.5) * self.tile_w, (p[0] + 0.5) * self.tile_h) for p in points]

        # Convert (row, col) -> (x, y) pixel coords
        points_px = np.array([(p[1] + 0.5, p[0] + 0.5) for p in points], dtype=np.float32)
        
        # Apply Catmull-Rom smoothing
        # num_samples is proportional to path length
        total_dist = np.sum(np.linalg.norm(np.diff(points_px, axis=0), axis=1))
        num_samples = int(max(total_dist / 5.0, 100)) # Sample every 5 pixels
        
        smoothed_px = catmull_rom_spline(points_px, num_samples=num_samples, is_loop=is_loop)
        
        # Scale to world coordinates
        final_points = [(p[0] * self.tile_w, p[1] * self.tile_h) for p in smoothed_px]
        return final_points

    def export_to_tmx(self, points: List[Tuple[float, float]], layer_name: str = "RacingLine") -> None:
        """
        Exports the generated centerline to the TMX file.
        """
        if not points: return

        try:
            tree = ET.parse(self.tmx_path)
            root = tree.getroot()

            next_id = int(root.get('nextobjectid', '1'))
            
            # Remove existing layer
            existing = root.find(f".//objectgroup[@name='{layer_name}']")
            if existing is not None:
                root.remove(existing)

            objectgroup = ET.Element('objectgroup', {'name': layer_name})
            points_str = " ".join([f"{p[0]:.2f},{p[1]:.2f}" for p in points])

            tiled_object = ET.Element('object', {
                'id': str(next_id),
                'name': 'Centerline',
                'x': "0",
                'y': "0"
            })
            ET.SubElement(tiled_object, 'polyline', {'points': points_str})
            
            objectgroup.append(tiled_object)
            root.append(objectgroup)
            root.set('nextobjectid', str(next_id + 1))
            
            tree.write(self.tmx_path, encoding='UTF-8', xml_declaration=True)
            print(f"   - Exported '{layer_name}' to '{self.tmx_path.name}'")

        except Exception as e:
            print(f"Error during TMX export: {e}", file=sys.stderr)

    def generate(self, smooth: bool = True, export_tmx: bool = False, visualize: bool = False) -> List[Tuple[float, float]]:
        """
        Executes the full generation pipeline.
        """
        screen = None
        if visualize:
            screen = pygame.display.set_mode((self.width_px, self.height_px))

        try:
            print("1. Rendering track mask...")
            mask = self._render_track_mask()
            
            print("2. Validating connectivity...")
            if not self._validate_connectivity(mask):
                print("Warning: Track might be disconnected!", file=sys.stderr)

            print("3. Skeletonizing...")
            skeleton = self._skeletonize(mask)
            
            print("4. Analyzing topology...")
            adj, endpoints, junctions = self._analyze_skeleton_graph(skeleton)
            
            print("5. Pruning artifacts...")
            pruned_adj = self._prune_skeleton(adj)
            
            print("6. Extracting main path...")
            ordered_points = self._find_main_loop(pruned_adj)
            
            if not ordered_points:
                print("Error: No path found.", file=sys.stderr)
                return []

            is_loop = ordered_points[0] == ordered_points[-1]
            print(f"   - Found path with {len(ordered_points)} nodes (Loop: {is_loop})")

            if smooth:
                print("7. Smoothing with Catmull-Rom splines...")
                final_points = self._smooth_and_simplify(ordered_points, is_loop)
            else:
                final_points = [((p[1] + 0.5) * self.tile_w, (p[0] + 0.5) * self.tile_h) for p in ordered_points]

            if export_tmx:
                print("8. Exporting to TMX...")
                self.export_to_tmx(final_points)

            return final_points
        
        except Exception as e:
            print(f"Generation failed: {e}", file=sys.stderr)
            import traceback
            traceback.print_exc()
            return []

def main():
    parser = argparse.ArgumentParser(description="CA-Racing Centerline Generator")
    parser.add_argument("tmx_path", type=str, help="Path to .tmx file")
    parser.add_argument("--no-smooth", action="store_true", help="Disable smoothing")
    parser.add_argument("--export-tmx", action="store_true", help="Export to TMX")
    parser.add_argument("--visualize", action="store_true", help="Visualize steps")
    
    args = parser.parse_args()

    if not pygame.get_init():
        pygame.init()

    if not args.visualize:
        os.environ['SDL_VIDEODRIVER'] = 'dummy'
        pygame.display.set_mode((1, 1), pygame.NOFRAME)
    
    try:
        generator = CenterlineGenerator(tmx_path=args.tmx_path)
        generator.generate(smooth=not args.no_smooth, export_tmx=args.export_tmx, visualize=args.visualize)
        print("\nDone.")
    except Exception as e:
        print(f"Fatal error: {e}", file=sys.stderr)
        sys.exit(1)
    finally:
        pygame.quit()

if __name__ == "__main__":
    main()
