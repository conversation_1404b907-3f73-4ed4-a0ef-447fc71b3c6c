import pygame
import random
import math

class Particle:
    def __init__(self, x, y, angle, speed, lifetime, color, radius):
        self.pos = pygame.Vector2(x, y)
        self.velocity = pygame.Vector2(math.cos(math.radians(angle)), math.sin(math.radians(angle))) * speed
        self.lifetime = lifetime
        self.initial_lifetime = lifetime
        self.color = color
        self.radius = radius

    def update(self, dt):
        self.pos += self.velocity * dt
        self.lifetime -= dt
        # Optional: Add physics like gravity or friction
        # self.velocity.y += 9.8 * dt  # Gravity
        self.velocity *= 0.99 # Friction

    def draw(self, surface, camera_offset):
        if self.lifetime > 0:
            # Fade out effect
            alpha = int(255 * (self.lifetime / self.initial_lifetime))
            self.color.a = max(0, min(255, alpha))
            
            # Draw particle
            temp_surface = pygame.Surface((self.radius * 2, self.radius * 2), pygame.SRCALPHA)
            pygame.draw.circle(temp_surface, self.color, (self.radius, self.radius), self.radius)
            
            # Adjust position based on camera
            draw_pos = self.pos - pygame.Vector2(camera_offset)
            surface.blit(temp_surface, (int(draw_pos.x), int(draw_pos.y)))

class ParticleEmitter:
    def __init__(self, x, y):
        self.pos = pygame.Vector2(x, y)
        self.particles = []
        self.emission_rate = 0
        self.emission_timer = 0

    def update(self, dt):
        # Update and remove dead particles
        self.particles = [p for p in self.particles if p.lifetime > 0]
        for particle in self.particles:
            particle.update(dt)

        # Emit new particles
        if self.emission_rate > 0:
            self.emission_timer += dt
            time_per_particle = 1.0 / self.emission_rate
            while self.emission_timer > time_per_particle:
                self.emission_timer -= time_per_particle
                self.emit()

    def draw(self, surface, camera_offset):
        for particle in self.particles:
            particle.draw(surface, camera_offset)

    def emit(self):
        # This method should be overridden by subclasses to define particle properties
        pass

    def start_emission(self, rate):
        self.emission_rate = rate

    def stop_emission(self):
        self.emission_rate = 0

class TireSmokeEmitter(ParticleEmitter):
    def __init__(self, x, y, surface_color):
        super().__init__(x, y)
        self.surface_color = surface_color

    def emit(self):
        angle = random.uniform(0, 360)
        speed = random.uniform(20, 50)
        lifetime = random.uniform(0.5, 1.5)
        
        # Color based on surface
        if self.surface_color == 'asphalt':
            color = pygame.Color(100, 100, 100, 150)
        elif self.surface_color == 'dirt':
            color = pygame.Color(150, 120, 80, 150)
        elif self.surface_color == 'grass':
            color = pygame.Color(80, 150, 80, 120)
        else: # Default
            color = pygame.Color(128, 128, 128, 150)
            
        radius = random.randint(3, 8)
        particle = Particle(self.pos.x, self.pos.y, angle, speed, lifetime, color, radius)
        self.particles.append(particle)

    def set_surface(self, surface_type):
        """Changes the color of particles based on the surface type."""
        self.surface_color = surface_type

class SparkEmitter(ParticleEmitter):
    def emit(self):
        angle = random.uniform(-30, 30) + 180 # Sparks fly backwards
        speed = random.uniform(100, 200)
        lifetime = random.uniform(0.1, 0.4)
        color = random.choice([pygame.Color(255, 255, 0), pygame.Color(255, 200, 0), pygame.Color(255, 255, 255)])
        radius = random.randint(1, 3)
        particle = Particle(self.pos.x, self.pos.y, angle, speed, lifetime, color, radius)
        self.particles.append(particle)
