﻿2026-01-04T18:36:37.2379613Z Current runner version: '2.330.0'
2026-01-04T18:36:37.2406332Z ##[group]Runner Image Provisioner
2026-01-04T18:36:37.2407379Z Hosted Compute Agent
2026-01-04T18:36:37.2408208Z Version: 20251211.462
2026-01-04T18:36:37.2408998Z Commit: 6cbad8c2bb55d58165063d031ccabf57e2d2db61
2026-01-04T18:36:37.2409966Z Build Date: 2025-12-11T16:28:49Z
2026-01-04T18:36:37.2410804Z Worker ID: {70301adb-d1df-4c26-a4e0-92dc881e687c}
2026-01-04T18:36:37.2411715Z ##[endgroup]
2026-01-04T18:36:37.2412447Z ##[group]Operating System
2026-01-04T18:36:37.2413279Z Ubuntu
2026-01-04T18:36:37.2414689Z 24.04.3
2026-01-04T18:36:37.2415323Z LTS
2026-01-04T18:36:37.2416029Z ##[endgroup]
2026-01-04T18:36:37.2416734Z ##[group]Runner Image
2026-01-04T18:36:37.2417514Z Image: ubuntu-24.04
2026-01-04T18:36:37.2418191Z Version: 20251215.174.1
2026-01-04T18:36:37.2419493Z Included Software: https://github.com/actions/runner-images/blob/ubuntu24/20251215.174/images/ubuntu/Ubuntu2404-Readme.md
2026-01-04T18:36:37.2421294Z Image Release: https://github.com/actions/runner-images/releases/tag/ubuntu24%2F20251215.174
2026-01-04T18:36:37.2422618Z ##[endgroup]
2026-01-04T18:36:37.2424269Z ##[group]GITHUB_TOKEN Permissions
2026-01-04T18:36:37.2426481Z Contents: read
2026-01-04T18:36:37.2427316Z Metadata: read
2026-01-04T18:36:37.2427965Z Packages: read
2026-01-04T18:36:37.2428717Z ##[endgroup]
2026-01-04T18:36:37.2431192Z Secret source: Actions
2026-01-04T18:36:37.2432194Z Prepare workflow directory
2026-01-04T18:36:37.2777559Z Prepare all required actions
2026-01-04T18:36:37.2816211Z Getting action download info
2026-01-04T18:36:37.6715481Z Download action repository 'actions/checkout@v3' (SHA:f43a0e5ff2bd294095638e18286ca9a3d1956744)
2026-01-04T18:36:37.8814774Z Download action repository 'actions/setup-python@v4' (SHA:7f4fc3e22c37d6ff65e88745f38bd3157c663f7c)
2026-01-04T18:36:37.9912853Z Download action repository 'crazy-max/ghaction-import-gpg@v5' (SHA:d6f3f49f3345e29369fe57596a3ca8f94c4d2ca7)
2026-01-04T18:36:39.3413478Z Download action repository 'actions/upload-artifact@v4' (SHA:ea165f8d65b6e75b540449e92b4886f43607fa02)
2026-01-04T18:36:39.5766624Z Complete job name: build-linux
2026-01-04T18:36:39.6642047Z ##[group]Run actions/checkout@v3
2026-01-04T18:36:39.6643643Z with:
2026-01-04T18:36:39.6644869Z   repository: piotrek1372/CA-Racing
2026-01-04T18:36:39.6646474Z   token: ***
2026-01-04T18:36:39.6647498Z   ssh-strict: true
2026-01-04T18:36:39.6648591Z   persist-credentials: true
2026-01-04T18:36:39.6649762Z   clean: true
2026-01-04T18:36:39.6650855Z   sparse-checkout-cone-mode: true
2026-01-04T18:36:39.6652137Z   fetch-depth: 1
2026-01-04T18:36:39.6653212Z   fetch-tags: false
2026-01-04T18:36:39.6654424Z   lfs: false
2026-01-04T18:36:39.6655446Z   submodules: false
2026-01-04T18:36:39.6656553Z   set-safe-directory: true
2026-01-04T18:36:39.6658042Z ##[endgroup]
2026-01-04T18:36:39.7550097Z Syncing repository: piotrek1372/CA-Racing
2026-01-04T18:36:39.7553140Z ##[group]Getting Git version info
2026-01-04T18:36:39.7554989Z Working directory is '/home/<USER>/work/CA-Racing/CA-Racing'
2026-01-04T18:36:39.7557346Z [command]/usr/bin/git version
2026-01-04T18:36:39.7628371Z git version 2.52.0
2026-01-04T18:36:39.7657391Z ##[endgroup]
2026-01-04T18:36:39.7672849Z Temporarily overriding HOME='/home/<USER>/work/_temp/3bd24369-0524-48c0-99ad-469aa38de989' before making global git config changes
2026-01-04T18:36:39.7676858Z Adding repository directory to the temporary git global config as a safe directory
2026-01-04T18:36:39.7679391Z [command]/usr/bin/git config --global --add safe.directory /home/<USER>/work/CA-Racing/CA-Racing
2026-01-04T18:36:39.7719811Z Deleting the contents of '/home/<USER>/work/CA-Racing/CA-Racing'
2026-01-04T18:36:39.7724278Z ##[group]Initializing the repository
2026-01-04T18:36:39.7726925Z [command]/usr/bin/git init /home/<USER>/work/CA-Racing/CA-Racing
2026-01-04T18:36:39.7845515Z hint: Using 'master' as the name for the initial branch. This default branch name
2026-01-04T18:36:39.7848676Z hint: will change to "main" in Git 3.0. To configure the initial branch name
2026-01-04T18:36:39.7851170Z hint: to use in all of your new repositories, which will suppress this warning,
2026-01-04T18:36:39.7852851Z hint: call:
2026-01-04T18:36:39.7854510Z hint:
2026-01-04T18:36:39.7856547Z hint: 	git config --global init.defaultBranch <name>
2026-01-04T18:36:39.7858623Z hint:
2026-01-04T18:36:39.7860418Z hint: Names commonly chosen instead of 'master' are 'main', 'trunk' and
2026-01-04T18:36:39.7862513Z hint: 'development'. The just-created branch can be renamed via this command:
2026-01-04T18:36:39.7864429Z hint:
2026-01-04T18:36:39.7865462Z hint: 	git branch -m <name>
2026-01-04T18:36:39.7866612Z hint:
2026-01-04T18:36:39.7868073Z hint: Disable this message with "git config set advice.defaultBranchName false"
2026-01-04T18:36:39.7870396Z Initialized empty Git repository in /home/<USER>/work/CA-Racing/CA-Racing/.git/
2026-01-04T18:36:39.7874369Z [command]/usr/bin/git remote add origin https://github.com/piotrek1372/CA-Racing
2026-01-04T18:36:39.7899654Z ##[endgroup]
2026-01-04T18:36:39.7902676Z ##[group]Disabling automatic garbage collection
2026-01-04T18:36:39.7904644Z [command]/usr/bin/git config --local gc.auto 0
2026-01-04T18:36:39.7932428Z ##[endgroup]
2026-01-04T18:36:39.7934281Z ##[group]Setting up auth
2026-01-04T18:36:39.7936443Z [command]/usr/bin/git config --local --name-only --get-regexp core\.sshCommand
2026-01-04T18:36:39.7964862Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'core\.sshCommand' && git config --local --unset-all 'core.sshCommand' || :"
2026-01-04T18:36:39.8306204Z [command]/usr/bin/git config --local --name-only --get-regexp http\.https\:\/\/github\.com\/\.extraheader
2026-01-04T18:36:39.8336741Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'http\.https\:\/\/github\.com\/\.extraheader' && git config --local --unset-all 'http.https://github.com/.extraheader' || :"
2026-01-04T18:36:39.8624857Z [command]/usr/bin/git config --local http.https://github.com/.extraheader AUTHORIZATION: basic ***
2026-01-04T18:36:39.8628999Z ##[endgroup]
2026-01-04T18:36:39.8631006Z ##[group]Fetching the repository
2026-01-04T18:36:39.8634351Z [command]/usr/bin/git -c protocol.version=2 fetch --no-tags --prune --progress --no-recurse-submodules --depth=1 origin +5383261b67be9b47db1749bd930185182f2c7786:refs/remotes/origin/main
2026-01-04T18:36:40.1109422Z remote: Enumerating objects: 146, done.        
2026-01-04T18:36:40.1112996Z remote: Counting objects:   0% (1/146)        
2026-01-04T18:36:40.1115787Z remote: Counting objects:   1% (2/146)        
2026-01-04T18:36:40.1118174Z remote: Counting objects:   2% (3/146)        
2026-01-04T18:36:40.1120565Z remote: Counting objects:   3% (5/146)        
2026-01-04T18:36:40.1123047Z remote: Counting objects:   4% (6/146)        
2026-01-04T18:36:40.1125763Z remote: Counting objects:   5% (8/146)        
2026-01-04T18:36:40.1127798Z remote: Counting objects:   6% (9/146)        
2026-01-04T18:36:40.1129242Z remote: Counting objects:   7% (11/146)        
2026-01-04T18:36:40.1130726Z remote: Counting objects:   8% (12/146)        
2026-01-04T18:36:40.1132186Z remote: Counting objects:   9% (14/146)        
2026-01-04T18:36:40.1133605Z remote: Counting objects:  10% (15/146)        
2026-01-04T18:36:40.1135279Z remote: Counting objects:  11% (17/146)        
2026-01-04T18:36:40.1136725Z remote: Counting objects:  12% (18/146)        
2026-01-04T18:36:40.1138156Z remote: Counting objects:  13% (19/146)        
2026-01-04T18:36:40.1139572Z remote: Counting objects:  14% (21/146)        
2026-01-04T18:36:40.1140998Z remote: Counting objects:  15% (22/146)        
2026-01-04T18:36:40.1142426Z remote: Counting objects:  16% (24/146)        
2026-01-04T18:36:40.1144027Z remote: Counting objects:  17% (25/146)        
2026-01-04T18:36:40.1145460Z remote: Counting objects:  18% (27/146)        
2026-01-04T18:36:40.1146880Z remote: Counting objects:  19% (28/146)        
2026-01-04T18:36:40.1148306Z remote: Counting objects:  20% (30/146)        
2026-01-04T18:36:40.1150050Z remote: Counting objects:  21% (31/146)        
2026-01-04T18:36:40.1151473Z remote: Counting objects:  22% (33/146)        
2026-01-04T18:36:40.1152887Z remote: Counting objects:  23% (34/146)        
2026-01-04T18:36:40.1154519Z remote: Counting objects:  24% (36/146)        
2026-01-04T18:36:40.1155934Z remote: Counting objects:  25% (37/146)        
2026-01-04T18:36:40.1157350Z remote: Counting objects:  26% (38/146)        
2026-01-04T18:36:40.1158778Z remote: Counting objects:  27% (40/146)        
2026-01-04T18:36:40.1160265Z remote: Counting objects:  28% (41/146)        
2026-01-04T18:36:40.1161673Z remote: Counting objects:  29% (43/146)        
2026-01-04T18:36:40.1163085Z remote: Counting objects:  30% (44/146)        
2026-01-04T18:36:40.1164687Z remote: Counting objects:  31% (46/146)        
2026-01-04T18:36:40.1166109Z remote: Counting objects:  32% (47/146)        
2026-01-04T18:36:40.1167522Z remote: Counting objects:  33% (49/146)        
2026-01-04T18:36:40.1168972Z remote: Counting objects:  34% (50/146)        
2026-01-04T18:36:40.1170400Z remote: Counting objects:  35% (52/146)        
2026-01-04T18:36:40.1171833Z remote: Counting objects:  36% (53/146)        
2026-01-04T18:36:40.1173251Z remote: Counting objects:  37% (55/146)        
2026-01-04T18:36:40.1174824Z remote: Counting objects:  38% (56/146)        
2026-01-04T18:36:40.1176234Z remote: Counting objects:  39% (57/146)        
2026-01-04T18:36:40.1177632Z remote: Counting objects:  40% (59/146)        
2026-01-04T18:36:40.1179030Z remote: Counting objects:  41% (60/146)        
2026-01-04T18:36:40.1180442Z remote: Counting objects:  42% (62/146)        
2026-01-04T18:36:40.1181866Z remote: Counting objects:  43% (63/146)        
2026-01-04T18:36:40.1183268Z remote: Counting objects:  44% (65/146)        
2026-01-04T18:36:40.1184860Z remote: Counting objects:  45% (66/146)        
2026-01-04T18:36:40.1186260Z remote: Counting objects:  46% (68/146)        
2026-01-04T18:36:40.1187663Z remote: Counting objects:  47% (69/146)        
2026-01-04T18:36:40.1189244Z remote: Counting objects:  48% (71/146)        
2026-01-04T18:36:40.1190656Z remote: Counting objects:  49% (72/146)        
2026-01-04T18:36:40.1192047Z remote: Counting objects:  50% (73/146)        
2026-01-04T18:36:40.1193436Z remote: Counting objects:  51% (75/146)        
2026-01-04T18:36:40.1195037Z remote: Counting objects:  52% (76/146)        
2026-01-04T18:36:40.1196424Z remote: Counting objects:  53% (78/146)        
2026-01-04T18:36:40.1197811Z remote: Counting objects:  54% (79/146)        
2026-01-04T18:36:40.1199192Z remote: Counting objects:  55% (81/146)        
2026-01-04T18:36:40.1200582Z remote: Counting objects:  56% (82/146)        
2026-01-04T18:36:40.1201974Z remote: Counting objects:  57% (84/146)        
2026-01-04T18:36:40.1236579Z remote: Counting objects:  58% (85/146)        
2026-01-04T18:36:40.1239012Z remote: Counting objects:  59% (87/146)        
2026-01-04T18:36:40.1240604Z remote: Counting objects:  60% (88/146)        
2026-01-04T18:36:40.1242043Z remote: Counting objects:  61% (90/146)        
2026-01-04T18:36:40.1243454Z remote: Counting objects:  62% (91/146)        
2026-01-04T18:36:40.1245042Z remote: Counting objects:  63% (92/146)        
2026-01-04T18:36:40.1246467Z remote: Counting objects:  64% (94/146)        
2026-01-04T18:36:40.1247865Z remote: Counting objects:  65% (95/146)        
2026-01-04T18:36:40.1249261Z remote: Counting objects:  66% (97/146)        
2026-01-04T18:36:40.1250668Z remote: Counting objects:  67% (98/146)        
2026-01-04T18:36:40.1252074Z remote: Counting objects:  68% (100/146)        
2026-01-04T18:36:40.1253471Z remote: Counting objects:  69% (101/146)        
2026-01-04T18:36:40.1254999Z remote: Counting objects:  70% (103/146)        
2026-01-04T18:36:40.1256403Z remote: Counting objects:  71% (104/146)        
2026-01-04T18:36:40.1257793Z remote: Counting objects:  72% (106/146)        
2026-01-04T18:36:40.1259204Z remote: Counting objects:  73% (107/146)        
2026-01-04T18:36:40.1260854Z remote: Counting objects:  74% (109/146)        
2026-01-04T18:36:40.1262285Z remote: Counting objects:  75% (110/146)        
2026-01-04T18:36:40.1263698Z remote: Counting objects:  76% (111/146)        
2026-01-04T18:36:40.1265280Z remote: Counting objects:  77% (113/146)        
2026-01-04T18:36:40.1266689Z remote: Counting objects:  78% (114/146)        
2026-01-04T18:36:40.1268077Z remote: Counting objects:  79% (116/146)        
2026-01-04T18:36:40.1269471Z remote: Counting objects:  80% (117/146)        
2026-01-04T18:36:40.1270869Z remote: Counting objects:  81% (119/146)        
2026-01-04T18:36:40.1272258Z remote: Counting objects:  82% (120/146)        
2026-01-04T18:36:40.1273653Z remote: Counting objects:  83% (122/146)        
2026-01-04T18:36:40.1275219Z remote: Counting objects:  84% (123/146)        
2026-01-04T18:36:40.1276619Z remote: Counting objects:  85% (125/146)        
2026-01-04T18:36:40.1278014Z remote: Counting objects:  86% (126/146)        
2026-01-04T18:36:40.1279422Z remote: Counting objects:  87% (128/146)        
2026-01-04T18:36:40.1280820Z remote: Counting objects:  88% (129/146)        
2026-01-04T18:36:40.1282203Z remote: Counting objects:  89% (130/146)        
2026-01-04T18:36:40.1283596Z remote: Counting objects:  90% (132/146)        
2026-01-04T18:36:40.1285100Z remote: Counting objects:  91% (133/146)        
2026-01-04T18:36:40.1286482Z remote: Counting objects:  92% (135/146)        
2026-01-04T18:36:40.1287887Z remote: Counting objects:  93% (136/146)        
2026-01-04T18:36:40.1289276Z remote: Counting objects:  94% (138/146)        
2026-01-04T18:36:40.1290663Z remote: Counting objects:  95% (139/146)        
2026-01-04T18:36:40.1292047Z remote: Counting objects:  96% (141/146)        
2026-01-04T18:36:40.1293425Z remote: Counting objects:  97% (142/146)        
2026-01-04T18:36:40.1294901Z remote: Counting objects:  98% (144/146)        
2026-01-04T18:36:40.1296287Z remote: Counting objects:  99% (145/146)        
2026-01-04T18:36:40.1297723Z remote: Counting objects: 100% (146/146)        
2026-01-04T18:36:40.1299314Z remote: Counting objects: 100% (146/146), done.        
2026-01-04T18:36:40.1300816Z remote: Compressing objects:   0% (1/137)        
2026-01-04T18:36:40.1302229Z remote: Compressing objects:   1% (2/137)        
2026-01-04T18:36:40.1303649Z remote: Compressing objects:   2% (3/137)        
2026-01-04T18:36:40.1305165Z remote: Compressing objects:   3% (5/137)        
2026-01-04T18:36:40.1306584Z remote: Compressing objects:   4% (6/137)        
2026-01-04T18:36:40.1308012Z remote: Compressing objects:   5% (7/137)        
2026-01-04T18:36:40.1309434Z remote: Compressing objects:   6% (9/137)        
2026-01-04T18:36:40.1310852Z remote: Compressing objects:   7% (10/137)        
2026-01-04T18:36:40.1312268Z remote: Compressing objects:   8% (11/137)        
2026-01-04T18:36:40.1313689Z remote: Compressing objects:   9% (13/137)        
2026-01-04T18:36:40.1315213Z remote: Compressing objects:  10% (14/137)        
2026-01-04T18:36:40.1316655Z remote: Compressing objects:  11% (16/137)        
2026-01-04T18:36:40.1318097Z remote: Compressing objects:  12% (17/137)        
2026-01-04T18:36:40.1319534Z remote: Compressing objects:  13% (18/137)        
2026-01-04T18:36:40.1320955Z remote: Compressing objects:  14% (20/137)        
2026-01-04T18:36:40.1322375Z remote: Compressing objects:  15% (21/137)        
2026-01-04T18:36:40.1323874Z remote: Compressing objects:  16% (22/137)        
2026-01-04T18:36:40.1325305Z remote: Compressing objects:  17% (24/137)        
2026-01-04T18:36:40.1326723Z remote: Compressing objects:  18% (25/137)        
2026-01-04T18:36:40.1328133Z remote: Compressing objects:  19% (27/137)        
2026-01-04T18:36:40.1329545Z remote: Compressing objects:  20% (28/137)        
2026-01-04T18:36:40.1330965Z remote: Compressing objects:  21% (29/137)        
2026-01-04T18:36:40.1332376Z remote: Compressing objects:  22% (31/137)        
2026-01-04T18:36:40.1333895Z remote: Compressing objects:  23% (32/137)        
2026-01-04T18:36:40.1335456Z remote: Compressing objects:  24% (33/137)        
2026-01-04T18:36:40.1336893Z remote: Compressing objects:  25% (35/137)        
2026-01-04T18:36:40.1338313Z remote: Compressing objects:  26% (36/137)        
2026-01-04T18:36:40.1339744Z remote: Compressing objects:  27% (37/137)        
2026-01-04T18:36:40.1341160Z remote: Compressing objects:  28% (39/137)        
2026-01-04T18:36:40.1342588Z remote: Compressing objects:  29% (40/137)        
2026-01-04T18:36:40.1344094Z remote: Compressing objects:  30% (42/137)        
2026-01-04T18:36:40.1345520Z remote: Compressing objects:  31% (43/137)        
2026-01-04T18:36:40.1346933Z remote: Compressing objects:  32% (44/137)        
2026-01-04T18:36:40.1348353Z remote: Compressing objects:  33% (46/137)        
2026-01-04T18:36:40.1349765Z remote: Compressing objects:  34% (47/137)        
2026-01-04T18:36:40.1351189Z remote: Compressing objects:  35% (48/137)        
2026-01-04T18:36:40.1352608Z remote: Compressing objects:  36% (50/137)        
2026-01-04T18:36:40.1354115Z remote: Compressing objects:  37% (51/137)        
2026-01-04T18:36:40.1355549Z remote: Compressing objects:  38% (53/137)        
2026-01-04T18:36:40.1356982Z remote: Compressing objects:  39% (54/137)        
2026-01-04T18:36:40.1358396Z remote: Compressing objects:  40% (55/137)        
2026-01-04T18:36:40.1359820Z remote: Compressing objects:  41% (57/137)        
2026-01-04T18:36:40.1361239Z remote: Compressing objects:  42% (58/137)        
2026-01-04T18:36:40.1362702Z remote: Compressing objects:  43% (59/137)        
2026-01-04T18:36:40.1364199Z remote: Compressing objects:  44% (61/137)        
2026-01-04T18:36:40.1365618Z remote: Compressing objects:  45% (62/137)        
2026-01-04T18:36:40.1367048Z remote: Compressing objects:  46% (64/137)        
2026-01-04T18:36:40.1368461Z remote: Compressing objects:  47% (65/137)        
2026-01-04T18:36:40.1369880Z remote: Compressing objects:  48% (66/137)        
2026-01-04T18:36:40.1371312Z remote: Compressing objects:  49% (68/137)        
2026-01-04T18:36:40.1372860Z remote: Compressing objects:  50% (69/137)        
2026-01-04T18:36:40.1374385Z remote: Compressing objects:  51% (70/137)        
2026-01-04T18:36:40.1375799Z remote: Compressing objects:  52% (72/137)        
2026-01-04T18:36:40.1378795Z remote: Compressing objects:  53% (73/137)        
2026-01-04T18:36:40.1381587Z remote: Compressing objects:  54% (74/137)        
2026-01-04T18:36:40.1384075Z remote: Compressing objects:  55% (76/137)        
2026-01-04T18:36:40.1385590Z remote: Compressing objects:  56% (77/137)        
2026-01-04T18:36:40.1387036Z remote: Compressing objects:  57% (79/137)        
2026-01-04T18:36:40.1388463Z remote: Compressing objects:  58% (80/137)        
2026-01-04T18:36:40.1390090Z remote: Compressing objects:  59% (81/137)        
2026-01-04T18:36:40.1391514Z remote: Compressing objects:  60% (83/137)        
2026-01-04T18:36:40.1392943Z remote: Compressing objects:  61% (84/137)        
2026-01-04T18:36:40.1394661Z remote: Compressing objects:  62% (85/137)        
2026-01-04T18:36:40.1396100Z remote: Compressing objects:  63% (87/137)        
2026-01-04T18:36:40.1397545Z remote: Compressing objects:  64% (88/137)        
2026-01-04T18:36:40.1398960Z remote: Compressing objects:  65% (90/137)        
2026-01-04T18:36:40.1400367Z remote: Compressing objects:  66% (91/137)        
2026-01-04T18:36:40.1401776Z remote: Compressing objects:  67% (92/137)        
2026-01-04T18:36:40.1403184Z remote: Compressing objects:  68% (94/137)        
2026-01-04T18:36:40.1404805Z remote: Compressing objects:  69% (95/137)        
2026-01-04T18:36:40.1406227Z remote: Compressing objects:  70% (96/137)        
2026-01-04T18:36:40.1407632Z remote: Compressing objects:  71% (98/137)        
2026-01-04T18:36:40.1409033Z remote: Compressing objects:  72% (99/137)        
2026-01-04T18:36:40.1410446Z remote: Compressing objects:  73% (101/137)        
2026-01-04T18:36:40.1411865Z remote: Compressing objects:  74% (102/137)        
2026-01-04T18:36:40.1413964Z remote: Compressing objects:  75% (103/137)        
2026-01-04T18:36:40.1416288Z remote: Compressing objects:  76% (105/137)        
2026-01-04T18:36:40.1417756Z remote: Compressing objects:  77% (106/137)        
2026-01-04T18:36:40.1419191Z remote: Compressing objects:  78% (107/137)        
2026-01-04T18:36:40.1420678Z remote: Compressing objects:  79% (109/137)        
2026-01-04T18:36:40.1422117Z remote: Compressing objects:  80% (110/137)        
2026-01-04T18:36:40.1423525Z remote: Compressing objects:  81% (111/137)        
2026-01-04T18:36:40.1425225Z remote: Compressing objects:  82% (113/137)        
2026-01-04T18:36:40.1426643Z remote: Compressing objects:  83% (114/137)        
2026-01-04T18:36:40.1428060Z remote: Compressing objects:  84% (116/137)        
2026-01-04T18:36:40.1429484Z remote: Compressing objects:  85% (117/137)        
2026-01-04T18:36:40.1430911Z remote: Compressing objects:  86% (118/137)        
2026-01-04T18:36:40.1432312Z remote: Compressing objects:  87% (120/137)        
2026-01-04T18:36:40.1433934Z remote: Compressing objects:  88% (121/137)        
2026-01-04T18:36:40.1435394Z remote: Compressing objects:  89% (122/137)        
2026-01-04T18:36:40.1436841Z remote: Compressing objects:  90% (124/137)        
2026-01-04T18:36:40.1438250Z remote: Compressing objects:  91% (125/137)        
2026-01-04T18:36:40.1439649Z remote: Compressing objects:  92% (127/137)        
2026-01-04T18:36:40.1441072Z remote: Compressing objects:  93% (128/137)        
2026-01-04T18:36:40.1442492Z remote: Compressing objects:  94% (129/137)        
2026-01-04T18:36:40.1444080Z remote: Compressing objects:  95% (131/137)        
2026-01-04T18:36:40.1445505Z remote: Compressing objects:  96% (132/137)        
2026-01-04T18:36:40.1446924Z remote: Compressing objects:  97% (133/137)        
2026-01-04T18:36:40.1448323Z remote: Compressing objects:  98% (135/137)        
2026-01-04T18:36:40.1449738Z remote: Compressing objects:  99% (136/137)        
2026-01-04T18:36:40.1451170Z remote: Compressing objects: 100% (137/137)        
2026-01-04T18:36:40.1452834Z remote: Compressing objects: 100% (137/137), done.        
2026-01-04T18:36:40.1454466Z Receiving objects:   0% (1/146)
2026-01-04T18:36:40.1455602Z Receiving objects:   1% (2/146)
2026-01-04T18:36:40.1456746Z Receiving objects:   2% (3/146)
2026-01-04T18:36:40.1457895Z Receiving objects:   3% (5/146)
2026-01-04T18:36:40.1459022Z Receiving objects:   4% (6/146)
2026-01-04T18:36:40.1515456Z Receiving objects:   5% (8/146)
2026-01-04T18:36:40.1516997Z Receiving objects:   6% (9/146)
2026-01-04T18:36:40.1518424Z Receiving objects:   7% (11/146)
2026-01-04T18:36:40.1662408Z Receiving objects:   8% (12/146)
2026-01-04T18:36:40.1664751Z Receiving objects:   9% (14/146)
2026-01-04T18:36:40.1666187Z Receiving objects:  10% (15/146)
2026-01-04T18:36:40.1667381Z Receiving objects:  11% (17/146)
2026-01-04T18:36:40.1668552Z Receiving objects:  12% (18/146)
2026-01-04T18:36:40.1792280Z Receiving objects:  13% (19/146)
2026-01-04T18:36:40.2830004Z Receiving objects:  14% (21/146)
2026-01-04T18:36:40.2832840Z Receiving objects:  15% (22/146)
2026-01-04T18:36:40.2837058Z Receiving objects:  16% (24/146)
2026-01-04T18:36:40.2840370Z Receiving objects:  17% (25/146)
2026-01-04T18:36:40.3631227Z Receiving objects:  18% (27/146)
2026-01-04T18:36:40.4691839Z Receiving objects:  19% (28/146)
2026-01-04T18:36:40.4694209Z Receiving objects:  20% (30/146)
2026-01-04T18:36:40.4816639Z Receiving objects:  21% (31/146)
2026-01-04T18:36:40.4884299Z Receiving objects:  22% (33/146)
2026-01-04T18:36:40.5448620Z Receiving objects:  23% (34/146)
2026-01-04T18:36:40.5451495Z Receiving objects:  24% (36/146)
2026-01-04T18:36:40.5453996Z Receiving objects:  25% (37/146)
2026-01-04T18:36:40.5457133Z Receiving objects:  26% (38/146)
2026-01-04T18:36:40.5458870Z Receiving objects:  27% (40/146)
2026-01-04T18:36:40.5460815Z Receiving objects:  28% (41/146)
2026-01-04T18:36:40.5463024Z Receiving objects:  29% (43/146)
2026-01-04T18:36:40.5465338Z Receiving objects:  30% (44/146)
2026-01-04T18:36:40.5469129Z Receiving objects:  31% (46/146)
2026-01-04T18:36:40.5471913Z Receiving objects:  32% (47/146)
2026-01-04T18:36:40.5475353Z Receiving objects:  33% (49/146)
2026-01-04T18:36:40.5527553Z Receiving objects:  34% (50/146)
2026-01-04T18:36:40.5551827Z Receiving objects:  35% (52/146)
2026-01-04T18:36:40.5598344Z Receiving objects:  36% (53/146)
2026-01-04T18:36:40.6095336Z Receiving objects:  37% (55/146)
2026-01-04T18:36:40.6418950Z Receiving objects:  38% (56/146)
2026-01-04T18:36:40.7282451Z Receiving objects:  39% (57/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.7624362Z Receiving objects:  40% (59/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.8456826Z Receiving objects:  41% (60/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.8954418Z Receiving objects:  42% (62/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9080647Z Receiving objects:  43% (63/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9153068Z Receiving objects:  44% (65/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9222549Z Receiving objects:  45% (66/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9226428Z Receiving objects:  46% (68/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9227536Z Receiving objects:  47% (69/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9231702Z Receiving objects:  48% (71/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9232518Z Receiving objects:  49% (72/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9233310Z Receiving objects:  50% (73/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9234274Z Receiving objects:  51% (75/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9235046Z Receiving objects:  52% (76/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9235813Z Receiving objects:  53% (78/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9236584Z Receiving objects:  54% (79/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9237354Z Receiving objects:  55% (81/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9238117Z Receiving objects:  56% (82/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9238897Z Receiving objects:  57% (84/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9242785Z Receiving objects:  58% (85/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9244750Z Receiving objects:  59% (87/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9246607Z Receiving objects:  60% (88/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9247609Z Receiving objects:  61% (90/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9248539Z Receiving objects:  62% (91/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9249437Z Receiving objects:  63% (92/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9250369Z Receiving objects:  64% (94/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9252169Z Receiving objects:  65% (95/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9253041Z Receiving objects:  66% (97/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9254088Z Receiving objects:  67% (98/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9254897Z Receiving objects:  68% (100/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9255747Z Receiving objects:  69% (101/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9256534Z Receiving objects:  70% (103/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9257353Z Receiving objects:  71% (104/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9258119Z Receiving objects:  72% (106/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9258949Z Receiving objects:  73% (107/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9259729Z Receiving objects:  74% (109/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9260503Z Receiving objects:  75% (110/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9261289Z Receiving objects:  76% (111/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9262075Z Receiving objects:  77% (113/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9262862Z Receiving objects:  78% (114/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9263625Z Receiving objects:  79% (116/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9265063Z Receiving objects:  80% (117/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9265862Z Receiving objects:  81% (119/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9266601Z Receiving objects:  82% (120/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9267288Z Receiving objects:  83% (122/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9267969Z Receiving objects:  84% (123/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9268653Z Receiving objects:  85% (125/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9269512Z remote: Total 146 (delta 7), reused 96 (delta 3), pack-reused 0 (from 0)        
2026-01-04T18:36:40.9270322Z Receiving objects:  86% (126/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9271085Z Receiving objects:  87% (128/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9271845Z Receiving objects:  88% (129/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9272573Z Receiving objects:  89% (130/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9273309Z Receiving objects:  90% (132/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9274307Z Receiving objects:  91% (133/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9275034Z Receiving objects:  92% (135/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9275753Z Receiving objects:  93% (136/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9276483Z Receiving objects:  94% (138/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9277193Z Receiving objects:  95% (139/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9277892Z Receiving objects:  96% (141/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9278644Z Receiving objects:  97% (142/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9279338Z Receiving objects:  98% (144/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9280045Z Receiving objects:  99% (145/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9280754Z Receiving objects: 100% (146/146), 23.09 MiB | 46.17 MiB/s
2026-01-04T18:36:40.9281496Z Receiving objects: 100% (146/146), 40.06 MiB | 50.96 MiB/s, done.
2026-01-04T18:36:40.9282180Z Resolving deltas:   0% (0/7)
2026-01-04T18:36:40.9282726Z Resolving deltas:  14% (1/7)
2026-01-04T18:36:40.9283447Z Resolving deltas:  28% (2/7)
2026-01-04T18:36:40.9284211Z Resolving deltas:  42% (3/7)
2026-01-04T18:36:40.9284685Z Resolving deltas:  57% (4/7)
2026-01-04T18:36:40.9285173Z Resolving deltas:  71% (5/7)
2026-01-04T18:36:40.9285668Z Resolving deltas:  85% (6/7)
2026-01-04T18:36:40.9286164Z Resolving deltas: 100% (7/7)
2026-01-04T18:36:40.9286677Z Resolving deltas: 100% (7/7), done.
2026-01-04T18:36:40.9526189Z From https://github.com/piotrek1372/CA-Racing
2026-01-04T18:36:40.9527040Z  * [new ref]         5383261b67be9b47db1749bd930185182f2c7786 -> origin/main
2026-01-04T18:36:40.9559567Z ##[endgroup]
2026-01-04T18:36:40.9560157Z ##[group]Determining the checkout info
2026-01-04T18:36:40.9562184Z ##[endgroup]
2026-01-04T18:36:40.9562769Z ##[group]Checking out the ref
2026-01-04T18:36:40.9566675Z [command]/usr/bin/git checkout --progress --force -B main refs/remotes/origin/main
2026-01-04T18:36:41.1563229Z Switched to a new branch 'main'
2026-01-04T18:36:41.1565293Z branch 'main' set up to track 'origin/main'.
2026-01-04T18:36:41.1587677Z ##[endgroup]
2026-01-04T18:36:41.1624046Z [command]/usr/bin/git log -1 --format='%H'
2026-01-04T18:36:41.1647197Z '5383261b67be9b47db1749bd930185182f2c7786'
2026-01-04T18:36:41.1870629Z ##[group]Run actions/setup-python@v4
2026-01-04T18:36:41.1870977Z with:
2026-01-04T18:36:41.1871212Z   python-version: 3.10
2026-01-04T18:36:41.1871482Z   check-latest: false
2026-01-04T18:36:41.1871852Z   token: ***
2026-01-04T18:36:41.1872099Z   update-environment: true
2026-01-04T18:36:41.1872380Z   allow-prereleases: false
2026-01-04T18:36:41.1872648Z ##[endgroup]
2026-01-04T18:36:41.3579047Z ##[group]Installed versions
2026-01-04T18:36:41.3694671Z Successfully set up CPython (3.10.19)
2026-01-04T18:36:41.3695966Z ##[endgroup]
2026-01-04T18:36:41.3834689Z ##[group]Run sudo apt-get update
2026-01-04T18:36:41.3835129Z [36;1msudo apt-get update[0m
2026-01-04T18:36:41.3835696Z [36;1msudo apt-get install -y rpm dpkg-dev createrepo-c[0m
2026-01-04T18:36:41.3836106Z [36;1mpip install -r requirements.txt[0m
2026-01-04T18:36:41.3836436Z [36;1mpip install pyinstaller[0m
2026-01-04T18:36:41.3885826Z shell: /usr/bin/bash -e {0}
2026-01-04T18:36:41.3886130Z env:
2026-01-04T18:36:41.3886444Z   pythonLocation: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:36:41.3886929Z   PKG_CONFIG_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib/pkgconfig
2026-01-04T18:36:41.3887384Z   Python_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:36:41.3887790Z   Python2_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:36:41.3888194Z   Python3_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:36:41.3888590Z   LD_LIBRARY_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib
2026-01-04T18:36:41.3888999Z ##[endgroup]
2026-01-04T18:36:41.4686176Z Get:1 file:/etc/apt/apt-mirrors.txt Mirrorlist [144 B]
2026-01-04T18:36:41.5028146Z Hit:2 http://azure.archive.ubuntu.com/ubuntu noble InRelease
2026-01-04T18:36:41.5034732Z Hit:6 https://packages.microsoft.com/repos/azure-cli noble InRelease
2026-01-04T18:36:41.5041321Z Get:7 https://packages.microsoft.com/ubuntu/24.04/prod noble InRelease [3600 B]
2026-01-04T18:36:41.5059859Z Get:3 http://azure.archive.ubuntu.com/ubuntu noble-updates InRelease [126 kB]
2026-01-04T18:36:41.5117616Z Get:4 http://azure.archive.ubuntu.com/ubuntu noble-backports InRelease [126 kB]
2026-01-04T18:36:41.5172368Z Get:5 http://azure.archive.ubuntu.com/ubuntu noble-security InRelease [126 kB]
2026-01-04T18:36:41.6865158Z Get:8 https://packages.microsoft.com/ubuntu/24.04/prod noble/main amd64 Packages [77.5 kB]
2026-01-04T18:36:41.6982376Z Get:9 https://packages.microsoft.com/ubuntu/24.04/prod noble/main armhf Packages [11.4 kB]
2026-01-04T18:36:41.7028331Z Get:10 https://packages.microsoft.com/ubuntu/24.04/prod noble/main arm64 Packages [59.2 kB]
2026-01-04T18:36:41.7274434Z Get:11 http://azure.archive.ubuntu.com/ubuntu noble-updates/main amd64 Packages [1684 kB]
2026-01-04T18:36:41.7387962Z Get:12 http://azure.archive.ubuntu.com/ubuntu noble-updates/main Translation-en [311 kB]
2026-01-04T18:36:41.7420181Z Get:13 http://azure.archive.ubuntu.com/ubuntu noble-updates/main amd64 Components [175 kB]
2026-01-04T18:36:41.7448445Z Get:14 http://azure.archive.ubuntu.com/ubuntu noble-updates/main amd64 c-n-f Metadata [15.8 kB]
2026-01-04T18:36:41.7456619Z Get:15 http://azure.archive.ubuntu.com/ubuntu noble-updates/universe amd64 Packages [1506 kB]
2026-01-04T18:36:41.7625947Z Get:16 http://azure.archive.ubuntu.com/ubuntu noble-updates/universe Translation-en [306 kB]
2026-01-04T18:36:41.7668062Z Get:17 http://azure.archive.ubuntu.com/ubuntu noble-updates/universe amd64 Components [378 kB]
2026-01-04T18:36:41.7704828Z Get:18 http://azure.archive.ubuntu.com/ubuntu noble-updates/universe amd64 c-n-f Metadata [31.4 kB]
2026-01-04T18:36:41.7719612Z Get:19 http://azure.archive.ubuntu.com/ubuntu noble-updates/restricted amd64 Packages [2413 kB]
2026-01-04T18:36:41.7906700Z Get:20 http://azure.archive.ubuntu.com/ubuntu noble-updates/restricted Translation-en [550 kB]
2026-01-04T18:36:41.8395327Z Get:21 http://azure.archive.ubuntu.com/ubuntu noble-updates/restricted amd64 Components [212 B]
2026-01-04T18:36:41.8411289Z Get:22 http://azure.archive.ubuntu.com/ubuntu noble-updates/restricted amd64 c-n-f Metadata [516 B]
2026-01-04T18:36:41.8425329Z Get:23 http://azure.archive.ubuntu.com/ubuntu noble-updates/multiverse amd64 Packages [30.3 kB]
2026-01-04T18:36:41.8435957Z Get:24 http://azure.archive.ubuntu.com/ubuntu noble-updates/multiverse Translation-en [6048 B]
2026-01-04T18:36:41.8449886Z Get:25 http://azure.archive.ubuntu.com/ubuntu noble-updates/multiverse amd64 Components [940 B]
2026-01-04T18:36:41.8477841Z Get:26 http://azure.archive.ubuntu.com/ubuntu noble-updates/multiverse amd64 c-n-f Metadata [488 B]
2026-01-04T18:36:41.8496063Z Get:27 http://azure.archive.ubuntu.com/ubuntu noble-backports/main amd64 Packages [40.4 kB]
2026-01-04T18:36:41.8505712Z Get:28 http://azure.archive.ubuntu.com/ubuntu noble-backports/main amd64 Components [7316 B]
2026-01-04T18:36:41.8515893Z Get:29 http://azure.archive.ubuntu.com/ubuntu noble-backports/main amd64 c-n-f Metadata [368 B]
2026-01-04T18:36:41.8530303Z Get:30 http://azure.archive.ubuntu.com/ubuntu noble-backports/universe amd64 Packages [29.5 kB]
2026-01-04T18:36:41.8543180Z Get:31 http://azure.archive.ubuntu.com/ubuntu noble-backports/universe Translation-en [17.9 kB]
2026-01-04T18:36:41.8621951Z Get:32 http://azure.archive.ubuntu.com/ubuntu noble-backports/universe amd64 Components [10.5 kB]
2026-01-04T18:36:41.8627598Z Get:33 http://azure.archive.ubuntu.com/ubuntu noble-backports/universe amd64 c-n-f Metadata [1444 B]
2026-01-04T18:36:41.8628868Z Get:34 http://azure.archive.ubuntu.com/ubuntu noble-backports/restricted amd64 Components [212 B]
2026-01-04T18:36:41.9046450Z Get:35 http://azure.archive.ubuntu.com/ubuntu noble-backports/multiverse amd64 Components [212 B]
2026-01-04T18:36:41.9063156Z Get:36 http://azure.archive.ubuntu.com/ubuntu noble-security/main Translation-en [225 kB]
2026-01-04T18:36:41.9088649Z Get:37 http://azure.archive.ubuntu.com/ubuntu noble-security/main amd64 Components [21.5 kB]
2026-01-04T18:36:41.9103212Z Get:38 http://azure.archive.ubuntu.com/ubuntu noble-security/main amd64 c-n-f Metadata [9504 B]
2026-01-04T18:36:41.9116797Z Get:39 http://azure.archive.ubuntu.com/ubuntu noble-security/universe amd64 Components [71.4 kB]
2026-01-04T18:36:41.9132106Z Get:40 http://azure.archive.ubuntu.com/ubuntu noble-security/restricted amd64 Components [208 B]
2026-01-04T18:36:41.9147107Z Get:41 http://azure.archive.ubuntu.com/ubuntu noble-security/multiverse amd64 Components [208 B]
2026-01-04T18:36:50.9930924Z Fetched 8375 kB in 1s (7220 kB/s)
2026-01-04T18:36:51.8051174Z Reading package lists...
2026-01-04T18:36:51.8378206Z Reading package lists...
2026-01-04T18:36:52.0570120Z Building dependency tree...
2026-01-04T18:36:52.0577742Z Reading state information...
2026-01-04T18:36:52.2835915Z rpm is already the newest version (4.18.2+dfsg-2.1build2).
2026-01-04T18:36:52.2836972Z dpkg-dev is already the newest version (1.22.6ubuntu6.5).
2026-01-04T18:36:52.2837826Z The following additional packages will be installed:
2026-01-04T18:36:52.2842475Z   libcreaterepo-c0 libdrpm0 libmodulemd2 libzck1
2026-01-04T18:36:52.3059930Z The following NEW packages will be installed:
2026-01-04T18:36:52.3070439Z   createrepo-c libcreaterepo-c0 libdrpm0 libmodulemd2 libzck1
2026-01-04T18:36:52.3279028Z 0 upgraded, 5 newly installed, 0 to remove and 69 not upgraded.
2026-01-04T18:36:52.3279728Z Need to get 388 kB of archives.
2026-01-04T18:36:52.3280340Z After this operation, 1241 kB of additional disk space will be used.
2026-01-04T18:36:52.3281083Z Get:1 file:/etc/apt/apt-mirrors.txt Mirrorlist [144 B]
2026-01-04T18:36:52.3784573Z Get:2 http://azure.archive.ubuntu.com/ubuntu noble/universe amd64 libdrpm0 amd64 0.5.1-1.1build1 [47.6 kB]
2026-01-04T18:36:52.4125974Z Get:3 http://azure.archive.ubuntu.com/ubuntu noble/universe amd64 libmodulemd2 amd64 2.14.0-3build2 [144 kB]
2026-01-04T18:36:52.4464437Z Get:4 http://azure.archive.ubuntu.com/ubuntu noble/universe amd64 libzck1 amd64 1.3.2+ds1-1build2 [50.2 kB]
2026-01-04T18:36:52.4781597Z Get:5 http://azure.archive.ubuntu.com/ubuntu noble/universe amd64 libcreaterepo-c0 amd64 0.17.3-2ubuntu3 [93.5 kB]
2026-01-04T18:36:52.5109134Z Get:6 http://azure.archive.ubuntu.com/ubuntu noble/universe amd64 createrepo-c amd64 0.17.3-2ubuntu3 [52.6 kB]
2026-01-04T18:36:52.7924426Z Fetched 388 kB in 0s (1975 kB/s)
2026-01-04T18:36:52.8183621Z Selecting previously unselected package libdrpm0:amd64.
2026-01-04T18:36:52.8473916Z (Reading database ... 
2026-01-04T18:36:52.8474445Z (Reading database ... 5%
2026-01-04T18:36:52.8474877Z (Reading database ... 10%
2026-01-04T18:36:52.8475311Z (Reading database ... 15%
2026-01-04T18:36:52.8475764Z (Reading database ... 20%
2026-01-04T18:36:52.8476741Z (Reading database ... 25%
2026-01-04T18:36:52.8477273Z (Reading database ... 30%
2026-01-04T18:36:52.8478222Z (Reading database ... 35%
2026-01-04T18:36:52.8478744Z (Reading database ... 40%
2026-01-04T18:36:52.8479173Z (Reading database ... 45%
2026-01-04T18:36:52.8479628Z (Reading database ... 50%
2026-01-04T18:36:52.8579934Z (Reading database ... 55%
2026-01-04T18:36:53.0154744Z (Reading database ... 60%
2026-01-04T18:36:53.0950612Z (Reading database ... 65%
2026-01-04T18:36:53.1290008Z (Reading database ... 70%
2026-01-04T18:36:53.2247186Z (Reading database ... 75%
2026-01-04T18:36:53.3714604Z (Reading database ... 80%
2026-01-04T18:36:53.4886440Z (Reading database ... 85%
2026-01-04T18:36:53.6401074Z (Reading database ... 90%
2026-01-04T18:36:53.7693150Z (Reading database ... 95%
2026-01-04T18:36:53.7694009Z (Reading database ... 100%
2026-01-04T18:36:53.7694780Z (Reading database ... 217374 files and directories currently installed.)
2026-01-04T18:36:53.7740493Z Preparing to unpack .../libdrpm0_0.5.1-1.1build1_amd64.deb ...
2026-01-04T18:36:53.7795079Z Unpacking libdrpm0:amd64 (0.5.1-1.1build1) ...
2026-01-04T18:36:53.8057670Z Selecting previously unselected package libmodulemd2:amd64.
2026-01-04T18:36:53.8236370Z Preparing to unpack .../libmodulemd2_2.14.0-3build2_amd64.deb ...
2026-01-04T18:36:53.8250577Z Unpacking libmodulemd2:amd64 (2.14.0-3build2) ...
2026-01-04T18:36:53.8528948Z Selecting previously unselected package libzck1:amd64.
2026-01-04T18:36:53.8662496Z Preparing to unpack .../libzck1_1.3.2+ds1-1build2_amd64.deb ...
2026-01-04T18:36:53.8682721Z Unpacking libzck1:amd64 (1.3.2+ds1-1build2) ...
2026-01-04T18:36:53.8938102Z Selecting previously unselected package libcreaterepo-c0:amd64.
2026-01-04T18:36:53.9073118Z Preparing to unpack .../libcreaterepo-c0_0.17.3-2ubuntu3_amd64.deb ...
2026-01-04T18:36:53.9084364Z Unpacking libcreaterepo-c0:amd64 (0.17.3-2ubuntu3) ...
2026-01-04T18:36:53.9336795Z Selecting previously unselected package createrepo-c.
2026-01-04T18:36:53.9483468Z Preparing to unpack .../createrepo-c_0.17.3-2ubuntu3_amd64.deb ...
2026-01-04T18:36:53.9497230Z Unpacking createrepo-c (0.17.3-2ubuntu3) ...
2026-01-04T18:36:54.0006528Z Setting up libmodulemd2:amd64 (2.14.0-3build2) ...
2026-01-04T18:36:54.0038419Z Setting up libzck1:amd64 (1.3.2+ds1-1build2) ...
2026-01-04T18:36:54.0074331Z Setting up libdrpm0:amd64 (0.5.1-1.1build1) ...
2026-01-04T18:36:54.0103381Z Setting up libcreaterepo-c0:amd64 (0.17.3-2ubuntu3) ...
2026-01-04T18:36:54.0135204Z Setting up createrepo-c (0.17.3-2ubuntu3) ...
2026-01-04T18:36:54.0171092Z Processing triggers for man-db (2.12.0-4build2) ...
2026-01-04T18:36:54.0203348Z Not building database; man-db/auto-update is not 'true'.
2026-01-04T18:36:54.0224312Z Processing triggers for libc-bin (2.39-0ubuntu8.6) ...
2026-01-04T18:36:54.7102685Z 
2026-01-04T18:36:54.7103548Z Running kernel seems to be up-to-date.
2026-01-04T18:36:54.7104092Z 
2026-01-04T18:36:54.7104250Z No services need to be restarted.
2026-01-04T18:36:54.7104467Z 
2026-01-04T18:36:54.7104635Z No containers need to be restarted.
2026-01-04T18:36:54.7104865Z 
2026-01-04T18:36:54.7105018Z No user sessions are running outdated binaries.
2026-01-04T18:36:54.7105281Z 
2026-01-04T18:36:54.7105514Z No VM guests are running outdated hypervisor (qemu) binaries on this host.
2026-01-04T18:36:56.9227265Z Collecting pygame (from -r requirements.txt (line 1))
2026-01-04T18:36:57.0205428Z   Downloading pygame-2.6.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (12 kB)
2026-01-04T18:36:57.0979332Z Collecting pyinstaller (from -r requirements.txt (line 2))
2026-01-04T18:36:57.1194440Z   Downloading pyinstaller-6.17.0-py3-none-manylinux2014_x86_64.whl.metadata (8.5 kB)
2026-01-04T18:36:57.4157670Z Collecting numpy (from -r requirements.txt (line 3))
2026-01-04T18:36:57.4333424Z   Downloading numpy-2.2.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (62 kB)
2026-01-04T18:36:57.7295229Z Collecting Pillow (from -r requirements.txt (line 4))
2026-01-04T18:36:57.7470989Z   Downloading pillow-12.1.0-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl.metadata (8.8 kB)
2026-01-04T18:36:57.7837754Z Collecting altgraph (from pyinstaller->-r requirements.txt (line 2))
2026-01-04T18:36:57.8010221Z   Downloading altgraph-0.17.5-py2.py3-none-any.whl.metadata (7.5 kB)
2026-01-04T18:36:57.8351755Z Collecting packaging>=22.0 (from pyinstaller->-r requirements.txt (line 2))
2026-01-04T18:36:57.8520526Z   Downloading packaging-25.0-py3-none-any.whl.metadata (3.3 kB)
2026-01-04T18:36:57.8882666Z Collecting pyinstaller-hooks-contrib>=2025.9 (from pyinstaller->-r requirements.txt (line 2))
2026-01-04T18:36:57.9062766Z   Downloading pyinstaller_hooks_contrib-2025.11-py3-none-any.whl.metadata (16 kB)
2026-01-04T18:36:57.9119887Z Requirement already satisfied: setuptools>=42.0.0 in /opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages (from pyinstaller->-r requirements.txt (line 2)) (79.0.1)
2026-01-04T18:36:57.9488712Z Downloading pygame-2.6.1-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (14.0 MB)
2026-01-04T18:36:58.0904470Z    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 14.0/14.0 MB 115.1 MB/s  0:00:00
2026-01-04T18:36:58.1091687Z Downloading pyinstaller-6.17.0-py3-none-manylinux2014_x86_64.whl (737 kB)
2026-01-04T18:36:58.1205548Z    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 737.8/737.8 kB 56.8 MB/s  0:00:00
2026-01-04T18:36:58.1376400Z Downloading numpy-2.2.6-cp310-cp310-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (16.8 MB)
2026-01-04T18:36:58.2199452Z    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 16.8/16.8 MB 208.2 MB/s  0:00:00
2026-01-04T18:36:58.2377504Z Downloading pillow-12.1.0-cp310-cp310-manylinux_2_27_x86_64.manylinux_2_28_x86_64.whl (7.0 MB)
2026-01-04T18:36:58.2761311Z    ━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━ 7.0/7.0 MB 190.7 MB/s  0:00:00
2026-01-04T18:36:58.2932329Z Downloading packaging-25.0-py3-none-any.whl (66 kB)
2026-01-04T18:36:58.3138189Z Downloading pyinstaller_hooks_contrib-2025.11-py3-none-any.whl (449 kB)
2026-01-04T18:36:58.3392366Z Downloading altgraph-0.17.5-py2.py3-none-any.whl (21 kB)
2026-01-04T18:36:58.4299117Z Installing collected packages: altgraph, pygame, Pillow, packaging, numpy, pyinstaller-hooks-contrib, pyinstaller
2026-01-04T18:37:02.0806321Z 
2026-01-04T18:37:02.0827334Z Successfully installed Pillow-12.1.0 altgraph-0.17.5 numpy-2.2.6 packaging-25.0 pygame-2.6.1 pyinstaller-6.17.0 pyinstaller-hooks-contrib-2025.11
2026-01-04T18:37:02.6753380Z Requirement already satisfied: pyinstaller in /opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages (6.17.0)
2026-01-04T18:37:02.6782724Z Requirement already satisfied: altgraph in /opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages (from pyinstaller) (0.17.5)
2026-01-04T18:37:02.6788662Z Requirement already satisfied: packaging>=22.0 in /opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages (from pyinstaller) (25.0)
2026-01-04T18:37:02.6794130Z Requirement already satisfied: pyinstaller-hooks-contrib>=2025.9 in /opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages (from pyinstaller) (2025.11)
2026-01-04T18:37:02.6799397Z Requirement already satisfied: setuptools>=42.0.0 in /opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages (from pyinstaller) (79.0.1)
2026-01-04T18:37:02.7543664Z ##[group]Run crazy-max/ghaction-import-gpg@v5
2026-01-04T18:37:02.7544176Z with:
2026-01-04T18:37:02.7548085Z   gpg_private_key: ***

2026-01-04T18:37:02.7548375Z   passphrase: ***
2026-01-04T18:37:02.7548621Z   git_config_global: false
2026-01-04T18:37:02.7548886Z   git_user_signingkey: false
2026-01-04T18:37:02.7549155Z   git_commit_gpgsign: false
2026-01-04T18:37:02.7549414Z   git_tag_gpgsign: false
2026-01-04T18:37:02.7549666Z   git_push_gpgsign: if-asked
2026-01-04T18:37:02.7549911Z   workdir: .
2026-01-04T18:37:02.7550124Z env:
2026-01-04T18:37:02.7550410Z   pythonLocation: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:02.7550877Z   PKG_CONFIG_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib/pkgconfig
2026-01-04T18:37:02.7551320Z   Python_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:02.7551724Z   Python2_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:02.7552330Z   Python3_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:02.7552767Z   LD_LIBRARY_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib
2026-01-04T18:37:02.7557169Z   HAS_GPG_KEY: ***

2026-01-04T18:37:02.7557413Z ##[endgroup]
2026-01-04T18:37:02.8647978Z ##[group]GnuPG info
2026-01-04T18:37:02.8648959Z Version    : 2.4.4 (libgcrypt 1.10.3)
2026-01-04T18:37:02.8649505Z Libdir     : /usr/lib/x86_64-linux-gnu/gnupg
2026-01-04T18:37:02.8650019Z Libexecdir : /usr/lib/gnupg
2026-01-04T18:37:02.8650370Z Datadir    : /usr/share/gnupg
2026-01-04T18:37:02.8650833Z Homedir    : /home/<USER>/.gnupg
2026-01-04T18:37:02.8651503Z ##[endgroup]
2026-01-04T18:37:02.8960490Z ##[group]GPG private key info
2026-01-04T18:37:02.8961135Z Fingerprint  : EDC8AE6B2F41B9FDB72690B12782F60FD42768B9
2026-01-04T18:37:02.8961741Z KeyID        : 2782F60FD42768B9
2026-01-04T18:37:02.8962200Z Name         : piotrek1372
2026-01-04T18:37:02.8962700Z Email        : <EMAIL>
2026-01-04T18:37:02.8963536Z CreationTime : Sat Jan 03 2026 05:04:44 GMT+0000 (Coordinated Universal Time)
2026-01-04T18:37:02.8964695Z ##[endgroup]
2026-01-04T18:37:02.8967409Z ##[group]Fingerprint to use
2026-01-04T18:37:02.8968164Z EDC8AE6B2F41B9FDB72690B12782F60FD42768B9
2026-01-04T18:37:02.8969226Z ##[endgroup]
2026-01-04T18:37:02.8970143Z ##[group]Importing GPG private key
2026-01-04T18:37:02.9607863Z gpg: directory '/home/<USER>/.gnupg' created
2026-01-04T18:37:02.9608611Z gpg: keybox '/home/<USER>/.gnupg/pubring.kbx' created
2026-01-04T18:37:02.9609309Z gpg: /home/<USER>/.gnupg/trustdb.gpg: trustdb created
2026-01-04T18:37:02.9610194Z gpg: key 2782F60FD42768B9: public key "piotrek1372 <<EMAIL>>" imported
2026-01-04T18:37:02.9611067Z gpg: key 2782F60FD42768B9: secret key imported
2026-01-04T18:37:02.9611664Z gpg: Total number processed: 1
2026-01-04T18:37:02.9612166Z gpg:               imported: 1
2026-01-04T18:37:02.9612621Z gpg:       secret keys read: 1
2026-01-04T18:37:02.9613153Z gpg:   secret keys imported: 1
2026-01-04T18:37:02.9614404Z ##[endgroup]
2026-01-04T18:37:02.9615232Z ##[group]Configuring GnuPG agent
2026-01-04T18:37:02.9615776Z GnuPG home: /home/<USER>/.gnupg
2026-01-04T18:37:02.9677363Z ##[endgroup]
2026-01-04T18:37:02.9678178Z ##[group]Getting keygrips
2026-01-04T18:37:02.9765610Z Presetting passphrase for AAF0F7184E3F58B024A32FE5E9B8EFBA76A5AEAD
2026-01-04T18:37:02.9871928Z Presetting passphrase for 745721F4308988187C6E7A4D8953B5F37AB28FAB
2026-01-04T18:37:02.9971927Z ##[endgroup]
2026-01-04T18:37:02.9972674Z ##[group]Setting outputs
2026-01-04T18:37:02.9973271Z fingerprint=EDC8AE6B2F41B9FDB72690B12782F60FD42768B9
2026-01-04T18:37:02.9974736Z keyid=2782F60FD42768B9
2026-01-04T18:37:02.9975218Z name=piotrek1372
2026-01-04T18:37:02.9976594Z email=<EMAIL>
2026-01-04T18:37:02.9977739Z ##[endgroup]
2026-01-04T18:37:03.0058870Z ##[group]Run echo "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT
2026-01-04T18:37:03.0059423Z [36;1mecho "VERSION=${GITHUB_REF#refs/tags/v}" >> $GITHUB_OUTPUT[0m
2026-01-04T18:37:03.0092629Z shell: /usr/bin/bash -e {0}
2026-01-04T18:37:03.0092919Z env:
2026-01-04T18:37:03.0093234Z   pythonLocation: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:03.0093909Z   PKG_CONFIG_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib/pkgconfig
2026-01-04T18:37:03.0094489Z   Python_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:03.0094890Z   Python2_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:03.0095292Z   Python3_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:03.0095695Z   LD_LIBRARY_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib
2026-01-04T18:37:03.0096038Z ##[endgroup]
2026-01-04T18:37:03.0219442Z ##[group]Run bash packaging/scripts/build_packages.sh refs/heads/main
2026-01-04T18:37:03.0219988Z [36;1mbash packaging/scripts/build_packages.sh refs/heads/main[0m
2026-01-04T18:37:03.0252735Z shell: /usr/bin/bash -e {0}
2026-01-04T18:37:03.0253209Z env:
2026-01-04T18:37:03.0253504Z   pythonLocation: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:03.0254381Z   PKG_CONFIG_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib/pkgconfig
2026-01-04T18:37:03.0254950Z   Python_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:03.0255368Z   Python2_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:03.0255770Z   Python3_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:03.0256169Z   LD_LIBRARY_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib
2026-01-04T18:37:03.0260130Z   GPG_PRIVATE_KEY: ***

2026-01-04T18:37:03.0260563Z   GPG_PASSPHRASE: ***
2026-01-04T18:37:03.0261027Z ##[endgroup]
2026-01-04T18:37:03.0366282Z 📦 Starting Package Build for version 0.2.0...
2026-01-04T18:37:03.0366874Z 📍 Script Directory: /home/<USER>/work/CA-Racing/CA-Racing/packaging/scripts
2026-01-04T18:37:03.0367430Z 📍 Project Root: /home/<USER>/work/CA-Racing/CA-Racing
2026-01-04T18:37:03.0380670Z ⚠️ Build directory (/home/<USER>/work/CA-Racing/CA-Racing/dist/CA-Racing) not found.
2026-01-04T18:37:03.0381461Z 🔨 Running build script: /home/<USER>/work/CA-Racing/CA-Racing/packaging/scripts/build_dist.sh
2026-01-04T18:37:03.0415868Z 🔨 Building CA-Racing distribution...
2026-01-04T18:37:03.2286881Z 118 INFO: PyInstaller: 6.17.0, contrib hooks: 2025.11
2026-01-04T18:37:03.2287406Z 118 INFO: Python: 3.10.19
2026-01-04T18:37:03.2301243Z 119 INFO: Platform: Linux-6.11.0-1018-azure-x86_64-with-glibc2.39
2026-01-04T18:37:03.2301996Z 119 INFO: Python environment: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:03.3215192Z 210 INFO: UPX is available but is disabled on non-Windows due to known compatibility problems.
2026-01-04T18:37:03.3222830Z 211 INFO: Module search paths (PYTHONPATH):
2026-01-04T18:37:03.3223567Z ['/opt/hostedtoolcache/Python/3.10.19/x64/lib/python310.zip',
2026-01-04T18:37:03.3224627Z  '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10',
2026-01-04T18:37:03.3225478Z  '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/lib-dynload',
2026-01-04T18:37:03.3226459Z  '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages',
2026-01-04T18:37:03.3227239Z  '/home/<USER>/work/CA-Racing/CA-Racing']
2026-01-04T18:37:03.8679003Z pygame 2.6.1 (SDL 2.28.4, Python 3.10.19)
2026-01-04T18:37:03.8679945Z Hello from the pygame community. https://www.pygame.org/contribute.html
2026-01-04T18:37:03.8995943Z 788 INFO: Appending 'datas' from .spec
2026-01-04T18:37:03.9012099Z 790 INFO: checking Analysis
2026-01-04T18:37:03.9012986Z 790 INFO: Building Analysis because Analysis-00.toc is non existent
2026-01-04T18:37:03.9014214Z 790 INFO: Looking for Python shared library...
2026-01-04T18:37:03.9109553Z 800 INFO: Using Python shared library: /opt/hostedtoolcache/Python/3.10.19/x64/lib/libpython3.10.so.1.0
2026-01-04T18:37:03.9110809Z 800 INFO: Running Analysis Analysis-00.toc
2026-01-04T18:37:03.9111554Z 800 INFO: Target bytecode optimization level: 0
2026-01-04T18:37:03.9112154Z 800 INFO: Initializing module dependency graph...
2026-01-04T18:37:03.9128887Z 802 INFO: Initializing module graph hook caches...
2026-01-04T18:37:03.9226390Z 812 INFO: Analyzing modules for base_library.zip ...
2026-01-04T18:37:04.4440383Z 1333 INFO: Processing standard module hook 'hook-encodings.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:05.8231717Z 2712 INFO: Processing standard module hook 'hook-pickle.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:06.8031369Z 3692 INFO: Processing standard module hook 'hook-heapq.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:07.4531291Z 4342 INFO: Caching module dependency graph...
2026-01-04T18:37:07.4928318Z 4382 INFO: Analyzing /home/<USER>/work/CA-Racing/CA-Racing/main.py
2026-01-04T18:37:07.5048142Z 4394 INFO: Processing standard module hook 'hook-pygame.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/pygame/__pyinstaller'
2026-01-04T18:37:07.6556253Z 4544 INFO: Processing pre-safe-import-module hook 'hook-typing_extensions.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:07.6560380Z 4545 INFO: SetuptoolsInfo: initializing cached setuptools info...
2026-01-04T18:37:12.3969047Z 9286 INFO: Setuptools: 'typing_extensions' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.typing_extensions'!
2026-01-04T18:37:12.4018132Z 9291 INFO: Processing standard module hook 'hook-setuptools.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:12.4341993Z 9323 INFO: Processing standard module hook 'hook-platform.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:12.4358134Z 9325 INFO: Processing pre-safe-import-module hook 'hook-distutils.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:12.4359765Z 9325 INFO: Processing pre-find-module-path hook 'hook-distutils.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_find_module_path'
2026-01-04T18:37:12.5863083Z 9475 INFO: Processing standard module hook 'hook-distutils.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:12.6091719Z 9498 INFO: Processing standard module hook 'hook-distutils.util.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:12.6791631Z 9568 INFO: Processing standard module hook 'hook-sysconfig.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:12.6969201Z 9586 INFO: Processing standard module hook 'hook-_osx_support.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:12.7285089Z 9617 INFO: Processing pre-safe-import-module hook 'hook-packaging.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:12.8071809Z 9696 INFO: Processing standard module hook 'hook-_ctypes.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:12.8552267Z 9744 INFO: Processing pre-safe-import-module hook 'hook-jaraco.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:12.8554473Z 9744 INFO: Setuptools: 'jaraco' appears to be a full setuptools-vendored copy - creating alias to 'setuptools._vendor.jaraco'!
2026-01-04T18:37:12.8620268Z 9751 INFO: Processing standard module hook 'hook-setuptools._vendor.jaraco.text.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:12.8718698Z 9761 INFO: Processing pre-safe-import-module hook 'hook-importlib_resources.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:12.8779482Z 9767 INFO: Processing pre-safe-import-module hook 'hook-more_itertools.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:12.8781417Z 9767 INFO: Setuptools: 'more_itertools' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.more_itertools'!
2026-01-04T18:37:13.2950278Z 10184 INFO: Processing pre-safe-import-module hook 'hook-backports.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:13.2951424Z 10184 INFO: Setuptools: 'backports' appears to be a full setuptools-vendored copy - creating alias to 'setuptools._vendor.backports'!
2026-01-04T18:37:13.4077402Z 10296 INFO: Processing pre-safe-import-module hook 'hook-importlib_metadata.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:13.4079460Z 10297 INFO: Setuptools: 'importlib_metadata' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.importlib_metadata'!
2026-01-04T18:37:13.4411472Z 10330 INFO: Processing standard module hook 'hook-setuptools._vendor.importlib_metadata.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:13.4885663Z 10377 INFO: Processing pre-safe-import-module hook 'hook-zipp.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:13.4887454Z 10378 INFO: Setuptools: 'zipp' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.zipp'!
2026-01-04T18:37:13.7662265Z 10655 INFO: Processing pre-safe-import-module hook 'hook-tomli.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:13.7664417Z 10655 INFO: Setuptools: 'tomli' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.tomli'!
2026-01-04T18:37:14.2270085Z 11116 INFO: Processing standard module hook 'hook-pkg_resources.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:14.3979845Z 11287 INFO: Processing standard module hook 'hook-xml.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:14.3999887Z 11289 INFO: Processing pre-safe-import-module hook 'hook-platformdirs.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:14.4001848Z 11289 INFO: Setuptools: 'platformdirs' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.platformdirs'!
2026-01-04T18:37:14.5747298Z 11463 INFO: Processing pre-safe-import-module hook 'hook-wheel.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/pre_safe_import_module'
2026-01-04T18:37:14.5749099Z 11464 INFO: Setuptools: 'wheel' appears to be a setuptools-vendored copy - creating alias to 'setuptools._vendor.wheel'!
2026-01-04T18:37:14.8992503Z 11788 INFO: Processing standard module hook 'hook-numpy.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:15.6892975Z 12578 INFO: Processing standard module hook 'hook-difflib.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:15.8657482Z 12755 INFO: Processing standard module hook 'hook-multiprocessing.util.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:17.6956820Z 14585 INFO: Processing standard module hook 'hook-xml.etree.cElementTree.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks'
2026-01-04T18:37:17.7415300Z 14630 INFO: Processing module hooks (post-graph stage)...
2026-01-04T18:37:18.3132248Z 15202 INFO: Performing binary vs. data reclassification (64 entries)
2026-01-04T18:37:18.4545429Z 15343 INFO: Looking for ctypes DLLs
2026-01-04T18:37:18.4714205Z 15360 INFO: Analyzing run-time hooks ...
2026-01-04T18:37:18.4753339Z 15364 INFO: Including run-time hook 'pyi_rth_inspect.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/rthooks'
2026-01-04T18:37:18.4772514Z 15366 INFO: Including run-time hook 'pyi_rth_pkgutil.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/rthooks'
2026-01-04T18:37:18.4784916Z 15367 INFO: Including run-time hook 'pyi_rth_multiprocessing.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/rthooks'
2026-01-04T18:37:18.4798669Z 15369 INFO: Including run-time hook 'pyi_rth_setuptools.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/rthooks'
2026-01-04T18:37:18.4807726Z 15370 INFO: Including run-time hook 'pyi_rth_pkgres.py' from '/opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/hooks/rthooks'
2026-01-04T18:37:18.4971001Z 15386 INFO: Creating base_library.zip...
2026-01-04T18:37:18.5141935Z 15403 INFO: Looking for dynamic libraries
2026-01-04T18:37:19.8604022Z 16749 INFO: Warnings written to /home/<USER>/work/CA-Racing/CA-Racing/build/ca-racing/warn-ca-racing.txt
2026-01-04T18:37:19.9045424Z 16793 INFO: Graph cross-reference written to /home/<USER>/work/CA-Racing/CA-Racing/build/ca-racing/xref-ca-racing.html
2026-01-04T18:37:19.9314922Z 16820 INFO: checking PYZ
2026-01-04T18:37:19.9315671Z 16821 INFO: Building PYZ because PYZ-00.toc is non existent
2026-01-04T18:37:19.9316654Z 16821 INFO: Building PYZ (ZlibArchive) /home/<USER>/work/CA-Racing/CA-Racing/build/ca-racing/PYZ-00.pyz
2026-01-04T18:37:20.3527908Z 17242 INFO: Building PYZ (ZlibArchive) /home/<USER>/work/CA-Racing/CA-Racing/build/ca-racing/PYZ-00.pyz completed successfully.
2026-01-04T18:37:20.3644314Z 17253 WARNING: Ignoring icon; supported only on Windows and macOS!
2026-01-04T18:37:20.3754227Z 17264 INFO: checking PKG
2026-01-04T18:37:20.3754852Z 17264 INFO: Building PKG because PKG-00.toc is non existent
2026-01-04T18:37:20.3755427Z 17265 INFO: Building PKG (CArchive) ca-racing.pkg
2026-01-04T18:37:36.8904625Z 33779 INFO: Building PKG (CArchive) ca-racing.pkg completed successfully.
2026-01-04T18:37:36.8960711Z 33785 INFO: Bootloader /opt/hostedtoolcache/Python/3.10.19/x64/lib/python3.10/site-packages/PyInstaller/bootloader/Linux-64bit-intel/run
2026-01-04T18:37:36.8961581Z 33785 INFO: checking EXE
2026-01-04T18:37:36.8961963Z 33785 INFO: Building EXE because EXE-00.toc is non existent
2026-01-04T18:37:36.8962390Z 33785 INFO: Building EXE from EXE-00.toc
2026-01-04T18:37:36.8962871Z 33785 INFO: Copying bootloader EXE to /home/<USER>/work/CA-Racing/CA-Racing/dist/ca-racing
2026-01-04T18:37:36.8965150Z 33786 INFO: Appending PKG archive to custom ELF section in EXE
2026-01-04T18:37:37.1231156Z 34012 INFO: Building EXE from EXE-00.toc completed successfully.
2026-01-04T18:37:37.1289642Z 34018 INFO: Build complete! The results are available in: /home/<USER>/work/CA-Racing/CA-Racing/dist
2026-01-04T18:37:37.1292990Z pygame 2.6.1 (SDL 2.28.4, Python 3.10.19)
2026-01-04T18:37:37.1293702Z Hello from the pygame community. https://www.pygame.org/contribute.html
2026-01-04T18:37:37.2031529Z 📦 Organizing single-file executable...
2026-01-04T18:37:37.2060532Z ✅ Build complete!
2026-01-04T18:37:37.2063091Z 🐧 Building DEB package...
2026-01-04T18:37:37.2723661Z DEBUG: Updating DEB control file version to 0.2.0
2026-01-04T18:37:37.2787830Z dpkg-deb: building package 'ca-racing' in '/home/<USER>/work/CA-Racing/CA-Racing/artifacts/ca-racing_0.2.0_amd64.deb'.
2026-01-04T18:37:46.6516371Z ✅ DEB created at /home/<USER>/work/CA-Racing/CA-Racing/artifacts/ca-racing_0.2.0_amd64.deb
2026-01-04T18:37:46.6517336Z 🎩 Building RPM package...
2026-01-04T18:37:46.6552116Z CA-Racing/
2026-01-04T18:37:46.6552509Z CA-Racing/CA-Racing
2026-01-04T18:37:48.9347285Z Executing(%prep): /bin/sh -e /var/tmp/rpm-tmp.p9z0oH
2026-01-04T18:37:48.9357329Z + umask 022
2026-01-04T18:37:48.9357760Z + cd /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILD
2026-01-04T18:37:48.9358463Z + cd /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILD
2026-01-04T18:37:48.9359014Z + rm -rf CA-Racing
2026-01-04T18:37:48.9365991Z + /usr/lib/rpm/rpmuncompress -x /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/SOURCES/ca-racing.tar.gz
2026-01-04T18:37:49.3560937Z + STATUS=0
2026-01-04T18:37:49.3561564Z + [ 0 -ne 0 ]
2026-01-04T18:37:49.3561959Z + cd CA-Racing
2026-01-04T18:37:49.3562368Z + /bin/chmod -Rf a+rX,u+w,g-w,o-w .
2026-01-04T18:37:49.3568938Z + RPM_EC=0
2026-01-04T18:37:49.3570588Z + jobs -p
2026-01-04T18:37:49.3571818Z + exit 0
2026-01-04T18:37:49.3583310Z Executing(%build): /bin/sh -e /var/tmp/rpm-tmp.0NUCpY
2026-01-04T18:37:49.3593402Z + umask 022
2026-01-04T18:37:49.3594136Z + cd /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILD
2026-01-04T18:37:49.3594722Z + cd CA-Racing
2026-01-04T18:37:49.3595029Z + RPM_EC=0
2026-01-04T18:37:49.3595434Z + jobs -p
2026-01-04T18:37:49.3596952Z + exit 0
2026-01-04T18:37:49.3606862Z Executing(%install): /bin/sh -e /var/tmp/rpm-tmp.oWaVVy
2026-01-04T18:37:49.3615982Z + umask 022
2026-01-04T18:37:49.3616498Z + cd /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILD
2026-01-04T18:37:49.3617274Z + /bin/rm -rf /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILDROOT/ca-racing-0.2.0-1.x86_64
2026-01-04T18:37:49.3622358Z + /bin/mkdir -p /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILDROOT
2026-01-04T18:37:49.3630992Z + /bin/mkdir /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILDROOT/ca-racing-0.2.0-1.x86_64
2026-01-04T18:37:49.3639341Z + cd CA-Racing
2026-01-04T18:37:49.3639981Z + mkdir -p /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILDROOT/ca-racing-0.2.0-1.x86_64/opt/ca-racing
2026-01-04T18:37:49.3649005Z + mkdir -p /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILDROOT/ca-racing-0.2.0-1.x86_64/usr/bin
2026-01-04T18:37:49.3657743Z + install -m 755 CA-Racing /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILDROOT/ca-racing-0.2.0-1.x86_64/opt/ca-racing/ca-racing
2026-01-04T18:37:49.4199612Z + ln -s /opt/ca-racing/ca-racing /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILDROOT/ca-racing-0.2.0-1.x86_64/usr/bin/ca-racing
2026-01-04T18:37:49.4207643Z + /usr/lib/rpm/brp-compress /usr
2026-01-04T18:37:49.4227878Z + /usr/lib/rpm/brp-elfperms
2026-01-04T18:37:49.4284556Z + /usr/lib/rpm/brp-strip /usr/bin/strip
2026-01-04T18:37:49.4397398Z + /usr/lib/rpm/brp-strip-static-archive /usr/bin/strip
2026-01-04T18:37:49.4550532Z + /usr/lib/rpm/brp-strip-comment-note /usr/bin/strip /usr/bin/objdump
2026-01-04T18:37:49.6678543Z + /usr/lib/rpm/brp-remove-la-files
2026-01-04T18:37:49.6727037Z Processing files: ca-racing-0.2.0-1.x86_64
2026-01-04T18:37:49.7645874Z warning: absolute symlink: /usr/bin/ca-racing -> /opt/ca-racing/ca-racing
2026-01-04T18:37:49.8711619Z Provides: ca-racing = 0.2.0-1 ca-racing(x86-64) = 0.2.0-1
2026-01-04T18:37:49.8712445Z Requires(rpmlib): rpmlib(CompressedFileNames) <= 3.0.4-1 rpmlib(FileDigests) <= 4.6.0-1 rpmlib(PayloadFilesHavePrefix) <= 4.0-1
2026-01-04T18:37:49.8714580Z Requires: libc.so.6()(64bit) libc.so.6(GLIBC_2.14)(64bit) libc.so.6(GLIBC_2.2.5)(64bit) libc.so.6(GLIBC_2.3)(64bit) libc.so.6(GLIBC_2.3.4)(64bit) libc.so.6(GLIBC_2.4)(64bit) libc.so.6(GLIBC_2.7)(64bit) libdl.so.2()(64bit) libdl.so.2(GLIBC_2.2.5)(64bit) libpthread.so.0()(64bit) libpthread.so.0(GLIBC_2.2.5)(64bit) libz.so.1()(64bit) rtld(GNU_HASH)
2026-01-04T18:37:49.8716009Z Checking for unpackaged file(s): /usr/lib/rpm/check-files /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILDROOT/ca-racing-0.2.0-1.x86_64
2026-01-04T18:37:52.0576155Z Wrote: /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/RPMS/x86_64/ca-racing-0.2.0-1.x86_64.rpm
2026-01-04T18:37:52.0588327Z Executing(%clean): /bin/sh -e /var/tmp/rpm-tmp.v81LhH
2026-01-04T18:37:52.0604134Z + umask 022
2026-01-04T18:37:52.0624745Z + cd /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILD
2026-01-04T18:37:52.0625329Z + cd CA-Racing
2026-01-04T18:37:52.0626016Z + /bin/rm -rf /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILDROOT/ca-racing-0.2.0-1.x86_64
2026-01-04T18:37:52.0759845Z + RPM_EC=0
2026-01-04T18:37:52.0762327Z + jobs -p
2026-01-04T18:37:52.0763235Z + exit 0
2026-01-04T18:37:52.0775592Z Executing(rmbuild): /bin/sh -e /var/tmp/rpm-tmp.5lpZOl
2026-01-04T18:37:52.0786257Z + umask 022
2026-01-04T18:37:52.0786784Z + cd /home/<USER>/work/CA-Racing/CA-Racing/build/rpm/BUILD
2026-01-04T18:37:52.0787789Z + rm -rf CA-Racing CA-Racing.gemspec
2026-01-04T18:37:52.0951585Z + RPM_EC=0
2026-01-04T18:37:52.0953223Z + jobs -p
2026-01-04T18:37:52.0954577Z + exit 0
2026-01-04T18:37:52.0957069Z     absolute symlink: /usr/bin/ca-racing -> /opt/ca-racing/ca-racing
2026-01-04T18:37:52.0957853Z 
2026-01-04T18:37:52.0958060Z RPM build warnings:
2026-01-04T18:37:52.1585126Z ✅ RPMs copied to /home/<USER>/work/CA-Racing/CA-Racing/artifacts
2026-01-04T18:37:52.1600150Z total 162120
2026-01-04T18:37:52.1600734Z -rw-r--r-- 1 <USER> <GROUP> 82392878 Jan  4 18:37 ca-racing-0.2.0-1.x86_64.rpm
2026-01-04T18:37:52.1601506Z -rw-r--r-- 1 <USER> <GROUP> 83614336 Jan  4 18:37 ca-racing_0.2.0_amd64.deb
2026-01-04T18:37:52.1602465Z 🏭 Generating Package Repositories...
2026-01-04T18:37:52.1627235Z 🏭 Starting Repository Creation...
2026-01-04T18:37:52.1627801Z 🔐 Importing GPG key...
2026-01-04T18:37:52.1682687Z gpg: key 2782F60FD42768B9: "piotrek1372 <<EMAIL>>" not changed
2026-01-04T18:37:52.1698683Z gpg: key 2782F60FD42768B9: secret key imported
2026-01-04T18:37:52.1699280Z gpg: Total number processed: 1
2026-01-04T18:37:52.1699731Z gpg:              unchanged: 1
2026-01-04T18:37:52.1700142Z gpg:       secret keys read: 1
2026-01-04T18:37:52.1700585Z gpg:  secret keys unchanged: 1
2026-01-04T18:37:52.1764778Z 🔑 Using GPG Key ID: 2782F60FD42768B9
2026-01-04T18:37:52.1790809Z 📦 Moving artifacts...
2026-01-04T18:37:52.2868867Z 🐧 Generating APT repository...
2026-01-04T18:37:53.1189075Z dpkg-scanpackages: warning: Packages in archive but missing from override file:
2026-01-04T18:37:53.1189748Z dpkg-scanpackages: warning:   ca-racing
2026-01-04T18:37:53.1190280Z dpkg-scanpackages: info: Wrote 1 entries to output Packages file.
2026-01-04T18:37:53.1277156Z ✍️ Signing APT Release file...
2026-01-04T18:37:53.1326089Z gpg: using "2782F60FD42768B9" as default secret key for signing
2026-01-04T18:37:54.1916744Z gpg: using "2782F60FD42768B9" as default secret key for signing
2026-01-04T18:37:54.5437870Z 🎩 Generating RPM repository...
2026-01-04T18:37:54.5438795Z ✍️ Signing RPM packages...
2026-01-04T18:37:54.8113638Z gpg: Fatal: can't create directory '/root/.gnupg': Permission denied
2026-01-04T18:37:54.8116524Z error: gpg exec failed (2)
2026-01-04T18:37:54.8119443Z repo/rpm/ca-racing-0.2.0-1.x86_64.rpm:
2026-01-04T18:37:54.8125002Z ⚠️ RPM signing failed. Continuing without signed RPMs.
2026-01-04T18:37:54.9095168Z Directory walk started
2026-01-04T18:37:54.9095679Z Directory walk done - 1 packages
2026-01-04T18:37:54.9096094Z Temporary output repo path: repo/rpm/.repodata/
2026-01-04T18:37:54.9096449Z Preparing sqlite DBs
2026-01-04T18:37:54.9096707Z Pool started (with 5 workers)
2026-01-04T18:37:54.9096966Z Pool finished
2026-01-04T18:37:54.9107126Z ✅ RPM repository created using createrepo_c at repo/rpm
2026-01-04T18:37:54.9107663Z DEBUG: Contents of repo/rpm after createrepo_c:
2026-01-04T18:37:54.9120518Z total 80476
2026-01-04T18:37:54.9120991Z drwxr-xr-x 3 <USER> <GROUP>     4096 Jan  4 18:37 .
2026-01-04T18:37:54.9121631Z drwxr-xr-x 4 <USER> <GROUP>     4096 Jan  4 18:37 ..
2026-01-04T18:37:54.9122209Z -rw-r--r-- 1 <USER> <GROUP> 82392878 Jan  4 18:37 ca-racing-0.2.0-1.x86_64.rpm
2026-01-04T18:37:54.9122973Z drwxr-xr-x 2 <USER> <GROUP>     4096 Jan  4 18:37 repodata
2026-01-04T18:37:54.9123523Z DEBUG: Contents of repo/rpm/repodata after createrepo_c:
2026-01-04T18:37:54.9135913Z total 36
2026-01-04T18:37:54.9136269Z drwxr-xr-x 2 <USER> <GROUP> 4096 Jan  4 18:37 .
2026-01-04T18:37:54.9136648Z drwxr-xr-x 3 <USER> <GROUP> 4096 Jan  4 18:37 ..
2026-01-04T18:37:54.9137356Z -rw-r--r-- 1 <USER> <GROUP>  577 Jan  4 18:37 25aebb849e7b0f48d61baf9b1d742a104015743ada3dfab52cf76bce3b452109-other.xml.gz
2026-01-04T18:37:54.9138720Z -rw-r--r-- 1 <USER> <GROUP>  317 Jan  4 18:37 2c28416694f79a74bcc7b21871efbe50d010e199d4b37c043227be2f4656bbc9-filelists.xml.gz
2026-01-04T18:37:54.9140105Z -rw-r--r-- 1 <USER> <GROUP>  846 Jan  4 18:37 5fc10ca5a2a2ebafe6129727d09cda54bb11372de10a5f0e72783c500343dec7-primary.xml.gz
2026-01-04T18:37:54.9141349Z -rw-r--r-- 1 <USER> <GROUP> 2245 Jan  4 18:37 77341677954eaa3a7f618db8cf3a1c26062ce8d864aad940e0db2202049a3c8a-primary.sqlite.bz2
2026-01-04T18:37:54.9142235Z -rw-r--r-- 1 <USER> <GROUP>  900 Jan  4 18:37 afe78cff78a43efbe14a08d56477ca9e932ea254520b77e5b68f185cd93caaa5-filelists.sqlite.bz2
2026-01-04T18:37:54.9143242Z -rw-r--r-- 1 <USER> <GROUP> 1065 Jan  4 18:37 b39d2e6e78a7190f167254a20fe3afef290c8273b401d45c34f17ce6166429db-other.sqlite.bz2
2026-01-04T18:37:54.9144056Z -rw-r--r-- 1 <USER> <GROUP> 3070 Jan  4 18:37 repomd.xml
2026-01-04T18:37:54.9144572Z 🔑 Exporting Public Key...
2026-01-04T18:37:54.9179180Z ✅ Public GPG key exported for RPM repository.
2026-01-04T18:37:54.9205151Z ✅ Public GPG key exported to repo/public.key.
2026-01-04T18:37:54.9205786Z ✅ Repository generation complete!
2026-01-04T18:37:54.9206302Z 📂 Output in: repo
2026-01-04T18:37:54.9248801Z ##[group]Run cp dist/CA-Racing/CA-Racing artifacts/ca-racing_linux_amd64
2026-01-04T18:37:54.9249350Z [36;1mcp dist/CA-Racing/CA-Racing artifacts/ca-racing_linux_amd64[0m
2026-01-04T18:37:54.9281525Z shell: /usr/bin/bash -e {0}
2026-01-04T18:37:54.9281799Z env:
2026-01-04T18:37:54.9282088Z   pythonLocation: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:54.9282567Z   PKG_CONFIG_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib/pkgconfig
2026-01-04T18:37:54.9283036Z   Python_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:54.9283448Z   Python2_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:54.9284040Z   Python3_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:54.9284447Z   LD_LIBRARY_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib
2026-01-04T18:37:54.9284785Z ##[endgroup]
2026-01-04T18:37:54.9916585Z ##[group]Run actions/upload-artifact@v4
2026-01-04T18:37:54.9916911Z with:
2026-01-04T18:37:54.9917138Z   name: linux-builds
2026-01-04T18:37:54.9917398Z   path: artifacts/*
2026-01-04T18:37:54.9917645Z   if-no-files-found: warn
2026-01-04T18:37:54.9917904Z   compression-level: 6
2026-01-04T18:37:54.9918145Z   overwrite: false
2026-01-04T18:37:54.9918387Z   include-hidden-files: false
2026-01-04T18:37:54.9918633Z env:
2026-01-04T18:37:54.9918908Z   pythonLocation: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:54.9919378Z   PKG_CONFIG_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib/pkgconfig
2026-01-04T18:37:54.9919815Z   Python_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:54.9920219Z   Python2_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:54.9920611Z   Python3_ROOT_DIR: /opt/hostedtoolcache/Python/3.10.19/x64
2026-01-04T18:37:54.9921004Z   LD_LIBRARY_PATH: /opt/hostedtoolcache/Python/3.10.19/x64/lib
2026-01-04T18:37:54.9921370Z ##[endgroup]
2026-01-04T18:37:55.2097286Z With the provided path, there will be 3 files uploaded
2026-01-04T18:37:55.2102336Z Artifact name is valid!
2026-01-04T18:37:55.2104571Z Root directory input is valid!
2026-01-04T18:37:55.3734500Z Beginning upload of artifact content to blob storage
2026-01-04T18:37:56.1640051Z Uploaded bytes 8388608
2026-01-04T18:37:56.4013554Z Uploaded bytes 16777216
2026-01-04T18:37:56.4723581Z Uploaded bytes 25165824
2026-01-04T18:37:56.6213285Z Uploaded bytes 33554432
2026-01-04T18:37:56.8972826Z Uploaded bytes 41943040
2026-01-04T18:37:57.1261722Z Uploaded bytes 50331648
2026-01-04T18:37:57.3252254Z Uploaded bytes 58720256
2026-01-04T18:37:57.5752346Z Uploaded bytes 67108864
2026-01-04T18:37:57.8397281Z Uploaded bytes 75497472
2026-01-04T18:37:58.0926622Z Uploaded bytes 83886080
2026-01-04T18:37:58.3179212Z Uploaded bytes 92274688
2026-01-04T18:37:58.5880276Z Uploaded bytes 100663296
2026-01-04T18:37:58.8390594Z Uploaded bytes 109051904
2026-01-04T18:37:59.0872158Z Uploaded bytes 117440512
2026-01-04T18:37:59.3156857Z Uploaded bytes 125829120
2026-01-04T18:37:59.5869840Z Uploaded bytes 134217728
2026-01-04T18:37:59.7831356Z Uploaded bytes 142606336
2026-01-04T18:38:00.0656729Z Uploaded bytes 150994944
2026-01-04T18:38:00.3003897Z Uploaded bytes 159383552
2026-01-04T18:38:00.5683134Z Uploaded bytes 167772160
2026-01-04T18:38:00.7392305Z Uploaded bytes 176160768
2026-01-04T18:38:01.0006046Z Uploaded bytes 184549376
2026-01-04T18:38:01.2764870Z Uploaded bytes 192937984
2026-01-04T18:38:01.4967413Z Uploaded bytes 201326592
2026-01-04T18:38:01.6762997Z Uploaded bytes 209715200
2026-01-04T18:38:01.9582486Z Uploaded bytes 218103808
2026-01-04T18:38:02.1949728Z Uploaded bytes 226492416
2026-01-04T18:38:02.4060723Z Uploaded bytes 234881024
2026-01-04T18:38:02.5951891Z Uploaded bytes 243269632
2026-01-04T18:38:02.7191463Z Uploaded bytes 248464842
2026-01-04T18:38:02.7665791Z Finished uploading artifact content to blob storage!
2026-01-04T18:38:02.7668908Z SHA256 digest of uploaded artifact zip is e45b7983a3bfe2f53c7184a8eb02dacbc57b6164ad4c2f02ddba8a322c8c7cd5
2026-01-04T18:38:02.7670998Z Finalizing artifact upload
2026-01-04T18:38:02.9388262Z Artifact linux-builds.zip successfully finalized. Artifact ID 5018637169
2026-01-04T18:38:02.9389467Z Artifact linux-builds has been successfully uploaded! Final size is 248464842 bytes. Artifact ID is 5018637169
2026-01-04T18:38:02.9398652Z Artifact download URL: https://github.com/piotrek1372/CA-Racing/actions/runs/20697364490/artifacts/5018637169
2026-01-04T18:38:02.9549500Z Post job cleanup.
2026-01-04T18:38:03.0429624Z Removing key EDC8AE6B2F41B9FDB72690B12782F60FD42768B9
2026-01-04T18:38:03.0638113Z Killing GnuPG agent
2026-01-04T18:38:03.0775475Z Post job cleanup.
2026-01-04T18:38:03.2430362Z Post job cleanup.
2026-01-04T18:38:03.3173658Z [command]/usr/bin/git version
2026-01-04T18:38:03.3209077Z git version 2.52.0
2026-01-04T18:38:03.3251647Z Temporarily overriding HOME='/home/<USER>/work/_temp/4493d817-c651-4012-890f-6e8c95faec67' before making global git config changes
2026-01-04T18:38:03.3252920Z Adding repository directory to the temporary git global config as a safe directory
2026-01-04T18:38:03.3255956Z [command]/usr/bin/git config --global --add safe.directory /home/<USER>/work/CA-Racing/CA-Racing
2026-01-04T18:38:03.3288012Z [command]/usr/bin/git config --local --name-only --get-regexp core\.sshCommand
2026-01-04T18:38:03.3317134Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'core\.sshCommand' && git config --local --unset-all 'core.sshCommand' || :"
2026-01-04T18:38:03.3550394Z [command]/usr/bin/git config --local --name-only --get-regexp http\.https\:\/\/github\.com\/\.extraheader
2026-01-04T18:38:03.3571272Z http.https://github.com/.extraheader
2026-01-04T18:38:03.3582847Z [command]/usr/bin/git config --local --unset-all http.https://github.com/.extraheader
2026-01-04T18:38:03.3611360Z [command]/usr/bin/git submodule foreach --recursive sh -c "git config --local --name-only --get-regexp 'http\.https\:\/\/github\.com\/\.extraheader' && git config --local --unset-all 'http.https://github.com/.extraheader' || :"
2026-01-04T18:38:03.3939224Z Cleaning up orphan processes
