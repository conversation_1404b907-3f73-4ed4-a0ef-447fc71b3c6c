"""
Engine Physics Calculator
=========================
Realistic vehicle dynamics based on mass, power, and aerodynamics.

Implements proper physics scaling to ensure realistic acceleration times:
- 0-100 km/h in 3-15 seconds depending on power-to-weight ratio
- Quadratic drag limiting top speed
- Rolling resistance for realistic deceleration
- Grip-based traction limits
"""

import math


class EnginePhysics:
    """
    Calculates realistic vehicle acceleration and forces.
    
    All calculations use SI units internally (kg, m/s, N) and convert
    to game units only at the end for consistent physics.
    """
    
    # === GLOBAL PHYSICS CONSTANTS ===
    
    # Scaling factor: Converts real-world forces to game units
    # Lower = more realistic (heavier feel), Higher = more arcade
    # Target: 0-100 km/h in 4-6 seconds for 200 HP/ton car
    # Balanced for gameplay: realistic feel but still fun
    # Note: In-game requires much higher value than simulation tests
    # because of Pygame's coordinate system and frame timing
    # Simulation: 0.0015 = realistic (4-5s)
    # In-game: Need ~10-20x more for actual movement
    PHYSICS_SCALE_FACTOR = 0.025  # HIGH for Pygame gameplay (will balance later)
    
    # Minimum starting force to overcome inertia
    # Ensures all cars can move from standstill
    MIN_STARTING_FORCE_FACTOR = 1.5  # Boost at low speeds (reduced from 2.0)
    
    # Aerodynamic drag coefficient (Cd * A * rho / 2)
    # Typical values: 0.3-0.5 for cars
    # This creates quadratic speed resistance: F_drag = k * v²
    DRAG_COEFFICIENT = 0.4  # Realistic drag
    
    # Rolling resistance coefficient (tires on asphalt)
    # Typical values: 0.01-0.015
    ROLLING_RESISTANCE = 0.012
    
    # Grip limit coefficient (maximum traction)
    # Prevents unrealistic acceleration on poor surfaces
    # ~0.8-1.0 for asphalt, ~0.3-0.5 for grass
    GRIP_COEFFICIENT_ASPHALT = 0.85
    GRIP_COEFFICIENT_GRASS = 0.35
    GRIP_COEFFICIENT_DIRT = 0.60
    
    # Game scale conversion
    PIXELS_PER_METER = 10.0  # 10 pixels = 1 meter in game world
    FPS = 60.0  # Target frame rate
    
    # Speed conversion constants
    KMH_TO_MS = 1.0 / 3.6  # km/h to m/s
    MS_TO_KMH = 3.6  # m/s to km/h
    
    def __init__(self, power_hp, weight_kg, engine_weight_kg=100):
        """
        Initialize physics calculator for a specific vehicle.
        
        Args:
            power_hp: Engine power in horsepower
            weight_kg: Vehicle body weight in kilograms
            engine_weight_kg: Engine weight in kilograms (default: 100)
        """
        self.power_hp = power_hp
        self.body_weight_kg = weight_kg
        self.engine_weight_kg = engine_weight_kg
        self.total_mass_kg = weight_kg + engine_weight_kg
        
        # Convert HP to Watts (1 HP = 745.7 Watts)
        self.power_watts = power_hp * 745.7
        
        # Calculate power-to-weight ratio (HP per metric ton)
        self.power_to_weight = power_hp / (self.total_mass_kg / 1000.0)
        
        # Pre-calculate coefficients for performance
        self._init_coefficients()
    
    def _init_coefficients(self):
        """Pre-calculate physics coefficients for optimization."""
        # Drag coefficient (includes mass effect on frontal area)
        # Heavier/larger vehicles have more frontal area
        mass_area_factor = (self.total_mass_kg / 1200.0) ** 0.5
        self.k_drag = self.DRAG_COEFFICIENT * mass_area_factor
        
        # Rolling resistance force (constant)
        # F_rolling = µ * m * g
        gravity = 9.81  # m/s²
        self.f_rolling = self.ROLLING_RESISTANCE * self.total_mass_kg * gravity
        
        # Maximum traction force (grip limit)
        # F_max = µ * m * g
        self.f_traction_max = self.GRIP_COEFFICIENT_ASPHALT * self.total_mass_kg * gravity
    
    def calculate_acceleration(self, current_speed_ms, throttle=1.0,
                              surface='asphalt', rpm_factor=1.0):
        """
        Calculate realistic acceleration for current conditions.
        
        Args:
            current_speed_ms: Current velocity in meters/second
            throttle: Throttle input (0.0 to 1.0)
            surface: Surface type ('asphalt', 'grass', 'dirt')
            rpm_factor: Engine efficiency at current RPM (0.0 to 1.0)
        
        Returns:
            acceleration in m/s² (can be applied to velocity)
        """
        # 1. ENGINE POWER OUTPUT
        # Power = Force * Velocity, so Force = Power / Velocity
        # At low speeds, use a minimum velocity to avoid division by zero
        min_velocity = 1.0  # m/s (prevents infinite force at v=0)
        velocity_for_calc = max(current_speed_ms, min_velocity)
        
        # Available engine force (limited by power curve)
        f_engine = (self.power_watts * throttle * rpm_factor) / velocity_for_calc
        
        # STARTING BOOST: Extra force at very low speeds to overcome inertia
        # This makes cars feel responsive from standstill
        if current_speed_ms < 2.0:  # Below ~7 km/h
            speed_factor = (2.0 - current_speed_ms) / 2.0  # 1.0 at 0, 0.0 at 2 m/s
            starting_boost = f_engine * self.MIN_STARTING_FORCE_FACTOR * speed_factor
            f_engine += starting_boost
            print(f"[PHYSICS] Starting boost: +{starting_boost:.1f}N at {current_speed_ms:.2f} m/s")
        
        # 2. TRACTION LIMIT (Grip)
        # Can't exceed what the tires can transfer to the ground
        grip_coeff = self._get_grip_coefficient(surface)
        f_traction_max = grip_coeff * self.total_mass_kg * 9.81
        f_engine = min(f_engine, f_traction_max)
        
        # 3. DRAG FORCES
        # Air resistance (quadratic with speed)
        f_drag = self.k_drag * current_speed_ms * abs(current_speed_ms)
        
        # Rolling resistance (constant)
        f_rolling = self.f_rolling
        
        # Total drag (always opposes motion)
        f_resistance = f_drag + f_rolling
        
        # 4. NET FORCE
        f_net = f_engine - f_resistance
        
        # 5. ACCELERATION (F = m * a, so a = F / m)
        acceleration_ms2 = f_net / self.total_mass_kg
        
        # Apply physics scaling for game feel
        # This softens the acceleration for more controllable gameplay
        acceleration_ms2 *= self.PHYSICS_SCALE_FACTOR / 0.001  # Normalize to base scale
        
        # DEBUG: Log final values occasionally
        import random
        if random.random() < 0.01:  # 1% of frames
            print(f"[PHYSICS DEBUG] Speed: {current_speed_ms:.2f} m/s | F_engine: {f_engine:.1f}N | F_net: {f_net:.1f}N | Accel: {acceleration_ms2:.3f} m/s²")
        
        return acceleration_ms2
    
    def calculate_braking_force(self, current_speed_ms, brake_input=1.0,
                               brake_efficiency=1.0, surface='asphalt'):
        """
        Calculate braking deceleration.
        
        Args:
            current_speed_ms: Current velocity in m/s
            brake_input: Brake pedal input (0.0 to 1.0)
            brake_efficiency: Brake system quality (0.5 to 2.0)
            surface: Surface type affects grip
        
        Returns:
            deceleration in m/s² (negative value)
        """
        # Maximum braking force limited by tire grip
        grip_coeff = self._get_grip_coefficient(surface)
        f_brake_max = grip_coeff * self.total_mass_kg * 9.81 * brake_efficiency
        
        # Applied braking force
        f_brake = f_brake_max * brake_input
        
        # Deceleration (always negative)
        deceleration_ms2 = -(f_brake / self.total_mass_kg)
        
        # Apply scaling
        deceleration_ms2 *= self.PHYSICS_SCALE_FACTOR / 0.001
        
        return deceleration_ms2
    
    def calculate_top_speed_ms(self):
        """
        Calculate theoretical top speed where engine force equals drag.
        
        Power = Drag Force * Velocity
        P = (k * v²) * v = k * v³
        v_max = (P / k)^(1/3)
        
        Returns:
            top_speed in m/s
        """
        # At top speed, all power goes into overcoming drag
        # P = F_drag * v = (k * v²) * v = k * v³
        # Solve for v: v_max = (P / k)^(1/3)
        
        v_max_cubed = self.power_watts / self.k_drag
        v_max_ms = v_max_cubed ** (1.0 / 3.0)
        
        # Apply slight reduction for rolling resistance
        # (more accurate would be to solve cubic equation, but this is close enough)
        v_max_ms *= 0.95
        
        return v_max_ms
    
    def calculate_top_speed_kmh(self):
        """
        Calculate theoretical top speed in km/h.
        
        Returns:
            top_speed in km/h
        """
        return self.calculate_top_speed_ms() * self.MS_TO_KMH
    
    def _get_grip_coefficient(self, surface):
        """Get grip coefficient for surface type."""
        surface_grip = {
            'asphalt': self.GRIP_COEFFICIENT_ASPHALT,
            'grass': self.GRIP_COEFFICIENT_GRASS,
            'dirt': self.GRIP_COEFFICIENT_DIRT,
            'offroad': self.GRIP_COEFFICIENT_DIRT
        }
        return surface_grip.get(surface, self.GRIP_COEFFICIENT_ASPHALT)
    
    def get_acceleration_0_to_100_time(self, surface='asphalt'):
        """
        Estimate 0-100 km/h acceleration time.
        
        Uses numerical integration to simulate acceleration over time.
        
        Returns:
            time in seconds to reach 100 km/h
        """
        target_speed_ms = 100.0 * self.KMH_TO_MS  # 100 km/h in m/s
        current_speed_ms = 0.0
        time_seconds = 0.0
        dt = 1.0 / self.FPS  # Time step (1 frame at 60 FPS)
        
        max_iterations = 1000  # Safety limit (16.7 seconds max)
        iteration = 0
        
        while current_speed_ms < target_speed_ms and iteration < max_iterations:
            # Calculate acceleration at current speed
            accel = self.calculate_acceleration(
                current_speed_ms,
                throttle=1.0,
                surface=surface,
                rpm_factor=1.0  # Assume optimal RPM
            )
            
            # Update speed
            current_speed_ms += accel * dt
            time_seconds += dt
            iteration += 1
        
        return time_seconds
    
    def to_game_units(self, value_ms2, is_speed=False):
        """
        Convert physics values to game pixel units.
        
        Args:
            value_ms2: Value in m/s² (acceleration) or m/s (speed)
            is_speed: If True, treats as speed; if False, as acceleration
        
        Returns:
            value in pixels/frame or pixels/frame²
        """
        if is_speed:
            # m/s -> pixels/frame
            pixels_per_second = value_ms2 * self.PIXELS_PER_METER
            pixels_per_frame = pixels_per_second / self.FPS
            return pixels_per_frame
        else:
            # m/s² -> pixels/frame²
            pixels_per_second2 = value_ms2 * self.PIXELS_PER_METER
            pixels_per_frame2 = pixels_per_second2 / (self.FPS ** 2)
            return pixels_per_frame2 * self.FPS  # Convert to per-frame increment
    
    def from_game_units(self, value_game, is_speed=False):
        """
        Convert game pixel units to physics values.
        
        Args:
            value_game: Value in pixels/frame or pixels/frame²
            is_speed: If True, treats as speed; if False, as acceleration
        
        Returns:
            value in m/s or m/s²
        """
        if is_speed:
            # pixels/frame -> m/s
            pixels_per_second = value_game * self.FPS
            meters_per_second = pixels_per_second / self.PIXELS_PER_METER
            return meters_per_second
        else:
            # pixels/frame² -> m/s²
            # This is acceleration per frame, so:
            value_per_frame2 = value_game / self.FPS
            pixels_per_second2 = value_per_frame2 * (self.FPS ** 2)
            meters_per_second2 = pixels_per_second2 / self.PIXELS_PER_METER
            return meters_per_second2
    
    def get_stats_summary(self):
        """Get a summary of vehicle performance characteristics."""
        return {
            'total_mass_kg': self.total_mass_kg,
            'power_hp': self.power_hp,
            'power_to_weight': self.power_to_weight,
            'top_speed_kmh': self.calculate_top_speed_kmh(),
            'accel_0_100_time': self.get_acceleration_0_to_100_time(),
            'power_watts': self.power_watts,
            'drag_coefficient': self.k_drag,
            'rolling_resistance_n': self.f_rolling,
            'max_traction_n': self.f_traction_max
        }
