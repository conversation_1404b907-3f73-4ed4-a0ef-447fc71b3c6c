#!/bin/bash
set -e

# Configuration
SOURCE_DIR="$(pwd)/repo"
BRANCH="gh-pages"

# Ensure we are in the project root
if [ ! -d ".git" ]; then
    echo "❌ Error: Please run this script from the project root."
    exit 1
fi

ORIGIN_URL=$(git remote get-url origin)
COMMIT_HASH=$(git rev-parse --short HEAD)

if [ ! -d "$SOURCE_DIR" ]; then
    echo "❌ Error: Directory '$SOURCE_DIR' not found."
    echo "   Run './packaging/scripts/build_packages.sh' first to generate the repository."
    exit 1
fi

echo "🚀 Deploying to GitHub Pages..."
echo "   Source: $SOURCE_DIR"
echo "   Branch: $BRANCH"
echo "   Remote: $ORIGIN_URL"

# Create temporary directory
TEMP_DIR=$(mktemp -d)
echo "📂 Created temp dir: $TEMP_DIR"

# Initialize git in temp dir
cd "$TEMP_DIR"
git init
git remote add origin "$ORIGIN_URL"

# Try to fetch the branch
if git ls-remote --exit-code origin "$BRANCH" &>/dev/null; then
    echo "📥 Fetching existing $BRANCH..."
    git fetch origin "$BRANCH" --depth=1
    git checkout "$BRANCH"
    # Clean directory except .git
    find . -maxdepth 1 ! -name '.git' ! -name '.' -exec rm -rf {} +
else
    echo "✨ Branch $BRANCH does not exist. Creating orphan branch..."
    git checkout --orphan "$BRANCH"
fi

# Copy files
echo "📋 Copying files..."
cp -r "$SOURCE_DIR"/* .

# Add .nojekyll (Crucial for some package structures to prevent Jekyll from ignoring folders starting with _)
touch .nojekyll

# Commit
git add .
if git diff --staged --quiet; then
    echo "⚠️ No changes to deploy."
else
    git commit -m "Deploy repo from commit $COMMIT_HASH"
    echo "📤 Pushing to origin..."
    git push origin "$BRANCH"
fi

# Cleanup
cd - > /dev/null
rm -rf "$TEMP_DIR"

echo "✅ Deployment complete!"
