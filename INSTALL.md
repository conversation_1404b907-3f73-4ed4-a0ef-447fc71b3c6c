# Installation Guide 💿

This document provides comprehensive instructions for installing **CA-Racing** on Linux and Windows systems.

## 🐧 Linux Installation

We provide native packages for major Linux distributions to ensure seamless integration with your system.

### Arch Linux (AUR)

Users of Arch Linux and its derivatives (Manjaro, EndeavourOS) can install `ca-racing` from the AUR using an AUR helper like `yay` or `paru`:

```bash
yay -S ca-racing
```

### Debian / Ubuntu

For Debian-based distributions, download the `.deb` package from the [Releases](https://github.com/piotrek1372/ca-racing/releases) page and install it using `apt`:

1.  **Download** the latest `.deb` file (e.g., `ca-racing_0.1.0-alpha_amd64.deb`).
2.  **Install** via terminal:
    ```bash
    sudo apt install ./ca-racing_*.deb
    ```

### Fedora / RHEL / CentOS

For RPM-based distributions, download the `.rpm` package from the [Releases](https://github.com/piotrek1372/ca-racing/releases) page and install it using `dnf`:

1.  **Download** the latest `.rpm` file (e.g., `ca-racing-0.1.0-alpha.x86_64.rpm`).
2.  **Install** via terminal:
    ```bash
    sudo dnf install ./ca-racing-*.rpm
    ```

---

## 🪟 Windows Installation

We provide a standard installer for Windows users.

1.  **Download** the Windows installer (e.g., `CA-Racing-Setup.exe`) from the [Releases](https://github.com/piotrek1372/ca-racing/releases) page.
2.  **Run** the executable file. You may need to grant permission if prompted by User Account Control (UAC).
3.  Follow the on-screen instructions in the setup wizard to complete the installation.
4.  Once installed, you can launch the game via the **Start Menu** or the **Desktop Shortcut**.

---

## 🔧 Troubleshooting

### Dependencies
The provided packages (`.deb`, `.rpm`, `.exe`) are configured to handle necessary dependencies automatically. No additional manual setup is required.

### Support
If you encounter any issues during installation or gameplay, please report them on our [GitHub Issue Tracker](https://github.com/piotrek1372/ca-racing/issues).
