# ======================================
# CA-Racing Multiplayer - Docker Compose
# ======================================
# Local development and testing stack
#
# Usage:
#   docker-compose up -d          # Start in background
#   docker-compose logs -f        # View logs
#   docker-compose down           # Stop and remove containers

version: '3.8'

services:
  # ======================================
  # CA-Racing Game Server
  # ======================================
  ca-racing-server:
    build:
      context: .
      dockerfile: Dockerfile
    image: ca-racing-server:latest
    container_name: ca-racing-server
    ports:
      - "5555:5555"
    environment:
      - SERVER_HOST=0.0.0.0
      - SERVER_PORT=5555
      - PYTHONUNBUFFERED=1
    restart: unless-stopped
    networks:
      - ca-racing-network
    healthcheck:
      test: ["CMD", "python", "-c", "import socket; s = socket.socket(); s.settimeout(5); s.connect(('127.0.0.1', 5555)); s.close()"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 5s
    # Resource limits (adjust based on your server capacity)
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G
    # Logging configuration
    logging:
      driver: "json-file"
      options:
        max-size: "10m"
        max-file: "3"

  # ======================================
  # Test Client (Optional - for testing)
  # ======================================
  # Uncomment to run a test client container
  # test-client:
  #   build:
  #     context: .
  #     dockerfile: Dockerfile.client
  #   container_name: ca-racing-test-client
  #   depends_on:
  #     - ca-racing-server
  #   environment:
  #     - SERVER_HOST=ca-racing-server
  #     - SERVER_PORT=5555
  #   networks:
  #     - ca-racing-network

networks:
  ca-racing-network:
    driver: bridge
    name: ca-racing-network
