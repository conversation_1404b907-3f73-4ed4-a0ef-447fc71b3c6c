import pygame
import json
from typing import List, Tuple

class NavigationManager:
    def __init__(self, nav_file_path: str):
        with open(nav_file_path, 'r') as f:
            nav_data = json.load(f)

        # Support both old format (path/width) and new format (waypoints)
        if 'path' in nav_data:
            self.path: List[pygame.Vector2] = [pygame.Vector2(p[0], p[1]) for p in nav_data['path']]
            self.width: List[float] = nav_data['width']
        elif 'waypoints' in nav_data:
            # Convert waypoints format to path format
            waypoints = nav_data['waypoints']
            self.path: List[pygame.Vector2] = [pygame.Vector2(wp['x'], wp['y']) for wp in waypoints]
            # Use default width if not specified
            self.width: List[float] = [50.0] * len(self.path)  # Default track width
        else:
            raise ValueError(f"Invalid navigation file format. Expected 'path' or 'waypoints' key.")

    def get_closest_point(self, pos: pygame.Vector2) -> Tuple[pygame.Vector2, int]:
        min_dist_sq = float('inf')
        closest_point_idx = -1
        for i, p in enumerate(self.path):
            dist_sq = pos.distance_squared_to(p)
            if dist_sq < min_dist_sq:
                min_dist_sq = dist_sq
                closest_point_idx = i
        return self.path[closest_point_idx], closest_point_idx

    def check_off_track(self, car_pos: pygame.Vector2) -> bool:
        closest_point, closest_point_idx = self.get_closest_point(car_pos)
        distance = car_pos.distance_to(closest_point)
        track_width_at_point = self.width[closest_point_idx]
        return distance > track_width_at_point * 1.2

class FadeManager:
    def __init__(self, screen_size: Tuple[int, int]):
        self.screen_size = screen_size
        self.fade_surface = pygame.Surface(self.screen_size)
        self.fade_surface.fill((0, 0, 0))
        self.alpha = 0
        self.fade_surface.set_alpha(self.alpha)
        self.fading_out = False
        self.fading_in = False
        self.fade_duration = 0.5  # seconds
        self.fade_timer = 0.0

    def start_fade_out(self):
        self.fading_out = True
        self.fading_in = False
        self.fade_timer = 0.0

    def start_fade_in(self):
        self.fading_in = True
        self.fading_out = False
        self.fade_timer = self.fade_duration

    def update(self, dt: float):
        if self.fading_out:
            self.fade_timer += dt
            if self.fade_timer >= self.fade_duration:
                self.fade_timer = self.fade_duration
                self.fading_out = False
        elif self.fading_in:
            self.fade_timer -= dt
            if self.fade_timer <= 0:
                self.fade_timer = 0
                self.fading_in = False
        
        self.alpha = (self.fade_timer / self.fade_duration) * 255
        
        self.fade_surface.set_alpha(self.alpha)

    def draw(self, screen: pygame.Surface):
        screen.blit(self.fade_surface, (0, 0))

    def is_fading(self) -> bool:
        return self.fading_in or self.fading_out

    def is_fully_faded(self) -> bool:
        return self.fade_timer == self.fade_duration and not self.fading_in
