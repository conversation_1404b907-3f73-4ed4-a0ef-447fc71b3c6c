#!/bin/bash
# Skrypt do tworzenia AppImage i wrappera uruchomieniowego dla CA-Racing
# Autor: Kilo Code
# Język: Bash

set -e # Zak<PERSON><PERSON>cz natychmiast w przypadku błędu

# --- Konfiguracja ---
# Robustly determine project root relative to this script
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/.." && pwd )"
DIST_DIR="${PROJECT_ROOT}/dist/CA-Racing"
PACKAGING_DIR="${PROJECT_ROOT}/packaging"
OUTPUT_NAME="CA-Racing"
ARCH="x86_64"
TOOL_URL="https://github.com/AppImage/appimagetool/releases/download/continuous/appimagetool-x86_64.AppImage"
TOOL_PATH="${PACKAGING_DIR}/appimagetool-x86_64.AppImage"

# Kolory do komunikatów dla lepszej czytelności
GREEN='\033[0;32m'
RED='\033[0;31m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}=========================================${NC}"
echo -e "${BLUE}   Kreator AppImage dla CA-Racing        ${NC}"
echo -e "${BLUE}=========================================${NC}"

# 1. Weryfikacja katalogu wejściowego AppDir (./dist)
echo -e "${YELLOW}[INFO] Weryfikacja struktury katalogu źródłowego: $DIST_DIR...${NC}"

if [ ! -d "$DIST_DIR" ]; then
    echo -e "${RED}[BŁĄD] Katalog $DIST_DIR nie istnieje!${NC}"
    echo -e "Uruchom najpierw skrypt budujący dystrybucję (np. build_dist.sh), aby przygotować pliki."
    exit 1
fi

# Sprawdzenie wymaganych plików metadanych
MISSING_FILES=0

if [ ! -f "$DIST_DIR/AppRun" ]; then
    echo -e "${RED}[BŁĄD] Brak pliku wykonywalnego 'AppRun' w $DIST_DIR.${NC}"
    MISSING_FILES=1
fi

if ! ls "$DIST_DIR"/*.desktop 1> /dev/null 2>&1; then
    echo -e "${RED}[BŁĄD] Brak pliku definicji '*.desktop' w $DIST_DIR.${NC}"
    MISSING_FILES=1
fi

if ! ls "$DIST_DIR"/*.png 1> /dev/null 2>&1; then
    echo -e "${YELLOW}[OSTRZEŻENIE] Nie znaleziono pliku ikony (.png) w głównym katalogu $DIST_DIR. AppImage może nie mieć ikony.${NC}"
    # To jest tylko ostrzeżenie, nie przerywamy
fi

if [ $MISSING_FILES -eq 1 ]; then
    echo -e "${RED}[STOP] Napraw brakujące pliki przed kontynuacją.${NC}"
    exit 1
fi

echo -e "${GREEN}[OK] Struktura katalogu ./dist wygląda poprawnie.${NC}"

# 2. Sprawdzenie i pobranie appimagetool
echo -e "${YELLOW}[INFO] Sprawdzanie narzędzia appimagetool...${NC}"

if [ ! -f "$TOOL_PATH" ]; then
    echo -e "${YELLOW}[INFO] Nie znaleziono appimagetool w $PACKAGING_DIR. Pobieranie...${NC}"
    wget -q "$TOOL_URL" -O "$TOOL_PATH"
    
    if [ $? -ne 0 ]; then
        echo -e "${RED}[BŁĄD] Nie udało się pobrać appimagetool. Sprawdź połączenie internetowe.${NC}"
        exit 1
    fi
    
    chmod +x "$TOOL_PATH"
    echo -e "${GREEN}[OK] Pobranno appimagetool do $PACKAGING_DIR.${NC}"
else
    echo -e "${GREEN}[OK] Używanie istniejącego appimagetool: $TOOL_PATH${NC}"
fi

# 3. Generowanie AppImage
echo -e "${YELLOW}[INFO] Generowanie pliku .AppImage...${NC}"

# Ustawienie zmiennej ARCH jest wymagane przez appimagetool
export ARCH="$ARCH"

# Uruchomienie narzędzia
# appimagetool [source] [destination]
"$TOOL_PATH" "$DIST_DIR" "${PROJECT_ROOT}/${OUTPUT_NAME}-${ARCH}.AppImage"

if [ $? -eq 0 ]; then
    echo -e "${GREEN}[SUKCES] Utworzono plik obrazu: ${OUTPUT_NAME}-${ARCH}.AppImage${NC}"
else
    echo -e "${RED}[BŁĄD] Nie udało się utworzyć AppImage. Sprawdź logi powyżej.${NC}"
    exit 1
fi

# 4. Tworzenie pliku .run (Wrapper)
RUN_FILE="${PROJECT_ROOT}/${OUTPUT_NAME}.run"
echo -e "${YELLOW}[INFO] Tworzenie wrappera uruchomieniowego: $RUN_FILE...${NC}"

cat > "$RUN_FILE" <<EOF
#!/bin/bash
# Wrapper uruchomieniowy dla CA-Racing AppImage
# Ten plik służy do łatwego uruchamiania gry bez konieczności znania pełnej nazwy pliku AppImage.

# Pobierz ścieżkę do katalogu, w którym znajduje się ten skrypt
DIR="\$( cd "\$( dirname "\${BASH_SOURCE[0]}" )" && pwd )"
APPIMAGE="\${DIR}/${OUTPUT_NAME}-${ARCH}.AppImage"

# Sprawdź czy AppImage istnieje
if [ -f "\$APPIMAGE" ]; then
    echo "Uruchamianie CA-Racing..."
    # Przekazanie wszystkich argumentów (\$@) do AppImage
    "\$APPIMAGE" "\$@"
else
    echo "Błąd: Nie znaleziono pliku gry: \$APPIMAGE"
    echo "Upewnij się, że plik .run i .AppImage znajdują się w tym samym katalogu."
    exit 1
fi
EOF

# 5. Finalizacja i uprawnienia
chmod +x "$RUN_FILE"
chmod +x "${PROJECT_ROOT}/${OUTPUT_NAME}-${ARCH}.AppImage"

echo -e "${BLUE}=========================================${NC}"
echo -e "${GREEN}   ZAKOŃCZONO SUKCESEM                   ${NC}"
echo -e "${BLUE}=========================================${NC}"
echo -e "Wygenerowane pliki:"
echo -e "1. Obraz aplikacji:  ${GREEN}./${OUTPUT_NAME}-${ARCH}.AppImage${NC}"
echo -e "2. Skrypt startowy:  ${GREEN}./${RUN_FILE}${NC}"
echo -e ""
echo -e "Aby uruchomić grę, wpisz: ./${RUN_FILE}"
