#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
The main entry point for the CA-Racing game.

This script initializes the environment, configures logging, and starts
the main application loop. It handles path configuration for both
source execution and frozen (PyInstaller) builds.
"""

import logging
import os
import sys
from typing import NoReturn

# Configure logging
from src.core.path import get_user_data_dir
from src.core.resource_path import resource_path, print_environment_info

def setup_logging():
    """
    Configures logging to output to a file in the user data directory
    and to the console.
    """
    log_dir = get_user_data_dir()
    log_file_path = os.path.join(log_dir, 'game_log.txt')

    # Ensure log directory exists
    if not os.path.exists(log_dir):
        os.makedirs(log_dir, exist_ok=True)

    # Basic configuration for file logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_file_path, encoding='utf-8'),
            logging.StreamHandler(sys.stdout) # Also log to console
        ]
    )
    # Set a more permissive level for console for initial debugging
    logging.getLogger().handlers[1].setLevel(logging.DEBUG)
    
    logger.info(f"Logging to file: {log_file_path}")

import logging # Dodany import
 
logger = logging.getLogger(__name__) # Przeniesiona definicja loggera
 
# Configure logging
setup_logging()


def setup_path() -> None:
    """
    Add the 'src' directory to the system path if running from source.
    
    This is crucial for imports from 'src' to work correctly.
    In frozen mode (PyInstaller), sys.path is already handled correctly.
    """
    if not getattr(sys, 'frozen', False):
        current_dir = os.path.dirname(os.path.abspath(__file__))
        src_path = os.path.join(current_dir, 'src')
        if src_path not in sys.path:
            sys.path.insert(0, src_path)


def main() -> NoReturn:
    """
    Main function to initialize and run the application.
    
    Raises:
        SystemExit: Always raised when the application closes.
    """
    try:
        setup_path()
        
        # The import is placed inside the function to ensure the system
        # path has been modified.
        from src.core.app import GameApp
        from src.constants import BASE_DIR, ASSETS_DIR, DATA_DIR, USER_DATA_DIR

        logger.info("Starting CA-Racing...")
        logger.info("Starting CA-Racing...")
        logger.info(f"BASE_DIR: {BASE_DIR}")
        logger.info(f"ASSETS_DIR: {ASSETS_DIR}")
        logger.info(f"DATA_DIR: {DATA_DIR}")
        logger.info(f"USER_DATA_DIR: {USER_DATA_DIR}")
        print_environment_info() # This prints PyInstaller specific paths

        app = GameApp()
        app.run()

    except Exception as e:
        logger.critical(f"An unexpected critical error occurred: {e}", exc_info=True)
        sys.exit(1)
    
    sys.exit(0)


if __name__ == "__main__":
    main()
