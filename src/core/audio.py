import pygame as pg

class AudioManager:
    """
    Handles music streaming and SFX playback.
    Integrates with global settings for volume control.
    Credits: SFX by <PERSON><PERSON>her<PERSON>.
    
    ✅ FIXED: Removed mixer pause/unpause that was causing noise and restart issues.
    """
    def __init__(self, app):
        self.app = app
        self.assets = app.assets
        self.music_volume = 0.5
        self.sfx_volume = 0.5
        
        # Public volume properties for easy access
        self.VOLUME_MUSIC = 0.5
        self.VOLUME_SFX = 0.8 # Increased default SFX volume for better audibility
        
        # Load initial volume from settings
        self.load_volume_settings()
        
        # Current track tracking
        self.current_music = None

    def load_volume_settings(self):
        """Updates internal volume variables from global settings."""
        settings = self.app.global_settings
        self.music_volume = settings.get("vol_music", 50) / 100.0
        self.sfx_volume = settings.get("vol_sfx", 80) / 100.0 # Default to 80% for SFX
        
        self.VOLUME_MUSIC = self.music_volume
        self.VOLUME_SFX = self.sfx_volume
        
        # Apply immediately
        pg.mixer.music.set_volume(self.music_volume)
        
    def play_music(self, track_key):
        """Streams music from disk."""
        path = self.assets['music'].get(track_key)
        if not path:
            print(f"[AUDIO] Music track not found: {track_key}")
            return

        if self.current_music == track_key and pg.mixer.music.get_busy():
            return # Already playing this track

        try:
            pg.mixer.music.load(path)
            pg.mixer.music.play(-1) # Loop indefinitely
            pg.mixer.music.set_volume(self.music_volume)
            self.current_music = track_key
            print(f"[AUDIO] Playing music: {track_key}")
        except Exception as e:
            print(f"[AUDIO] Error playing music: {e}")

    def play_sfx(self, sfx_key):
        """Plays a sound effect if loaded."""
        sound = self.assets['sfx'].get(sfx_key)
        if sound:
            sound.set_volume(self.sfx_volume)
            sound.play()
        else:
            # Silent fail or debug print
            pass

    def set_music_volume(self, percent):
        """Sets music volume (0-100) and saves to settings."""
        self.app.global_settings["vol_music"] = percent
        self.music_volume = percent / 100.0
        pg.mixer.music.set_volume(self.music_volume)
        self.app.data_manager.save_global_settings(self.app.global_settings)

    def set_sfx_volume(self, percent):
        """
        Sets SFX volume (0-100) and saves to settings.
        
        ✅ FIXED: Removed pg.mixer.pause()/unpause() which was causing:
        - Static noise when resuming
        - Engine sound restart issues
        - Channel desynchronization
        
        Now uses soft volume control - channels keep playing but are silent.
        The CarAudioController handles the actual muting via volume=0.
        """
        self.app.global_settings["vol_sfx"] = percent
        self.sfx_volume = percent / 100.0
        self.VOLUME_SFX = self.sfx_volume
        
        # ✅ REMOVED problematic pause/unpause logic
        # CarAudioController now handles muting via volume control
        # This prevents static noise and restart issues
            
        self.app.data_manager.save_global_settings(self.app.global_settings)

    def stop_all_sfx(self):
        """
        Stops all SFX channels except engine sounds.
        
        ✅ FIXED: More selective stopping to avoid interfering with engine.
        Engine channels (0-7) are managed by CarAudioController.
        This only stops UI/menu SFX channels (8+).
        """
        # Stop only non-engine channels (UI/menu sounds)
        total_channels = pg.mixer.get_num_channels()
        for channel_id in range(8, total_channels):  # Skip engine channels 0-7
            channel = pg.mixer.Channel(channel_id)
            if channel.get_busy():
                channel.stop()

    def set_volume(self, category, level):
        """
        Sets volume for a specific category.
        Args:
            category: 'music' or 'sfx'
            level: 0-100
        """
        if category == 'music':
            self.set_music_volume(level)
        elif category == 'sfx':
            self.set_sfx_volume(level)