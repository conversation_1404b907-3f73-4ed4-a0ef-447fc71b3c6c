import pygame as pg
from src.constants import *
from src.ui.widgets import <PERSON>ton
from src.core.assets import get_car_sprite
from src.core.game_data import (
    get_all_tracks,
    get_track_data,
    get_track_display_name,
    get_all_opponents,
    get_opponent_data,
    get_opponent_display_name
)


class RaceSelectMenu:
    """
    Race selection menu where players choose track and opponent before racing.
    """

    def __init__(self, app, player, start_race_callback, return_callback):
        self.app = app
        self.player = player
        self.start_race_callback = start_race_callback
        self.return_callback = return_callback
        self.screen = app.screen

        # Fonts
        self.title_font = pg.font.SysFont('Consolas', 50, bold=True)
        self.info_font = pg.font.SysFont('Consolas', 24)
        self.small_font = pg.font.SysFont('Consolas', 18)

        # Selection state
        self.selected_track = None
        self.selected_opponent = None
        self.tracks = get_all_tracks()
        self.opponents = get_all_opponents()

        if self.tracks:
            self.selected_track = self.tracks[0]
        if self.opponents:
            self.selected_opponent = self.opponents[0]

        # UI elements
        self.buttons = []
        self.track_slots = []  # (rect, track_id)
        self.opponent_slots = []  # (rect, opponent_id)

        # Feedback
        self.feedback_message = ""
        self.feedback_color = TEXT_MAIN
        self.feedback_timer = 0

        self.init_ui()

    def init_ui(self):
        """Initialize buttons and UI elements."""
        self.buttons = []
        cx = self.screen.get_width() // 2
        h = self.screen.get_height()

        # Back button
        self.buttons.append(Button(
            self.app.lang.get("menu_back"),
            (150, h - 50),
            self._on_back,
            app=self.app,
            custom_color=ACCENT_RED
        ))

        # Start Race button
        self.buttons.append(Button(
            self.app.lang.get("race_start"),
            (cx, h - 50),
            self._on_start_race,
            app=self.app,
            custom_color=ACCENT_GREEN
        ))

    def _on_back(self):
        self.app.audio.play_sfx('ui_back')
        self.return_callback()

    def _on_start_race(self):
        # Check if player has a car selected
        if not self.player.current_car:
            self._show_feedback(self.app.lang.get("race_no_car"), ACCENT_RED)
            self.app.audio.play_sfx('ui_error')
            return

        if self.selected_track and self.selected_opponent:
            self.app.audio.play_sfx('ui_select')
            self.start_race_callback(self.selected_track, self.selected_opponent)

    def _show_feedback(self, message, color=TEXT_MAIN):
        self.feedback_message = message
        self.feedback_color = color
        self.feedback_timer = 120

    def update(self, event):
        """Handle input events."""
        for btn in self.buttons:
            btn.handle_event(event)

        if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
            self._handle_click(event.pos)

    def _handle_click(self, mouse_pos):
        """Handle clicks on selection slots."""
        # Check track slots
        for rect, track_id in self.track_slots:
            if rect.collidepoint(mouse_pos):
                if self.selected_track != track_id:
                    self.selected_track = track_id
                    self.app.audio.play_sfx('ui_hover')
                return

        # Check opponent slots
        for rect, opp_id in self.opponent_slots:
            if rect.collidepoint(mouse_pos):
                if self.selected_opponent != opp_id:
                    self.selected_opponent = opp_id
                    self.app.audio.play_sfx('ui_hover')
                return

    def draw(self, screen):
        """Main draw method."""
        screen.fill(BG_COLOR)
        self.track_slots = []
        self.opponent_slots = []

        # Update feedback timer
        if self.feedback_timer > 0:
            self.feedback_timer -= 1
            if self.feedback_timer <= 0:
                self.feedback_message = ""

        # Title
        title_surf = self.title_font.render(self.app.lang.get("race_title"), True, TEXT_MAIN)
        title_rect = title_surf.get_rect(center=(screen.get_width() // 2, 50))
        screen.blit(title_surf, title_rect)

        # Draw track selection (left side)
        self._draw_track_selection(screen)

        # Draw opponent selection (right side)
        self._draw_opponent_selection(screen)

        # Draw race info panel (bottom center)
        self._draw_race_info(screen)

        # Feedback message
        if self.feedback_message:
            fb_surf = self.info_font.render(self.feedback_message, True, self.feedback_color)
            fb_rect = fb_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() - 120))
            screen.blit(fb_surf, fb_rect)

        # Buttons
        for btn in self.buttons:
            btn.draw(screen)

    def _draw_track_selection(self, screen):
        """Draw track selection panel on the left."""
        mouse_pos = pg.mouse.get_pos()

        # Panel
        panel_x = 50
        panel_y = 100
        panel_w = 400
        panel_h = 380

        pg.draw.rect(screen, PANEL_BG, (panel_x, panel_y, panel_w, panel_h), border_radius=10)
        pg.draw.rect(screen, TEXT_DIM, (panel_x, panel_y, panel_w, panel_h), 2, border_radius=10)

        # Title
        title = self.app.lang.get("race_select_track")
        title_surf = self.info_font.render(title, True, TEXT_MAIN)
        screen.blit(title_surf, (panel_x + 15, panel_y + 10))

        # Track slots
        slot_y = panel_y + 50
        for track_id in self.tracks:
            track_data = get_track_data(track_id)
            if not track_data:
                continue

            slot_rect = pg.Rect(panel_x + 15, slot_y, panel_w - 30, 100)
            is_selected = track_id == self.selected_track
            is_hovered = slot_rect.collidepoint(mouse_pos)

            # Background
            bg_color = ACCENT_BLUE if is_selected else (BUTTON_HOVER_COLOR if is_hovered else (60, 60, 70))
            pg.draw.rect(screen, bg_color, slot_rect, border_radius=8)
            pg.draw.rect(screen, ACCENT_GOLD if is_selected else TEXT_DIM, slot_rect, 2, border_radius=8)

            # Track name
            name = get_track_display_name(track_id, self.app.lang)
            name_surf = self.info_font.render(name, True, TEXT_MAIN)
            screen.blit(name_surf, (slot_rect.x + 15, slot_rect.y + 10))

            # Difficulty
            diff = track_data.get('difficulty', 1)
            diff_text = "★" * diff + "☆" * (3 - diff)
            diff_surf = self.small_font.render(f"{self.app.lang.get('race_difficulty')}: {diff_text}", True, ACCENT_GOLD)
            screen.blit(diff_surf, (slot_rect.x + 15, slot_rect.y + 40))

            # Laps
            laps = track_data.get('laps', 3)
            laps_surf = self.small_font.render(f"{self.app.lang.get('race_laps')}: {laps}", True, TEXT_DIM)
            screen.blit(laps_surf, (slot_rect.x + 15, slot_rect.y + 65))

            # Base reward
            reward = track_data.get('base_reward', 500)
            reward_surf = self.small_font.render(f"${reward}", True, ACCENT_GREEN)
            screen.blit(reward_surf, (slot_rect.x + slot_rect.width - 80, slot_rect.y + 40))

            self.track_slots.append((slot_rect, track_id))
            slot_y += 110

    def _draw_opponent_selection(self, screen):
        """Draw opponent selection panel on the right."""
        mouse_pos = pg.mouse.get_pos()

        # Panel
        panel_x = screen.get_width() - 450
        panel_y = 100
        panel_w = 400
        panel_h = 380

        pg.draw.rect(screen, PANEL_BG, (panel_x, panel_y, panel_w, panel_h), border_radius=10)
        pg.draw.rect(screen, TEXT_DIM, (panel_x, panel_y, panel_w, panel_h), 2, border_radius=10)

        # Title
        title = self.app.lang.get("race_select_opponent")
        title_surf = self.info_font.render(title, True, TEXT_MAIN)
        screen.blit(title_surf, (panel_x + 15, panel_y + 10))

        # Opponent slots
        slot_y = panel_y + 50
        for opp_id in self.opponents:
            opp_data = get_opponent_data(opp_id)
            if not opp_data:
                continue

            slot_rect = pg.Rect(panel_x + 15, slot_y, panel_w - 30, 70)
            is_selected = opp_id == self.selected_opponent
            is_hovered = slot_rect.collidepoint(mouse_pos)

            # Background
            bg_color = ACCENT_BLUE if is_selected else (BUTTON_HOVER_COLOR if is_hovered else (60, 60, 70))
            pg.draw.rect(screen, bg_color, slot_rect, border_radius=8)
            pg.draw.rect(screen, ACCENT_GOLD if is_selected else TEXT_DIM, slot_rect, 2, border_radius=8)

            # Opponent name
            name = get_opponent_display_name(opp_id, self.app.lang)
            name_surf = self.info_font.render(name, True, TEXT_MAIN)
            screen.blit(name_surf, (slot_rect.x + 15, slot_rect.y + 10))

            # Skill indicator
            skill = opp_data.get('skill', 1.0)
            skill_bars = int(skill * 5)
            skill_text = "▮" * skill_bars + "▯" * (6 - skill_bars)
            skill_surf = self.small_font.render(skill_text, True, ACCENT_RED if skill >= 1.0 else ACCENT_GOLD)
            screen.blit(skill_surf, (slot_rect.x + 15, slot_rect.y + 40))

            # Reward multiplier
            mult = opp_data.get('reward_mult', 1.0)
            mult_surf = self.small_font.render(f"x{mult:.1f}", True, ACCENT_GREEN)
            screen.blit(mult_surf, (slot_rect.x + slot_rect.width - 60, slot_rect.y + 25))

            self.opponent_slots.append((slot_rect, opp_id))
            slot_y += 80

    def _draw_race_info(self, screen):
        """Draw race info panel showing selected race details."""
        if not self.selected_track or not self.selected_opponent:
            return

        track_data = get_track_data(self.selected_track)
        opp_data = get_opponent_data(self.selected_opponent)

        if not track_data or not opp_data:
            return

        # Calculate reward
        base_reward = track_data.get('base_reward', 500)
        mult = opp_data.get('reward_mult', 1.0)
        total_reward = int(base_reward * mult)

        # Info panel
        panel_x = 500
        panel_y = 500
        panel_w = screen.get_width() - 1000
        panel_h = 80

        pg.draw.rect(screen, PANEL_BG, (panel_x, panel_y, panel_w, panel_h), border_radius=10)
        pg.draw.rect(screen, ACCENT_GOLD, (panel_x, panel_y, panel_w, panel_h), 2, border_radius=10)

        # Reward display
        reward_label = self.app.lang.get("race_reward")
        reward_text = f"{reward_label}: ${total_reward}"
        reward_surf = self.info_font.render(reward_text, True, ACCENT_GREEN)
        reward_rect = reward_surf.get_rect(center=(panel_x + panel_w // 2, panel_y + 25))
        screen.blit(reward_surf, reward_rect)

        # Player car display
        if self.player.current_car:
            car_sprite = get_car_sprite(self.app.assets, self.player.current_car)
            if car_sprite:
                scaled = pg.transform.scale(car_sprite, (50, 50))
                screen.blit(scaled, (panel_x + 20, panel_y + 15))

