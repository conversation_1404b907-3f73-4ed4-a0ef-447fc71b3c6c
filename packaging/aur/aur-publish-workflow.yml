name: Publish to AUR

on:
  release:
    types: [published]

jobs:
  aur-publish:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3

      - name: Publish to AUR
        uses: KSXGitHub/github-actions-deploy-aur@v2.7.2
        with:
          pkgname: ca-racing
          pkgbuild: ./packaging/aur/PKGBUILD
          commit_username: ${{ secrets.AUR_USERNAME }}
          commit_email: ${{ secrets.AUR_EMAIL }}
          ssh_private_key: ${{ secrets.AUR_SSH_PRIVATE_KEY }}
          commit_message: "Update to v${{ github.event.release.tag_name }}"
          ssh_keyscan_types: rsa,dsa,ecdsa,ed25519
