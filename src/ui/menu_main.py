import pygame as pg
import threading
import asyncio
from src.game.multiplayer_session import MultiplayerSession
from src.utils.logger import logger
from src.ui.multiplayer_lobby import Multiplayer<PERSON><PERSON><PERSON>, setup_lobby_callbacks
from src.network.embedded_server import start_localhost_server, is_localhost_server_running
from src.constants import *
from src.ui.widgets import Button, InputBox

OFFICIAL_SERVER_IP = "************" # Official GCP Server IP

class MainMenu:
    def __init__(self, app):
        self.app = app
        self.state = "main"
        self.buttons = []
        self.connecting = False
        self.inputs = []
        self.title_font = pg.font.SysFont('Consolas', 72, bold=True)
        self.subtitle_font = pg.font.SysFont('Consolas', 24)
        self.init_main_view()

    def _get_center_x(self):
        return self.app.screen.get_width() // 2

    def init_main_view(self):
        self.state = "main"
        self.buttons = []
        self.inputs = []
        cx = self._get_center_x()
        start_y = 280
        gap = 80

        opts = [
            (self.app.lang.get("menu_play"), self.go_to_saves, ACCENT_GREEN),
            (self.app.lang.get("menu_options"), self.app.open_global_settings, None),
            (self.app.lang.get("menu_exit"), self.app.quit_game, ACCENT_RED)
        ]

        for i, (txt, act, col) in enumerate(opts):
            self.buttons.append(Button(txt, (cx, start_y + i*gap), act, app=self.app, custom_color=col))

    def init_save_view(self):
        self.state = "saves"
        self.buttons = []
        self.inputs = []
        cx = self._get_center_x()
        start_y = 220
        gap = 85
        
        slots = self.app.data_manager.check_save_slots()
        prefix = self.app.lang.get("slot_prefix")
        txt_new = self.app.lang.get("slot_new")
        
        for i in range(1, 4):
            slot_info = slots[i]
            occupied = slot_info['exists']
            player_name = slot_info['name']
            
            btn_txt = f"{prefix} {i}: {player_name}" if occupied else f"{prefix} {i} [{txt_new}]"
            col = ACCENT_BLUE if occupied else ACCENT_GREEN
            
            action = (lambda slot=i: self.init_slot_options_view(slot)) if occupied else (lambda slot=i: self.app.start_game_session(slot))
            self.buttons.append(Button(btn_txt, (cx, start_y + (i-1)*gap), action, app=self.app, custom_color=col))
            
        self.buttons.append(Button(self.app.lang.get("menu_back"), (cx, start_y + 3*gap + 30), self.init_main_view, app=self.app, custom_color=ACCENT_RED))

    def init_slot_options_view(self, slot_id):
        self.state = "slot_options"
        self.buttons = []
        self.inputs = []
        self.current_slot_id = slot_id
        cx = self._get_center_x()
        start_y = 220
        gap = 75
        
        self.buttons.append(Button("LOAD GAME", (cx, start_y),
            lambda: self.app.start_game_session(slot_id), app=self.app, custom_color=ACCENT_GREEN))
        self.buttons.append(Button("MULTIPLAYER", (cx, start_y + gap),
            lambda: self.init_multiplayer_view(slot_id), app=self.app, custom_color=ACCENT_BLUE))
        self.buttons.append(Button("HARD RESET", (cx, start_y + gap*2),
            lambda: self.perform_reset(slot_id), app=self.app, custom_color=ACCENT_RED))
        self.buttons.append(Button("BACK", (cx, start_y + gap*3),
            self.init_save_view, app=self.app, custom_color=TEXT_DIM))

    def init_multiplayer_view(self, slot_id):
        # Task 3: Remove 'Create Room' logic and buttons from UI flow
        self.state = "multiplayer"
        self.buttons = []
        self.inputs = []
        self.current_slot_id = slot_id
        cx = self._get_center_x()
        start_y = 220
        gap = 80
        
        # UI is simplified to only allow joining via Official Server or Manual IP
        self.buttons.append(Button("OFFICIAL SERVER", (cx, start_y),
            lambda: self.connect_to_ip(self.current_slot_id, OFFICIAL_SERVER_IP), app=self.app, custom_color=ACCENT_GOLD))
        self.buttons.append(Button("MANUAL IP", (cx, start_y + gap),
            lambda: self.init_manual_ip_view(self.current_slot_id), app=self.app, custom_color=ACCENT_BLUE))
        self.buttons.append(Button("BACK", (cx, start_y + 2 * gap),
            lambda: self.init_slot_options_view(self.current_slot_id), app=self.app, custom_color=TEXT_DIM))

    def init_manual_ip_view(self, slot_id):
        self.state = "manual_ip"
        self.buttons = []
        self.inputs = []
        cx = self._get_center_x()
        start_y = 200
        
        self.ip_input = InputBox((cx, start_y), "127.0.0.1", app=self.app)
        self.inputs.append(self.ip_input)
        
        self.buttons.append(Button("CONNECT", (cx, start_y + 80),
            lambda: self.connect_to_ip(slot_id, self.ip_input.text), app=self.app, custom_color=ACCENT_GREEN))
        self.buttons.append(Button("BACK", (cx, start_y + 160),
            lambda: self.init_multiplayer_view(slot_id), app=self.app, custom_color=TEXT_DIM))

    def connect_to_ip(self, slot_id, ip):
        # Use port 8444 for both localhost and remote servers
        port = 8444

        # Auto-start localhost server if connecting to localhost
        if ip in ['127.0.0.1', 'localhost']:
            if not is_localhost_server_running():
                logger.info("Starting localhost server automatically...")
                if start_localhost_server():
                    logger.info("✅ Localhost server started successfully")
                else:
                    logger.error("❌ Failed to start localhost server")
                    # Could show error message to user here
                    return
            else:
                logger.info("Localhost server is already running")

        logger.info(f"Connecting to {ip}:{port}")
        session = MultiplayerSession(self.app, slot_id, ip, port)
        self.app.multiplayer_session = session
        self.app.multiplayer_lobby = MultiplayerLobby(self.app.screen, session)
        setup_lobby_callbacks(session, self.app.multiplayer_lobby)
        
        self.app.state = 'MULTIPLAYER_LOBBY'
        self.app.multiplayer_connecting = True
        
        def connect_thread():
            try:
                success = session.connect_sync()
                self.app.multiplayer_connecting = False
                if success:
                    logger.info("✅ Connected to server!")
                    if self.app.multiplayer_lobby:
                        self.app.multiplayer_lobby.add_chat_message("✅ Connected!")
                else:
                    logger.error("❌ Connection failed")
                    if self.app.multiplayer_lobby:
                        self.app.multiplayer_lobby.add_chat_message("❌ Connection failed")
            except Exception as e:
                logger.error(f"Connection error: {e}")
                self.app.multiplayer_connecting = False
                if self.app.multiplayer_lobby:
                    self.app.multiplayer_lobby.add_chat_message(f"❌ Error: {e}")
        
        thread = threading.Thread(target=connect_thread, daemon=True)
        thread.start()
        logger.info("Connection thread started")

    def perform_reset(self, slot_id):
        self.app.data_manager.reset_save_slot(slot_id)
        self.init_save_view()

    def go_to_saves(self):
        self.init_save_view()

    def update(self, event):
        for btn in self.buttons:
            btn.handle_event(event)
        for inp in self.inputs:
            inp.handle_event(event)

    def draw(self, screen):
        screen.fill(BG_COLOR)
        cx = self._get_center_x()
        
        title_map = {
            "main": self.app.lang.get("title_main"),
            "saves": self.app.lang.get("title_slots"),
            "slot_options": f"SLOT {getattr(self, 'current_slot_id', '?')}",
            "multiplayer": "MULTIPLAYER",
            "manual_ip": "DIRECT CONNECT"
        }
        
        title = title_map.get(self.state, "")
        color = ACCENT_GOLD if self.state == "main" else (ACCENT_BLUE if "multiplayer" in self.state or "manual_ip" in self.state else TEXT_MAIN)
        
        # Shadow
        shadow_surf = self.title_font.render(title, True, (15, 15, 20))
        screen.blit(shadow_surf, shadow_surf.get_rect(center=(cx + 4, 124)))
        
        # Main title
        t_surf = self.title_font.render(title, True, color)
        screen.blit(t_surf, t_surf.get_rect(center=(cx, 120)))
        
        if self.state == "main":
            version_surf = self.subtitle_font.render(f"v{APP_VERSION}", True, TEXT_DIM)
            screen.blit(version_surf, version_surf.get_rect(center=(cx, 170)))
        
        for btn in self.buttons:
            btn.draw(screen)
        for inp in self.inputs:
            inp.draw(screen)