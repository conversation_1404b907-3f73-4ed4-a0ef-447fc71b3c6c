# <AUTHOR> <EMAIL>
# LOCAL BUILD VERSION - for testing before GitHub release
pkgname=ca-racing
_realver=0.1.0-alpha
pkgver=${_realver//-/_}
pkgrel=1
pkgdesc="A retro-style 2D top-down racing game"
arch=('x86_64')
url="https://github.com/piotrek1372/ca-racing"
license=('MIT')
depends=()
makedepends=()
provides=('ca-racing')
conflicts=('ca-racing')
# Use local file for testing
source=("file://${PWD}/../../CA-Racing-${_realver}-setup.run")
sha256sums=('SKIP')

package() {
    # Create directories
    install -dm755 "${pkgdir}/opt/ca-racing"
    install -dm755 "${pkgdir}/usr/share/applications"
    install -dm755 "${pkgdir}/usr/share/icons/hicolor/1024x1024/apps"
    
    # Extract and install game files
    cd "${srcdir}"
    chmod +x "CA-Racing-${_realver}-setup.run"
    
    # Manual extraction (since the installer requires root)
    # Extract the archive part into a temporary directory
    mkdir -p extracted
    ARCHIVE_LINE=$(awk '/^__ARCHIVE_BELOW__/ {print NR + 1; exit 0; }' "CA-Racing-${_realver}-setup.run")
    tail -n +${ARCHIVE_LINE} "CA-Racing-${_realver}-setup.run" | tar -xz -C extracted
    
    # Install files
    cp -r extracted/* "${pkgdir}/opt/ca-racing/"
    chmod +x "${pkgdir}/opt/ca-racing/CA-Racing"
    
    # Install icon
    install -Dm644 "extracted/icon.png" \
        "${pkgdir}/usr/share/icons/hicolor/1024x1024/apps/ca-racing.png"
    
    # Install desktop file
    cat > "${pkgdir}/usr/share/applications/ca-racing.desktop" << 'EOF'
[Desktop Entry]
Type=Application
Name=CA-Racing 0.1.0-alpha
GenericName=Racing Game
Comment=A retro-style 2D top-down racing game (Alpha version)
Exec=/opt/ca-racing/CA-Racing
Icon=ca-racing
Categories=Game;ArcadeGame;
Terminal=false
StartupNotify=true
Path=/opt/ca-racing
EOF
}
