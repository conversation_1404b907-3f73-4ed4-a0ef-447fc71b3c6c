"""
CA-Racing Network Client Handler
=================================
Handles client-side networking with client-side prediction and server reconciliation.

Key Concepts:
- Client predicts movement locally for responsive controls
- Stores state buffer of predicted states
- Server sends authoritative state updates
- <PERSON><PERSON> reconciles by replaying inputs from acknowledged sequence
"""

import asyncio
import json
import time
from typing import Optional, Dict, List, Callable
from dataclasses import dataclass
import pygame as pg
import logging

logger = logging.getLogger(__name__)


@dataclass
class InputState:
    """
    Represents a single input frame that was sent to server.
    Used for reconciliation - we need to replay these if server disagrees.
    """
    sequence_number: int
    throttle: float
    steering: float
    is_braking: float
    timestamp: float


@dataclass
class PredictedState:
    """
    Local predicted state at a specific sequence number.
    If server state differs, we rewind to this and replay.
    """
    sequence_number: int
    x: float
    y: float
    angle: float
    velocity_x: float
    velocity_y: float


class NetworkClient:
    """
    Asynchronous network client with client-side prediction.
    
    Why Reconciliation is Needed:
    -----------------------------
    Due to network latency, the client predicts movement before the server confirms it.
    If the client prediction differs from server's authoritative calculation
    (due to lag, packet loss, or physics differences), we must correct it.
    
    The reconciliation process:
    1. Client sends input with sequence number
    2. Client immediately predicts result locally
    3. Server processes input and sends back authoritative state
    4. Client checks if prediction matches server state
    5. If different, client "rewinds" to server state and replays all inputs
       that came after the acknowledged sequence
    """
    
    INPUT_BUFFER_SIZE = 60  # Keep last 1 second of inputs at 60 FPS
    
    def __init__(self, host: str = '0.0.0.0', port: int = 5555):
        self.host = host
        self.port = port
        
        # Connection state
        self.reader: Optional[asyncio.StreamReader] = None
        self.writer: Optional[asyncio.StreamWriter] = None
        self.connected = False
        
        # Player state
        self.player_id: Optional[str] = None
        self.room_id: Optional[int] = None
        
        # Prediction & Reconciliation
        self.sequence_number = 0
        self.input_buffer: List[InputState] = []  # Pending inputs
        self.predicted_state: Optional[PredictedState] = None
        self.last_server_sequence = -1
        
        # Network stats
        self.rtt = 0.0  # Round-trip time (ms)
        self.last_ping_time = 0.0
        
        # Callbacks for game logic
        self.on_game_state: Optional[Callable] = None
        self.on_race_start: Optional[Callable] = None
        self.on_player_joined: Optional[Callable] = None
        
        # Receive task
        self.receive_task: Optional[asyncio.Task] = None
    
    async def connect(self) -> bool:
        """Connect to game server"""
        try:
            self.reader, self.writer = await asyncio.open_connection(
                self.host, self.port
            )
            self.connected = True
            logger.info(f"Connected to server at {self.host}:{self.port}")
            
            # Start receive loop
            self.receive_task = asyncio.create_task(self._receive_loop())
            
            return True
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    async def disconnect(self) -> None:
        """Disconnect from server"""
        self.connected = False
        
        if self.receive_task:
            self.receive_task.cancel()
            try:
                await self.receive_task
            except asyncio.CancelledError:
                pass
        
        if self.writer:
            try:
                self.writer.close()
                await self.writer.wait_closed()
            except Exception:
                pass
        
        logger.info("Disconnected from server")
    
    async def send_ready(self) -> None:
        """Signal ready to start race"""
        await self._send_message({
            'type': 'player_ready',
            'timestamp': time.time()
        })
    
    async def send_input(
        self,
        throttle: float,
        steering: float,
        is_braking: float
    ) -> None:
        """
        Send input to server and predict local state.
        
        This is the core of client-side prediction:
        1. Increment sequence number
        2. Send input to server
        3. Store input for potential replay
        4. Apply input locally (predict)
        """
        self.sequence_number += 1
        
        # Create input state
        input_state = InputState(
            sequence_number=self.sequence_number,
            throttle=throttle,
            steering=steering,
            is_braking=is_braking,
            timestamp=time.time()
        )
        
        # Store in buffer for reconciliation
        self.input_buffer.append(input_state)
        
        # Trim buffer to size limit
        if len(self.input_buffer) > self.INPUT_BUFFER_SIZE:
            self.input_buffer.pop(0)
        
        # Send to server
        await self._send_message({
            'type': 'player_input',
            'throttle': throttle,
            'steering': steering,
            'is_braking': is_braking,
            'sequence_number': self.sequence_number,
            'timestamp': time.time()
        })
    
    def predict_state(
        self,
        car_instance,
        dt: float
    ) -> None:
        """
        Apply local prediction to car instance.
        
        This runs the same physics as the server would, but locally
        for immediate visual feedback. Stores the predicted state
        for later reconciliation.
        
        Args:
            car_instance: The Car object to update
            dt: Delta time
        """
        # Store prediction
        self.predicted_state = PredictedState(
            sequence_number=self.sequence_number,
            x=car_instance.x,
            y=car_instance.y,
            angle=car_instance.angle,
            velocity_x=car_instance.velocity.x,
            velocity_y=car_instance.velocity.y
        )
    
    def reconcile_state(
        self,
        car_instance,
        server_state: Dict,
        dt: float
    ) -> None:
        """
        Reconcile local prediction with authoritative server state.
        
        **Why This is Critical:**
        Network latency means our prediction might be wrong. The server
        has the authoritative state. If they differ, we must correct our
        position by:
        1. Setting car to server's confirmed state
        2. Replaying all inputs that came AFTER server's sequence
        
        This prevents rubber-banding while still allowing smooth local prediction.
        
        Args:
            car_instance: The Car object to reconcile
            server_state: Authoritative state from server
            dt: Delta time for replay
        """
        server_seq = server_state.get('sequence_number', -1)
        
        # If we haven't received this sequence yet, ignore
        if server_seq <= self.last_server_sequence:
            return
        
        self.last_server_sequence = server_seq
        
        # Extract server position
        server_x = server_state.get('x', car_instance.x)
        server_y = server_state.get('y', car_instance.y)
        server_angle = server_state.get('angle', car_instance.angle)
        server_vx = server_state.get('velocity_x', car_instance.velocity.x)
        server_vy = server_state.get('velocity_y', car_instance.velocity.y)
        
        # Check if prediction was accurate (within tolerance)
        ERROR_THRESHOLD = 5.0  # pixels
        
        if self.predicted_state and self.predicted_state.sequence_number == server_seq:
            dx = abs(self.predicted_state.x - server_x)
            dy = abs(self.predicted_state.y - server_y)
            
            if dx < ERROR_THRESHOLD and dy < ERROR_THRESHOLD:
                # Prediction was accurate, no reconciliation needed
                return
            
            logger.debug(f"Prediction error: dx={dx:.2f}, dy={dy:.2f} - Reconciling")
        
        # Apply server state (rewind)
        car_instance.x = server_x
        car_instance.y = server_y
        car_instance.angle = server_angle
        car_instance.velocity.x = server_vx
        car_instance.velocity.y = server_vy
        
        # Replay inputs that came after server's acknowledged sequence
        inputs_to_replay = [
            inp for inp in self.input_buffer
            if inp.sequence_number > server_seq
        ]
        
        if inputs_to_replay:
            logger.debug(f"Replaying {len(inputs_to_replay)} inputs from seq {server_seq}")
            
            for input_state in inputs_to_replay:
                # Apply input to car (simplified - use actual physics in production)
                self._apply_input_to_car(car_instance, input_state, dt)
        
        # Clean up acknowledged inputs
        self.input_buffer = [
            inp for inp in self.input_buffer
            if inp.sequence_number > server_seq
        ]
    
    def _apply_input_to_car(
        self,
        car_instance,
        input_state: InputState,
        dt: float
    ) -> None:
        """
        Apply a single input frame to car physics.
        
        This should mirror the server's physics calculations.
        For production, integrate with actual Car._update_physics() method.
        """
        import math
        
        # Simplified physics (replace with actual Car physics)
        accel = input_state.throttle * 5.0
        brake = input_state.is_braking * -8.0
        turn_rate = input_state.steering * 120.0
        
        car_instance.angle += turn_rate * dt
        
        angle_rad = math.radians(car_instance.angle)
        forward_force = accel + brake
        
        car_instance.velocity.x += math.cos(angle_rad) * forward_force * dt
        car_instance.velocity.y += math.sin(angle_rad) * forward_force * dt
        
        # Drag
        car_instance.velocity.x *= 0.98
        car_instance.velocity.y *= 0.98
        
        car_instance.x += car_instance.velocity.x * dt * 60
        car_instance.y += car_instance.velocity.y * dt * 60
    
    async def send_ping(self) -> None:
        """Send ping to measure latency"""
        self.last_ping_time = time.time()
        await self._send_message({
            'type': 'ping',
            'timestamp': self.last_ping_time
        })
    
    async def _send_message(self, message: dict) -> None:
        """Send JSON message to server"""
        if not self.connected or not self.writer:
            return
        
        try:
            data = json.dumps(message) + '\n'
            self.writer.write(data.encode())
            await self.writer.drain()
        except Exception as e:
            logger.error(f"Send error: {e}")
            self.connected = False
    
    async def _receive_loop(self) -> None:
        """Main receive loop for server messages"""
        try:
            while self.connected:
                data = await self.reader.readline()
                
                if not data:
                    break
                
                try:
                    message = json.loads(data.decode())
                    await self._process_message(message)
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON received: {e}")
                    
        except asyncio.CancelledError:
            logger.info("Receive loop cancelled")
        except ConnectionResetError:
            logger.error("Connection reset by server")
            self.connected = False
        except Exception as e:
            logger.error(f"Receive error: {e}")
            self.connected = False
    
    async def _process_message(self, message: dict) -> None:
        """Process incoming server message"""
        msg_type = message.get('type')
        
        if msg_type == 'welcome':
            self.player_id = message.get('player_id')
            logger.info(f"Assigned player ID: {self.player_id}")
        
        elif msg_type == 'room_assigned':
            self.room_id = message.get('room_id')
            logger.info(f"Assigned to room: {self.room_id}")
        
        elif msg_type == 'race_start':
            logger.info("Race starting!")
            if self.on_race_start:
                self.on_race_start(message)
        
        elif msg_type == 'game_state':
            # Server authoritative state update
            if self.on_game_state:
                self.on_game_state(message)
        
        elif msg_type == 'player_joined':
            logger.info(f"Player joined: {message.get('player_id')}")
            if self.on_player_joined:
                self.on_player_joined(message)
        
        elif msg_type == 'pong':
            # Calculate RTT
            client_time = message.get('client_timestamp')
            if client_time:
                self.rtt = (time.time() - client_time) * 1000  # Convert to ms
                logger.debug(f"RTT: {self.rtt:.2f}ms")
        
        elif msg_type == 'error':
            logger.error(f"Server error: {message.get('message')}")


# Example integration with Pygame event loop
class NetworkGameLoop:
    """
    Helper class to integrate async network client with Pygame's synchronous loop.
    """
    
    def __init__(self, client: NetworkClient):
        self.client = client
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.loop_thread: Optional[asyncio.Task] = None
    
    def start_async_loop(self) -> None:
        """Start async event loop in background"""
        self.loop = asyncio.new_event_loop()
        asyncio.set_event_loop(self.loop)
    
    def connect_sync(self) -> bool:
        """Synchronous wrapper for connect"""
        if not self.loop:
            self.start_async_loop()
        
        future = asyncio.run_coroutine_threadsafe(
            self.client.connect(),
            self.loop
        )
        return future.result(timeout=15.0)
    
    def send_input_sync(
        self,
        throttle: float,
        steering: float,
        is_braking: float
    ) -> None:
        """Synchronous wrapper for send_input"""
        if self.loop and self.client.connected:
            asyncio.run_coroutine_threadsafe(
                self.client.send_input(throttle, steering, is_braking),
                self.loop
            )
    
    def update(self) -> None:
        """Call this in Pygame main loop to process async events"""
        if self.loop:
            # Process pending async tasks
            self.loop.stop()
            self.loop.run_forever()
