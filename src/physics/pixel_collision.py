"""
Pixel-Perfect Collision Detection Module
=========================================
Implements collision detection based on non-transparent pixels.

Author: Claude (Anthropic)
Date: 2026-01-10
"""

import pygame as pg
import math
from typing import Optional, Tuple


class CollisionMask:
    """
    Wrapper for pygame.mask with helper methods for collision detection.
    Masks represent the non-transparent pixels of a sprite.
    """
    
    def __init__(self, surface: pg.Surface):
        """
        Create a collision mask from a surface.
        
        Args:
            surface: Pygame surface (should have alpha channel)
        """
        # Create mask from alpha channel (non-transparent = collision)
        self.mask = pg.mask.from_surface(surface)
        self.surface = surface
        self.width = surface.get_width()
        self.height = surface.get_height()
    
    def overlaps(self, other_mask: 'CollisionMask', offset: Tuple[int, int]) -> bool:
        """
        Check if this mask overlaps with another mask.
        
        Args:
            other_mask: Another CollisionMask to check against
            offset: (x, y) offset of other_mask relative to this mask
            
        Returns:
            True if masks overlap (collision detected)
        """
        # pygame.mask.overlap returns None if no overlap, or the first overlapping pixel
        overlap_point = self.mask.overlap(other_mask.mask, offset)
        return overlap_point is not None
    
    def overlap_area(self, other_mask: 'CollisionMask', offset: Tuple[int, int]) -> int:
        """
        Get the number of overlapping pixels.
        
        Args:
            other_mask: Another CollisionMask
            offset: (x, y) offset of other_mask relative to this mask
            
        Returns:
            Number of overlapping pixels
        """
        return self.mask.overlap_area(other_mask.mask, offset)
    
    def get_bounding_rects(self) -> list:
        """
        Get list of bounding rectangles for all connected components.
        Useful for optimizing collision checks.
        
        Returns:
            List of pygame.Rect objects
        """
        return self.mask.get_bounding_rects()


class PixelPerfectCollision:
    """
    Handles pixel-perfect collision detection for cars and track.
    """
    
    def __init__(self):
        # Cache for rotated sprite masks (avoid recreating every frame)
        self.mask_cache = {}
        self.cache_max_size = 100  # Limit cache size
        
    def get_rotated_mask(self, sprite: pg.Surface, angle: float) -> Tuple[CollisionMask, pg.Rect]:
        """
        Get a collision mask for a rotated sprite (with caching).
        
        Args:
            sprite: Original sprite surface
            angle: Rotation angle in degrees
            
        Returns:
            Tuple of (CollisionMask, bounding_rect)
        """
        # Round angle to nearest 5 degrees for caching
        cache_angle = round(angle / 5.0) * 5.0
        cache_key = (id(sprite), cache_angle)
        
        if cache_key in self.mask_cache:
            return self.mask_cache[cache_key]
        
        # Rotate sprite
        rotated = pg.transform.rotate(sprite, -angle - 90)  # Match car.py rotation
        
        # Create mask
        mask = CollisionMask(rotated)
        rect = rotated.get_rect()
        
        # Cache it
        if len(self.mask_cache) >= self.cache_max_size:
            # Remove oldest entry (simple FIFO)
            self.mask_cache.pop(next(iter(self.mask_cache)))
        
        self.mask_cache[cache_key] = (mask, rect)
        return mask, rect
    
    def check_car_collision(self, car1, car2) -> Tuple[bool, Optional[Tuple[int, int]]]:
        """
        Check pixel-perfect collision between two cars.
        
        Args:
            car1: First Car object (must have sprite, x, y, angle)
            car2: Second Car object
            
        Returns:
            Tuple of (collision_detected: bool, overlap_point: Optional[Tuple[int, int]])
        """
        # Quick AABB (bounding box) check first
        dx = abs(car2.x - car1.x)
        dy = abs(car2.y - car1.y)
        max_dist = (car1.width + car1.height + car2.width + car2.height) / 2
        
        if dx > max_dist or dy > max_dist:
            return False, None
        
        # Both cars need sprites for pixel-perfect collision
        if not car1.sprite or not car2.sprite:
            # Fallback to circular collision
            dist = math.sqrt(dx * dx + dy * dy)
            radius1 = (car1.width + car1.height) / 4
            radius2 = (car2.width + car2.height) / 4
            return dist < (radius1 + radius2), None
        
        # Get rotated masks
        mask1, rect1 = self.get_rotated_mask(car1.sprite, car1.angle)
        mask2, rect2 = self.get_rotated_mask(car2.sprite, car2.angle)
        
        # Calculate offset between masks
        # Masks are centered on car positions
        rect1.center = (int(car1.x), int(car1.y))
        rect2.center = (int(car2.x), int(car2.y))
        
        offset = (rect2.left - rect1.left, rect2.top - rect1.top)
        
        # Check overlap
        overlap_point = mask1.mask.overlap(mask2.mask, offset)
        
        if overlap_point:
            # Convert overlap point to world coordinates
            world_x = rect1.left + overlap_point[0]
            world_y = rect1.top + overlap_point[1]
            return True, (world_x, world_y)
        
        return False, None
    
    def check_track_collision(self, car, track_surface: pg.Surface, 
                            camera_offset: Tuple[int, int] = (0, 0)) -> Tuple[bool, float]:
        """
        Check if car is on transparent (off-track) area of track image.
        
        Args:
            car: Car object
            track_surface: Track image with alpha channel (transparent = off-track)
            camera_offset: Camera offset for world-to-screen conversion
            
        Returns:
            Tuple of (on_track: bool, coverage: float)
            - on_track: True if car is mostly on non-transparent pixels
            - coverage: Percentage of car on track (0.0 to 1.0)
        """
        if not car.sprite:
            # Fallback: just check center point
            x = int(car.x - camera_offset[0])
            y = int(car.y - camera_offset[1])
            
            if 0 <= x < track_surface.get_width() and 0 <= y < track_surface.get_height():
                pixel = track_surface.get_at((x, y))
                return pixel[3] > 128, 1.0 if pixel[3] > 128 else 0.0
            
            return False, 0.0
        
        # Get car mask
        car_mask, car_rect = self.get_rotated_mask(car.sprite, car.angle)
        car_rect.center = (int(car.x), int(car.y))
        
        # Create track mask (one-time operation, could be cached)
        track_mask = pg.mask.from_surface(track_surface)
        
        # Calculate offset
        offset = (car_rect.left, car_rect.top)
        
        # Count overlapping pixels
        overlap_area = car_mask.mask.overlap_area(track_mask, offset)
        
        # Calculate coverage (what % of car is on track)
        total_car_pixels = car_mask.mask.count()
        
        if total_car_pixels > 0:
            coverage = overlap_area / total_car_pixels
        else:
            coverage = 0.0
        
        # Consider "on track" if > 50% of car is on non-transparent pixels
        on_track = coverage > 0.5
        
        return on_track, coverage
    
    def is_point_on_track(self, x: int, y: int, track_surface: pg.Surface) -> bool:
        """
        Simple point-based track check (fast, for AI or simple checks).
        
        Args:
            x, y: World coordinates
            track_surface: Track image
            
        Returns:
            True if point is on non-transparent pixel
        """
        if 0 <= x < track_surface.get_width() and 0 <= y < track_surface.get_height():
            pixel = track_surface.get_at((int(x), int(y)))
            # Alpha > 128 = opaque = on track
            return pixel[3] > 128
        
        return False
    
    def get_collision_normal(self, car, other_car) -> Optional[pg.Vector2]:
        """
        Get the collision normal vector (direction to push cars apart).
        
        Args:
            car: First car
            other_car: Second car
            
        Returns:
            Normalized vector pointing from other_car to car, or None if no collision
        """
        collision, overlap_point = self.check_car_collision(car, other_car)
        
        if not collision:
            return None
        
        # Calculate vector from other to this car
        dx = car.x - other_car.x
        dy = car.y - other_car.y
        
        if abs(dx) < 0.01 and abs(dy) < 0.01:
            # Cars are exactly on top of each other, use arbitrary direction
            return pg.Vector2(1, 0)
        
        normal = pg.Vector2(dx, dy).normalize()
        return normal
    
    def clear_cache(self):
        """Clear the mask cache (call when changing sprites)."""
        self.mask_cache.clear()


# Global instance (singleton pattern)
_collision_system = None

def get_collision_system() -> PixelPerfectCollision:
    """Get the global collision system instance."""
    global _collision_system
    if _collision_system is None:
        _collision_system = PixelPerfectCollision()
    return _collision_system
