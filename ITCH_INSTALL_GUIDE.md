# How to Install CA Racing

Welcome to the track! Follow these simple steps to get CA Racing running on your PC.

## 1. Downloading the Game
1. Go to our official itch.io page: [https://piotrek2713.itch.io/ca-racing](https://piotrek2713.itch.io/ca-racing)
2. Scroll down to the "Download" section.
3. Find the file for Windows (usually named `CA-Racing-Windows.zip`) and click the **Download** button.
   * *Note: If you see a donation pop-up, you can choose to support development or simply click "No thanks, just take me to the downloads" to proceed for free.*

## 2. Extracting the ZIP Archive
Once the download finishes, you need to unzip the game files before playing.
**Important:** Do not try to run the game directly from inside the ZIP file!

1. Locate the downloaded file (usually in your `Downloads` folder).
2. Right-click on the ZIP file.
3. Select **"Extract All..."** from the menu.
4. Choose where you want to save the game (e.g., your Desktop or a Games folder) and click **"Extract"**.

## 3. Launching the Game
1. Open the folder where you just extracted the files.
2. Look for the application file named `CA-Racing.exe` (or simply `CA-Racing` with the game icon).
3. Double-click the file to launch the game.

## 4. Troubleshooting Common Issues
### "Windows protected your PC" (SmartScreen Warning)
Since CA Racing is an indie game, Microsoft Defender SmartScreen might not recognize it immediately. This is a common occurrence for new games from independent developers.

If you see a blue window preventing the game from starting:
1. Click on the **"More info"** link text.
2. Click the **"Run anyway"** button that appears at the bottom.

The game will now start normally. You only need to do this once!

## 5. Feedback & Support
Thank you for downloading CA Racing! We hope you enjoy the ride.

Your feedback is incredibly valuable to us. Please consider leaving a **rating and a comment** on our [itch.io page](https://piotrek2713.itch.io/ca-racing) to let us know what you think. If you encounter any bugs or have suggestions for new features, drop us a message in the community section!

See you on the track! 🏁
