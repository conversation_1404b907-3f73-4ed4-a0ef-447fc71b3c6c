"""
Server Discovery - UDP Broadcast Responder
==========================================
Server-side component for LAN server discovery via UDP broadcast.

The server listens on UDP port 8445 and responds to discovery packets
with server information including IP, port, player count, and room status.
"""

import asyncio
import json
import socket
import logging
from typing import Optional, Dict, Any

logger = logging.getLogger(__name__)


class ServerBroadcastResponder:
    """
    UDP broadcast responder for server discovery.
    
    Listens for discovery packets and responds with server information.
    """
    
    DISCOVERY_PORT = 8445
    MAGIC_PACKET = b"CA_RACING_DISCOVERY"
    
    def __init__(self, server_name: str, tcp_port: int, room_manager):
        """
        Initialize broadcast responder.
        
        Args:
            server_name: Display name of the server
            tcp_port: TCP port where game server is running
            room_manager: Reference to RoomManager for stats
        """
        self.server_name = server_name
        self.tcp_port = tcp_port
        self.room_manager = room_manager
        self.transport: Optional[asyncio.DatagramTransport] = None
        self.protocol: Optional['DiscoveryProtocol'] = None
        
    async def start(self):
        """Start the UDP broadcast responder"""
        loop = asyncio.get_event_loop()
        
        try:
            # Create UDP endpoint
            self.transport, self.protocol = await loop.create_datagram_endpoint(
                lambda: DiscoveryProtocol(self),
                local_addr=('0.0.0.0', self.DISCOVERY_PORT)
            )
            
            logger.info(f"🔍 Server discovery listening on UDP port {self.DISCOVERY_PORT}")
            
        except Exception as e:
            logger.error(f"Failed to start discovery responder: {e}")
            raise
    
    def stop(self):
        """Stop the UDP broadcast responder"""
        if self.transport:
            self.transport.close()
            logger.info("Server discovery stopped")
    
    def get_server_info(self) -> Dict[str, Any]:
        """
        Get current server information for discovery response.
        
        Returns:
            Dictionary with server stats
        """
        total_players = sum(len(room.players) for room in self.room_manager.rooms.values())
        available_rooms = sum(1 for room in self.room_manager.rooms.values() if not room.is_full())
        
        return {
            'type': 'server_info',
            'server_name': self.server_name,
            'tcp_port': self.tcp_port,
            'total_players': total_players,
            'total_rooms': self.room_manager.get_room_count(),
            'available_rooms': available_rooms,
            'max_players_per_room': 2,
            'version': '1.0.0'
        }


class DiscoveryProtocol(asyncio.DatagramProtocol):
    """UDP protocol handler for discovery packets"""
    
    def __init__(self, responder: ServerBroadcastResponder):
        self.responder = responder
        super().__init__()
    
    def connection_made(self, transport):
        self.transport = transport
    
    def datagram_received(self, data: bytes, addr: tuple):
        """
        Handle incoming discovery packet.
        
        Args:
            data: Received data
            addr: (host, port) of sender
        """
        try:
            # Check if it's a valid discovery packet
            if data == ServerBroadcastResponder.MAGIC_PACKET:
                logger.debug(f"Discovery request from {addr[0]}:{addr[1]}")
                
                # Get server info
                server_info = self.responder.get_server_info()
                
                # Send response back to requester
                response = json.dumps(server_info).encode('utf-8')
                self.transport.sendto(response, addr)
                
                logger.debug(f"Sent discovery response to {addr[0]}:{addr[1]}")
            else:
                logger.warning(f"Invalid discovery packet from {addr[0]}:{addr[1]}")
                
        except Exception as e:
            logger.error(f"Error handling discovery packet: {e}")
    
    def error_received(self, exc):
        logger.error(f"Discovery protocol error: {exc}")
    
    def connection_lost(self, exc):
        if exc:
            logger.error(f"Discovery connection lost: {exc}")
