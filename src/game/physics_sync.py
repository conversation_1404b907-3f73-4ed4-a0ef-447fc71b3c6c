"""
Physics Synchronization Module
================================
Extends the Car class with network synchronization capabilities.

Implements state buffering and interpolation for smooth multiplayer rendering.
"""

import pygame as pg
from typing import List, Optional
from dataclasses import dataclass
from collections import deque


@dataclass
class CarSnapshot:
    """
    Represents a car's state at a specific point in time.
    Used for state buffering and interpolation.
    """
    timestamp: float
    sequence: int
    x: float
    y: float
    angle: float
    velocity_x: float
    velocity_y: float
    lap: int = 0
    current_waypoint: int = 0
    finished: bool = False


class NetworkCar:
    """
    Extends Car functionality with network synchronization.
    
    Key Features:
    - State buffer: Stores recent states for interpolation
    - Interpolation: Smoothly blend between server snapshots
    - Extrapolation: Predict beyond last known state (with caution)
    
    This class wraps an existing Car instance and manages its network sync.
    """
    
    BUFFER_SIZE = 3  # Number of snapshots to keep for interpolation
    INTERPOLATION_DELAY = 0.1  # Delay in seconds (100ms)
    
    def __init__(self, car_instance, is_local_player: bool = False):
        """
        Initialize network car wrapper.
        
        Args:
            car_instance: The Car object to synchronize
            is_local_player: If True, uses client-side prediction instead of interpolation
        """
        self.car = car_instance
        self.is_local_player = is_local_player
        
        # State buffer for remote players (interpolation)
        self.state_buffer: deque[CarSnapshot] = deque(maxlen=self.BUFFER_SIZE)
        
        # Last applied state
        self.last_snapshot: Optional[CarSnapshot] = None
        
        # Prediction for remote cars (extrapolation)
        self.last_velocity = pg.Vector2(0, 0)
        self.last_angular_velocity = 0.0
    
    def add_server_snapshot(
        self,
        timestamp: float,
        sequence: int,
        x: float,
        y: float,
        angle: float,
        velocity_x: float,
        velocity_y: float,
        lap: int = 0,
        current_waypoint: int = 0,
        finished: bool = False
    ) -> None:
        """
        Add a new server state snapshot to the buffer.
        
        For remote players, this will be used for interpolation.
        For local player, this triggers reconciliation in client_handler.
        """
        snapshot = CarSnapshot(
            timestamp=timestamp,
            sequence=sequence,
            x=x,
            y=y,
            angle=angle,
            velocity_x=velocity_x,
            velocity_y=velocity_y,
            lap=lap,
            current_waypoint=current_waypoint,
            finished=finished
        )
        
        self.state_buffer.append(snapshot)
        self.last_snapshot = snapshot
    
    def update_interpolated(self, current_time: float) -> None:
        """
        Update car position using interpolation between buffered states.
        
        This provides smooth movement for remote players by interpolating
        between received server snapshots. We render in the past (by INTERPOLATION_DELAY)
        to always have two snapshots to blend between.
        
        **Why Interpolation?**
        Server updates arrive at irregular intervals due to network jitter.
        Direct application would cause jerky movement. Interpolation smooths this.
        """
        if self.is_local_player:
            # Local player uses prediction, not interpolation
            return
        
        if len(self.state_buffer) < 2:
            # Not enough data for interpolation, use extrapolation
            self._extrapolate()
            return
        
        # Render time is slightly in the past
        render_time = current_time - self.INTERPOLATION_DELAY
        
        # Find two snapshots to interpolate between
        snapshot_before: Optional[CarSnapshot] = None
        snapshot_after: Optional[CarSnapshot] = None
        
        for i in range(len(self.state_buffer) - 1):
            s1 = self.state_buffer[i]
            s2 = self.state_buffer[i + 1]
            
            if s1.timestamp <= render_time <= s2.timestamp:
                snapshot_before = s1
                snapshot_after = s2
                break
        
        if not snapshot_before or not snapshot_after:
            # Couldn't find suitable snapshots, use latest
            if self.last_snapshot:
                self._apply_snapshot(self.last_snapshot)
            return
        
        # Calculate interpolation factor (0.0 to 1.0)
        time_range = snapshot_after.timestamp - snapshot_before.timestamp
        if time_range <= 0:
            alpha = 0.0
        else:
            alpha = (render_time - snapshot_before.timestamp) / time_range
            alpha = max(0.0, min(1.0, alpha))  # Clamp to [0, 1]
        
        # Interpolate position
        self.car.x = self._lerp(snapshot_before.x, snapshot_after.x, alpha)
        self.car.y = self._lerp(snapshot_before.y, snapshot_after.y, alpha)
        
        # Interpolate angle (handle wrapping)
        self.car.angle = self._lerp_angle(
            snapshot_before.angle,
            snapshot_after.angle,
            alpha
        )
        
        # Interpolate velocity for smooth physics
        self.car.velocity.x = self._lerp(
            snapshot_before.velocity_x,
            snapshot_after.velocity_x,
            alpha
        )
        self.car.velocity.y = self._lerp(
            snapshot_before.velocity_y,
            snapshot_after.velocity_y,
            alpha
        )
        
        # Update race state (discrete values, use threshold)
        if alpha > 0.5:
            self.car.lap = snapshot_after.lap
            self.car.current_waypoint = snapshot_after.current_waypoint
            self.car.finished = snapshot_after.finished
        else:
            self.car.lap = snapshot_before.lap
            self.car.current_waypoint = snapshot_before.current_waypoint
            self.car.finished = snapshot_before.finished
    
    def _extrapolate(self) -> None:
        """
        Extrapolate car position beyond last known state.
        
        Used when no recent snapshots are available (packet loss, lag spike).
        This is less accurate than interpolation but prevents freezing.
        """
        if not self.last_snapshot:
            return
        
        # Use last known velocity to predict movement
        dt = 1/60  # Assume 60 FPS
        
        self.car.x += self.last_snapshot.velocity_x * dt * 60
        self.car.y += self.last_snapshot.velocity_y * dt * 60
        
        # Gradually slow down extrapolation to prevent runaway
        self.car.velocity.x = self.last_snapshot.velocity_x * 0.95
        self.car.velocity.y = self.last_snapshot.velocity_y * 0.95
    
    def _apply_snapshot(self, snapshot: CarSnapshot) -> None:
        """Directly apply snapshot to car (no interpolation)"""
        self.car.x = snapshot.x
        self.car.y = snapshot.y
        self.car.angle = snapshot.angle
        self.car.velocity.x = snapshot.velocity_x
        self.car.velocity.y = snapshot.velocity_y
        self.car.lap = snapshot.lap
        self.car.current_waypoint = snapshot.current_waypoint
        self.car.finished = snapshot.finished
    
    @staticmethod
    def _lerp(a: float, b: float, t: float) -> float:
        """Linear interpolation between a and b"""
        return a + (b - a) * t
    
    @staticmethod
    def _lerp_angle(a: float, b: float, t: float) -> float:
        """
        Interpolate angle with proper wrapping.
        
        Angles wrap at 360 degrees, so we need special handling
        to avoid interpolating the long way around (e.g., 350° to 10°
        should go through 360°, not backwards through 180°).
        """
        # Normalize angles to [0, 360)
        a = a % 360
        b = b % 360
        
        # Calculate shortest angular distance
        diff = b - a
        if diff > 180:
            diff -= 360
        elif diff < -180:
            diff += 360
        
        result = a + diff * t
        return result % 360


class StateBuffer:
    """
    Generic state buffer for storing and querying game states.
    
    Can be used for debugging, replay, or advanced prediction algorithms.
    """
    
    def __init__(self, max_size: int = 120):  # 2 seconds at 60 FPS
        self.buffer: deque[CarSnapshot] = deque(maxlen=max_size)
    
    def add(self, snapshot: CarSnapshot) -> None:
        """Add snapshot to buffer"""
        self.buffer.append(snapshot)
    
    def get_at_time(self, timestamp: float) -> Optional[CarSnapshot]:
        """Get snapshot closest to given timestamp"""
        if not self.buffer:
            return None
        
        # Find closest snapshot
        closest = min(
            self.buffer,
            key=lambda s: abs(s.timestamp - timestamp)
        )
        return closest
    
    def get_at_sequence(self, sequence: int) -> Optional[CarSnapshot]:
        """Get snapshot by sequence number"""
        for snapshot in reversed(self.buffer):
            if snapshot.sequence == sequence:
                return snapshot
        return None
    
    def get_range(
        self,
        start_time: float,
        end_time: float
    ) -> List[CarSnapshot]:
        """Get all snapshots within time range"""
        return [
            s for s in self.buffer
            if start_time <= s.timestamp <= end_time
        ]
    
    def clear(self) -> None:
        """Clear buffer"""
        self.buffer.clear()


# Helper functions for integration

def create_network_car(car_instance, is_local: bool = False) -> NetworkCar:
    """
    Factory function to wrap a Car instance for network sync.
    
    Usage:
        car = Car(x, y, angle, stats)
        net_car = create_network_car(car, is_local=True)
    """
    return NetworkCar(car_instance, is_local_player=is_local)


def apply_server_state(
    net_car: NetworkCar,
    server_data: dict,
    current_time: float
) -> None:
    """
    Helper to apply server state to a NetworkCar.
    
    Args:
        net_car: The NetworkCar instance
        server_data: Dictionary with state data from server
        current_time: Current client time
    """
    net_car.add_server_snapshot(
        timestamp=server_data.get('timestamp', current_time),
        sequence=server_data.get('sequence_number', 0),
        x=server_data.get('x', 0.0),
        y=server_data.get('y', 0.0),
        angle=server_data.get('angle', 0.0),
        velocity_x=server_data.get('velocity_x', 0.0),
        velocity_y=server_data.get('velocity_y', 0.0),
        lap=server_data.get('lap', 0),
        current_waypoint=server_data.get('current_waypoint', 0),
        finished=server_data.get('finished', False)
    )
