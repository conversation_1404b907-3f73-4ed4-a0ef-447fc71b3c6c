from pygame.math import Vector2
import pygame as pg
import math
import logging
# from src.game.ai_driver import TrackCrawler # Deprecated

class TrackManager:
    def __init__(self):
        self.waypoints = []
        self.racing_line = [] # Optimal racing line points
        self.using_generated_waypoints = False
        # self.crawler = None # Deprecated

    def load_waypoints(self, tmx_data, start_pos):
        """
        Loads waypoints from the TMXMap object.
        Priority:
        1. 'RacingLine' object layer (Polyline).
        2. Explicit 'waypoints' layer in TMX.
        3. Road tiles (Tile-based generation).
        """
        self.waypoints = []
        self.racing_line = []
        self.using_generated_waypoints = False
        # self.crawler = None

        # 0. Load Racing Line if available (from TMXMap)
        # This is the preferred method for AI navigation
        if hasattr(tmx_data, 'racing_line') and tmx_data.racing_line:
            self.racing_line = tmx_data.racing_line
            # Also use racing line as waypoints if no other waypoints found
            self.waypoints = [Vector2(p) for p in self.racing_line]
            self._interpolate_waypoints(min_distance=30.0) # High density for precise AI
            logging.info(f"[TrackManager] Loaded RacingLine with {len(self.racing_line)} points (Interpolated to {len(self.waypoints)}).")
            return

        # 1. Try high-precision 'AI_Spline' polyline first (Legacy)
        ai_spline_object = None
        for obj in tmx_data.collision_objects:
            if obj.group_name == 'AI_Spline' and obj.type == 'polyline':
                ai_spline_object = obj
                break
        
        if ai_spline_object:
            world_points = ai_spline_object.get_world_points()
            self.waypoints = [Vector2(p) for p in world_points]
            self._interpolate_waypoints(min_distance=30.0)
            logging.info(f"SUCCESS: Loaded {len(self.waypoints)} waypoints from high-precision 'AI_Spline'.")
            return

        # 2. Try explicit waypoints
        if hasattr(tmx_data, 'waypoints') and tmx_data.waypoints:
            try:
                sorted_waypoints = sorted(tmx_data.waypoints, key=lambda w: int(w.name))
                self.waypoints = [Vector2(obj.x, obj.y) for obj in sorted_waypoints]
                logging.info(f"SUCCESS: Loaded {len(self.waypoints)} waypoints from TMX.")
                return
            except (ValueError, TypeError):
                logging.warning("[TrackManager] Warning: Waypoint names are not integers. Loading without sorting.")
                self.waypoints = [Vector2(obj.x, obj.y) for obj in tmx_data.waypoints]
                return

        # 3. TrackCrawler removed as per request.

        # 4. Fallback to Tile Generation
        logging.warning("CRITICAL WARNING: No waypoints found! Attempting to generate from tiles.")
        self._generate_waypoints_from_tiles(tmx_data, start_pos)
        if self.waypoints:
            self.using_generated_waypoints = True
            logging.info(f"SUCCESS: Generated {len(self.waypoints)} waypoints from road tiles.")
        else:
            logging.critical("CRITICAL FAILURE: Could not generate waypoints. AI will be disabled.")

    def _generate_waypoints_from_tiles(self, tmx_data, start_pos):
        """
        Scans tile layers for road tiles and generates a path of waypoints.
        Uses TMXMap data structure from src.core.tmx_loader.
        """
        road_centers = []
        
        # Iterate over all tile layers in the TMXMap
        # tmx_data is expected to be a TMXMap instance which has 'tile_layers'
        if not hasattr(tmx_data, 'tile_layers'):
            logging.critical("TMX Data object missing 'tile_layers' attribute.")
            return

        for layer in tmx_data.tile_layers:
            # Iterate through the grid
            for y in range(layer.height):
                for x in range(layer.width):
                    gid = layer.get_tile_at(x, y)
                    
                    # Check if this GID corresponds to a road tile
                    # We use the ROAD_TILES set defined in TMXMap
                    if gid in tmx_data.ROAD_TILES:
                        # Calculate center of the tile in world coordinates
                        # tmx_data.tile_width/height are already scaled
                        world_x = x * tmx_data.tile_width + tmx_data.tile_width / 2
                        world_y = y * tmx_data.tile_height + tmx_data.tile_height / 2
                        road_centers.append(Vector2(world_x, world_y))

        if not road_centers:
            logging.warning("No road tiles found to generate waypoints.")
            return

        # Determine the starting point for sorting
        # Prioritize the car's actual start position, fallback to TMX data, then to the first found tile
        if not start_pos:
            if hasattr(tmx_data, 'start_line_pos') and tmx_data.start_line_pos:
                start_pos = Vector2(tmx_data.start_line_pos)
            elif road_centers:
                start_pos = road_centers[0]
            else:
                return # Should be caught by 'if not road_centers' above

        sorted_waypoints = []
        unvisited = road_centers[:]
        
        # Find the closest road tile to the start position to begin the path
        # This ensures we start the path at the actual start line
        unvisited.sort(key=lambda p: p.distance_to(start_pos))

        if unvisited:
            current_point = unvisited.pop(0)
            sorted_waypoints.append(current_point)

            # Simple nearest-neighbor sorting to form a continuous path
            # Optimization: Only check nearest neighbors to avoid jumping across track sections
            while unvisited:
                # Sort by distance to current point
                # We only need the closest one, so min is faster than sort
                next_point = min(unvisited, key=lambda p: p.distance_squared_to(current_point))
                
                # Check if the next point is reasonably close (e.g. adjacent tile)
                # Diagonal distance is sqrt(w^2 + h^2), so allow a bit more margin
                max_jump = tmx_data.tile_width * 2.5
                if current_point.distance_to(next_point) > max_jump:
                     # If the closest point is too far, we might have hit a dead end or a gap
                     # For now, we just jump to it, but logging a warning might be good
                     # logging.debug(f"Jump detected in waypoint generation: {current_point.distance_to(next_point)}")
                     pass

                unvisited.remove(next_point)
                current_point = next_point
                sorted_waypoints.append(current_point)

        # Downsample waypoints to reduce count (optional, but good for performance)
        # Keep every Nth waypoint
        if len(sorted_waypoints) > 100:
             self.waypoints = sorted_waypoints[::2]
        else:
             self.waypoints = sorted_waypoints
             self._interpolate_waypoints(min_distance=30.0)

    def _interpolate_waypoints(self, min_distance=30.0):
        """
        Densifies the waypoint list by adding intermediate points.
        Ensures that no two waypoints are further apart than min_distance.
        """
        if len(self.waypoints) < 2:
            return

        new_waypoints = []
        for i in range(len(self.waypoints)):
            p1 = self.waypoints[i]
            p2 = self.waypoints[(i + 1) % len(self.waypoints)]
            
            dist = p1.distance_to(p2)
            new_waypoints.append(p1)
            
            if dist > min_distance:
                num_segments = int(dist / min_distance)
                for j in range(1, num_segments):
                    t = j / num_segments
                    interpolated_point = p1.lerp(p2, t)
                    new_waypoints.append(interpolated_point)
        
        self.waypoints = new_waypoints

    def get_waypoint(self, index):
        """
        Returns the waypoint at a given index.
        """
        if 0 <= index < len(self.waypoints):
            return self.waypoints[index]
        return None

    def get_total_waypoints(self):
        """
        Returns the total number of waypoints.
        """
        return len(self.waypoints)

    def get_car_progress(self, car):
        """
        Calculates the total distance traveled by the car along the track.
        Used for ranking.
        """
        if not self.waypoints:
            return 0.0
            
        # Use strict progress based on the last validated waypoint
        # This prevents cheating by cutting corners
        
        base_progress = (car.lap * len(self.waypoints)) + car.current_waypoint
        
        # Add fractional progress towards the next waypoint
        if car.current_waypoint < len(self.waypoints):
            current_wp = self.waypoints[car.current_waypoint]
            # If we have a previous waypoint, we can calculate interpolation
            prev_wp_idx = (car.current_waypoint - 1) % len(self.waypoints)
            prev_wp = self.waypoints[prev_wp_idx]
            
            segment_vec = current_wp - prev_wp
            car_vec = car.position - prev_wp
            
            segment_len_sq = segment_vec.length_squared()
            if segment_len_sq > 0:
                # Project car position onto segment
                t = max(0.0, min(1.0, car_vec.dot(segment_vec) / segment_len_sq))
                base_progress += t
                
        return base_progress

    def draw_debug(self, surface, camera, active_waypoint_index=None, mode='SIMPLE'):
        """
        Draws the waypoint path and the active target for debugging.
        Modes:
        - 'SIMPLE': Just blue lines and active red circle.
        - 'ADVANCED': Green racing line, specific waypoint dots, indices, and radii.
        """
        if len(self.waypoints) < 2:
            return

        # 1. Draw Racing Line (Green) - ADVANCED only
        if mode == 'ADVANCED' and self.racing_line:
            # Racing line points might be raw coordinates, apply camera
            rl_points = [camera.apply_pos(Vector2(p[0], p[1])) for p in self.racing_line]
            if len(rl_points) > 1:
                pg.draw.lines(surface, (0, 255, 0), False, rl_points, 2)

        # 2. Draw Waypoint Path (Blue)
        wp_points = [camera.apply_pos(wp) for wp in self.waypoints]
        pg.draw.lines(surface, (0, 0, 255), False, wp_points, 2)

        # 3. Draw Waypoint Details - ADVANCED only
        if mode == 'ADVANCED':
            font = pg.font.SysFont('Arial', 12)
            for i, wp in enumerate(self.waypoints):
                # Draw every 10th waypoint index to avoid clutter
                if i % 10 == 0 or i == len(self.waypoints) - 1:
                    pos = camera.apply_pos(wp)
                    pg.draw.circle(surface, (0, 255, 255), pos, 3) # Cyan dot
                    
                    text = font.render(str(i), True, (255, 255, 255))
                    surface.blit(text, (pos[0] + 5, pos[1] - 5))
                
                # Highlight start and finish
                if i == 0:
                     pos = camera.apply_pos(wp)
                     pg.draw.circle(surface, (255, 255, 0), pos, 5) # Yellow Start

        # 4. Draw Active Waypoint (Red)
        if active_waypoint_index is not None and 0 <= active_waypoint_index < len(self.waypoints):
            active_wp = self.waypoints[active_waypoint_index]
            pos = camera.apply_pos(active_wp)
            
            # Draw target circle
            pg.draw.circle(surface, (255, 0, 0), pos, 15, 3)
            
            # Draw line to target
            # (Requires player position, passed via camera or handled externally)
            
            if mode == 'ADVANCED':
                # Draw detection radius (approx)
                 pg.draw.circle(surface, (255, 0, 0), pos, 600, 1) # 600px off-track threshold
                 pg.draw.circle(surface, (0, 255, 0), pos, 200, 1) # 200px reliable detection

        if self.using_generated_waypoints:
            font = pg.font.Font(None, 36)
            text = font.render("WARNING: Using Generated Waypoints", True, (255, 255, 0))
            surface.blit(text, (10, 10))
