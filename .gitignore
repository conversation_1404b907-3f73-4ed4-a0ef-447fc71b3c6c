# ═══════════════════════════════════════════════════════════
# CA-RACING .gitignore
# ═══════════════════════════════════════════════════════════

# --- Python Core ---
__pycache__/
*.py[cod]
*$py.class
*.so
.Python

# --- Virtual Environments ---
venv/
.venv/
env/
.env/
ENV/
env.bak/
venv.bak/

# --- Distribution & Packaging ---
build/
dist/
dist_pkg/
*.egg-info/
.eggs/
*.egg
*.whl

# --- Build Artifacts (Binaries & Installers) ---
*.AppImage
*.run
*.deb
*.rpm
*.exe
*.msi
*.msix
*.bin
ca-racing-installer*/
CA-Racing.AppDir/
appimagetool

# --- AUR Build Artifacts ---
packaging/aur/src/
packaging/aur/pkg/
packaging/aur/*.pkg.tar.zst
packaging/aur/*.pkg.tar.xz

# --- PyInstaller ---
*.spec
!ca-racing.spec
!packaging/rpm/*.spec
*.manifest

# --- Unit Test & Coverage ---
htmlcov/
.tox/
.nox/
.coverage
.coverage.*
.cache
nosetests.xml
coverage.xml
*.cover
*.py,cover
.hypothesis/
.pytest_cache/
pytestdebug.log

# --- Translations ---
*.mo
*.pot

# --- User Data & Saves ---
data/saves/
!data/saves/.gitkeep

# --- IDE & Editor Configuration ---
.vscode/
.idea/
.cursor/
*.swp
*.swo
*.swn
*~
.project
.pydevproject
.settings/

# --- OS Generated Files ---
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
desktop.ini

# --- Security & Secrets (CRITICAL) ---
private.key
*.pem
*.key
secrets.env
.env
.env.*
!.env.example
push-with-token.sh

# --- Logs & Temporary Files ---
*.log
*logs.txt
*.tmp
*.bak
*.swp
*.orig
*~

# --- Development & Testing Scripts (Root Level) ---
# Test scripts
test_*.py
!src/test_*.py

# Generation/Fix scripts
fix_*.py
generate_*.py
download_*.py
reproduce_bug*.py

# Generated images/covers (development only)
devlog_cover*.png
*_cover.png

# Asset downloads (large files)
*.zip
!packaging/**/*.zip

# --- Tool Output & Debug Files ---
src/tools/debug_*.txt
src/tools/debug_*.png
src/tools/*_output.png
src/tools/*_debug.png

# --- Backup files ---
*.backup
*_backup.*
*.old
