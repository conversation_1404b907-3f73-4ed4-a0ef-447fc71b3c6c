name: Build and Publish Packages

on:
  push:
    tags:
      - 'v*'

jobs:
  build:
    name: Build Packages
    runs-on: ubuntu-latest
    permissions:
      contents: write
      pages: write
      id-token: write

    steps:
      - name: Checkout code
        uses: actions/checkout@v4

      - name: Set up Python
        uses: actions/setup-python@v5
        with:
          python-version: '3.12'

      - name: Install System Dependencies
        run: |
          sudo apt-get update
          sudo apt-get install -y rpm dpkg-dev createrepo-c build-essential libgl1-mesa-dev libasound2-dev libsdl2-dev

      - name: Install Python Dependencies
        run: |
          python -m pip install --upgrade pip
          pip install -r requirements.txt || pip install pyinstaller pygame pytmx

      - name: Import GPG Key
        id: import_gpg
        uses: crazy-max/ghaction-import-gpg@v6
        with:
          gpg_private_key: ${{ secrets.GPG_PRIVATE_KEY }}
          passphrase: ${{ secrets.GPG_PASSPHRASE }}

      - name: Build Distribution (Binary & Installer)
        run: ./build_dist.sh

      - name: Build DEB and RPM Packages
        run: |
          # Strip 'v' prefix from tag for versioning
          VERSION="${{ github.ref_name }}"
          VERSION="${VERSION#v}"
          echo "Building packages for version: $VERSION"
          ./packaging/scripts/build_packages.sh "$VERSION"

      - name: Create Repository Structure
        env:
          GPG_PRIVATE_KEY: ${{ secrets.GPG_PRIVATE_KEY }}
          GPG_PASSPHRASE: ${{ secrets.GPG_PASSPHRASE }}
          GPG_KEY_ID: ${{ steps.import_gpg.outputs.keyid }}
        run: ./packaging/scripts/create_repo.sh

      - name: Upload Artifacts to Release
        uses: softprops/action-gh-release@v1
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            artifacts/*.deb
            artifacts/*.rpm
            CA-Racing-*-setup.run
            CA-Racing-*-setup.AppImage

      - name: Deploy Repository to GitHub Pages
        uses: peaceiris/actions-gh-pages@v3
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          publish_dir: ./repo
          keep_files: true # Keep old versions if desired, or false to wipe
          user_name: 'github-actions[bot]'
          user_email: 'github-actions[bot]@users.noreply.github.com'