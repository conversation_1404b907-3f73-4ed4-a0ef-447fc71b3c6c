#!/usr/bin/env python3
"""
Track Builder v10.0 - Sub-Tile Anchor & Spline Engine

Refactored to implement a high-precision "Sub-Tile Anchor System". This script
scans a TMX file's tile layer, applies affine transformations to pre-defined
local anchor points within each tile, and generates a Centripetal Catmull-Rom
spline that perfectly traces the visual shoulder of the track texture.

This eliminates "corner cutting" artifacts produced by single-point geometry
generation and ensures the collision boundary is pixel-perfect.
"""
import sys
import math
import xml.etree.ElementTree as ET
from pathlib import Path
from typing import List, Tuple, Dict, Optional

try:
    import torch
except ImportError:
    print("ERROR: PyTorch is required but not found.")
    print("Please install it: pip install torch")
    sys.exit(1)

# --- Engineering Configuration ---

# [VERIFICATION]: Manually adjust these pixel values to calibrate the anchors.
# Coordinates are local (0-63) within a tile. (0,0) is top-left.
ANCHOR_DEFS: Dict[int, List[Tuple[int, int]]] = {
    # Tile IDs are derived from the 'map.tsx' tileset, starting from firstgid=2.
    # ID 3: Straight Horizontal
    3: [(0, 22), (64, 22)],
    # ID 4: Straight Vertical
    4: [(42, 0), (42, 64)],
    # ID 5: Corner (Top-Left)
    5: [(42, 0), (22, 22), (0, 42)],
    # ID 6: Corner (Top-Right)
    6: [(0, 22), (42, 22), (64, 42)],
    # ID 9: Corner (Bottom-Left)
    9: [(0, 42), (22, 42), (42, 64)],
    # ID 10: Corner (Bottom-Right)
    10: [(64, 22), (42, 42), (42, 64)],
    # Additional tiles can be defined here if the tileset expands.
}

# --- Constants & Parameters ---
class Config:
    """Static configuration for the track builder."""
    SCRIPT_DIR: Path = Path(__file__).parent.resolve()
    PROJECT_ROOT: Path = SCRIPT_DIR.parents[1]
    MAP_PATH: Path = PROJECT_ROOT / "maps" / "map_0.tmx"
    OUTPUT_PATH: Path = PROJECT_ROOT / "maps" / "map_0_generated.tmx"

    TILE_WIDTH: int = 64
    TILE_HEIGHT: int = 64
    
    # TMX Layer/Object Configuration
    TRACK_LAYER_NAME: str = "Droga"
    OUTPUT_LAYER_NAME: str = "ShoulderLine"
    OUTPUT_OBJECT_NAME: str = "ShoulderLine"
    OUTPUT_OBJECT_PROPS: Dict[str, str] = {
        "name": "ShoulderLine",
        "type": "Polyline",
        "friction": "medium"
    }

    # Centripetal Catmull-Rom Spline Parameters
    SPLINE_ALPHA: float = 0.5  # 0.5 for Centripetal, 0.0 for Uniform, 1.0 for Chordal
    SAMPLES_PER_SEGMENT: int = 64 # Increase for smoother curves

# --- Core Logic ---

class TMXParser:
    """Parses the TMX file to extract the track's tile sequence and rotations."""
    def __init__(self, tmx_path: Path):
        self.tree = ET.parse(tmx_path)
        self.root = self.tree.getroot()
        self.firstgid = int(self.root.find(".//tileset[@source='map.tsx']").get('firstgid', '1'))

    def get_track_sequence(self, layer_name: str) -> List[Tuple[Tuple[int, int], int, int]]:
        """Extracts tile data as (grid_pos, tile_id, rotation_degrees)."""
        layer = self.root.find(f".//layer[@name='{layer_name}']")
        if layer is None:
            raise ValueError(f"Layer '{layer_name}' not found in TMX file.")
        
        width = int(layer.get('width'))
        data_element = layer.find('data')
        if data_element is None or data_element.text is None:
            raise ValueError("No data found in the track layer.")
            
        csv_data = [int(x.strip()) for x in data_element.text.split(',')]
        
        sequence = []
        for i, gid in enumerate(csv_data):
            if gid == 0:
                continue

            # Decode GID for flip/rotation flags
            flipped_horizontally = (gid & 0x80000000)
            flipped_vertically = (gid & 0x40000000)
            flipped_diagonally = (gid & 0x20000000)
            
            # Clear flags to get the actual tile ID
            tile_id = gid & ~(0x80000000 | 0x40000000 | 0x20000000)
            
            # TMX rotation logic
            rotation = 0
            if flipped_diagonally:
                if flipped_horizontally and flipped_vertically:
                    rotation = 90
                elif flipped_horizontally:
                    rotation = 90
                elif flipped_vertically:
                    rotation = 270
                else:
                    rotation = 270
            else:
                if flipped_horizontally and flipped_vertically:
                    rotation = 180
                elif flipped_horizontally:
                    rotation = 0 # No standard rotation, but handle if needed
                elif flipped_vertically:
                    rotation = 180
            
            grid_x = i % width
            grid_y = i // width
            sequence.append(((grid_x, grid_y), tile_id, rotation))
            
        return sequence

class GridScanner:
    """Applies transformations to generate global anchor points."""
    def __init__(self, track_sequence: List[Tuple[Tuple[int, int], int, int]]):
        self.sequence = track_sequence
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        print(f"INFO: GridScanner using device: {self.device}")

    def generate_global_anchors(self) -> List[Tuple[float, float]]:
        """Processes the track sequence to produce a list of world-space anchor points."""
        global_points = []
        for (grid_pos, tile_id, rotation) in self.sequence:
            local_anchors = ANCHOR_DEFS.get(tile_id)
            if not local_anchors:
                continue

            # Convert to tensor for matrix operations
            anchors_tensor = torch.tensor(local_anchors, dtype=torch.float64, device=self.device)
            
            # Center of tile for rotation
            center = torch.tensor([Config.TILE_WIDTH / 2, Config.TILE_HEIGHT / 2], dtype=torch.float64, device=self.device)
            
            # 1. Translate to origin
            translated_anchors = anchors_tensor - center
            
            # 2. Apply rotation matrix
            rad = math.radians(rotation)
            cos_r, sin_r = math.cos(rad), math.sin(rad)
            rot_matrix = torch.tensor([[cos_r, -sin_r], [sin_r, cos_r]], dtype=torch.float64, device=self.device)
            rotated_anchors = translated_anchors @ rot_matrix.T
            
            # 3. Translate back from origin
            rotated_local_anchors = rotated_anchors + center
            
            # 4. Translate to global map coordinates
            grid_offset = torch.tensor(grid_pos, dtype=torch.float64, device=self.device) * Config.TILE_WIDTH
            final_global_anchors = rotated_local_anchors + grid_offset
            
            global_points.extend([tuple(p.tolist()) for p in final_global_anchors])
            
        return global_points

class TorchSplineProcessor:
    """Generates a Centripetal Catmull-Rom spline from control points."""
    def __init__(self, points: List[Tuple[float, float]]):
        self.device = torch.device('cuda' if torch.cuda.is_available() else 'cpu')
        self.points = torch.tensor(points, dtype=torch.float64, device=self.device)
        self.spline: Optional[torch.Tensor] = None

    def _get_t(self, t, alpha, p0, p1):
        """Calculate the parameter t for the spline segment."""
        return ((p1 - p0).pow(2).sum(axis=0)).pow(0.5).pow(alpha) + t

    def _catmull_rom_segment(self, P0, P1, P2, P3, num_points, alpha):
        """Calculates a single segment of the Catmull-Rom spline."""
        t0 = torch.tensor(0.0, device=self.device, dtype=torch.float64)
        t1 = self._get_t(t0, alpha, P0, P1)
        t2 = self._get_t(t1, alpha, P1, P2)
        t3 = self._get_t(t2, alpha, P2, P3)
        
        t = torch.linspace(t1, t2, num_points, device=self.device, dtype=torch.float64)
        
        t = t.unsqueeze(1) # Reshape t to [64, 1] for broadcasting

        A1 = (t1 - t) / (t1 - t0) * P0 + (t - t0) / (t1 - t0) * P1
        A2 = (t2 - t) / (t2 - t1) * P1 + (t - t1) / (t2 - t1) * P2
        A3 = (t3 - t) / (t3 - t2) * P2 + (t - t2) / (t3 - t2) * P3
        
        B1 = (t2 - t) / (t2 - t0) * A1 + (t - t0) / (t2 - t0) * A2
        B2 = (t3 - t) / (t3 - t1) * A2 + (t - t1) / (t3 - t1) * A3
        
        C = (t2 - t) / (t2 - t1) * B1 + (t - t1) / (t2 - t1) * B2
        return C

    def generate_spline(self):
        """Generates the full spline from all control points."""
        # Pad points for an open spline (repeat start and end)
        padded_points = torch.cat([self.points[0:1], self.points, self.points[-1:]])
        
        spline_segments = []
        for i in range(len(padded_points) - 3):
            p0, p1, p2, p3 = padded_points[i], padded_points[i+1], padded_points[i+2], padded_points[i+3]
            segment = self._catmull_rom_segment(p0, p1, p2, p3, Config.SAMPLES_PER_SEGMENT, Config.SPLINE_ALPHA)
            spline_segments.append(segment)
            
        self.spline = torch.cat(spline_segments)
        print(f"INFO: Generated spline with {len(self.spline)} points.")

    def get_points(self) -> List[Tuple[float, float]]:
        if self.spline is None: return []
        return [tuple(p.tolist()) for p in self.spline]

class TMXWriter:
    """Writes the generated polyline back to a new TMX file."""
    def __init__(self, tmx_path: Path):
        self.tree = ET.parse(tmx_path)
        self.root = self.tree.getroot()

    def write_polyline(self, layer_name: str, obj_name: str, points: List[Tuple[float, float]], props: Dict[str, str]):
        # Remove existing layer if it exists
        existing_layer = self.root.find(f".//objectgroup[@name='{layer_name}']")
        if existing_layer is not None:
            self.root.remove(existing_layer)

        # Create new object group
        layer_ids = [int(l.get('id', '0')) for l in self.root.findall('.//layer|.//objectgroup')]
        new_id = str(max(layer_ids) + 1 if layer_ids else 2)
        layer = ET.SubElement(self.root, 'objectgroup', id=new_id, name=layer_name)
        
        # Create the polyline object
        points_str = " ".join([f"{p[0]:.2f},{p[1]:.2f}" for p in points])
        next_obj_id = int(self.root.get('nextobjectid', '1'))
        obj = ET.SubElement(layer, 'object', id=str(next_obj_id), name=obj_name, x='0', y='0')
        ET.SubElement(obj, 'polyline', {'points': points_str})
        
        # Add properties
        props_el = ET.SubElement(obj, 'properties')
        for key, val in props.items():
            ET.SubElement(props_el, 'property', {'name': key, 'value': str(val)})
            
        self.root.set('nextobjectid', str(next_obj_id + 1))
        self.root.set('nextlayerid', str(int(new_id) + 1))

    def save(self, output_path: Path):
        ET.indent(self.tree, space="  ", level=0)
        self.tree.write(output_path, encoding='utf-8', xml_declaration=True)
        print(f"SUCCESS: Generated TMX file saved to {output_path}")

def main():
    """Main execution function."""
    print("--- Track Builder v10.0 ---")
    try:
        # 1. Parse TMX to get the sequence of tiles and their rotations
        print(f"[1/4] Parsing TMX file: {Config.MAP_PATH}")
        parser = TMXParser(Config.MAP_PATH)
        track_sequence = parser.get_track_sequence(Config.TRACK_LAYER_NAME)
        print(f"INFO: Found {len(track_sequence)} track tiles.")

        # 2. Generate global anchor points using the Grid Scanner
        print("[2/4] Scanning grid and transforming anchors...")
        scanner = GridScanner(track_sequence)
        global_anchors = scanner.generate_global_anchors()
        print(f"INFO: Generated {len(global_anchors)} global anchor points.")

        # 3. Process points through the spline engine
        print("[3/4] Generating Centripetal Catmull-Rom spline...")
        spline_proc = TorchSplineProcessor(global_anchors)
        spline_proc.generate_spline()
        final_points = spline_proc.get_points()

        # 4. Write the final polyline to the TMX file
        print(f"[4/4] Writing polyline to output file: {Config.OUTPUT_PATH}")
        writer = TMXWriter(Config.MAP_PATH)
        writer.write_polyline(
            layer_name=Config.OUTPUT_LAYER_NAME,
            obj_name=Config.OUTPUT_OBJECT_NAME,
            points=final_points,
            props=Config.OUTPUT_OBJECT_PROPS
        )
        writer.save(Config.OUTPUT_PATH)

    except Exception as e:
        print(f"FATAL ERROR: {e}")
        import traceback
        traceback.print_exc()
        return 1
    
    print("--- Build Complete ---")
    return 0

if __name__ == "__main__":
    sys.exit(main())
