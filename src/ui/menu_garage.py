import pygame as pg
from src.constants import *
from src.ui.widgets import But<PERSON>
from src.core.assets import get_car_sprite
from src.core.game_data import (
    get_car_display_name,
    get_car_size_class,
    get_part_display_name,
    get_part_size_class,
    is_part_compatible
)

# Color for incompatible parts warning background
INCOMPATIBLE_BG = (100, 40, 40)  # Reddish warning color


class GarageMenu:
    """
    Displays the player's garage allowing them to select an active car.
    Supports two tabs: CARS (car selection) and PARTS (parts management).
    Visualize cars in a grid system with mounted parts display.
    """

    # Tab identifiers
    TAB_CARS = 'CARS'
    TAB_PARTS = 'PARTS'

    # Part type categories for the parts management UI
    PART_TYPES = ['engine', 'breaks', 'boost']

    def __init__(self, app, player, return_callback):
        self.app = app
        self.player = player
        self.return_callback = return_callback
        self.screen = app.screen

        # Fonts for UI rendering
        self.title_font = pg.font.SysFont('Consolas', 50, bold=True)
        self.info_font = pg.font.SysFont('Consolas', 24)
        self.small_font = pg.font.SysFont('Consolas', 18)
        self.part_font = pg.font.SysFont('Consolas', 20)

        # List of tuples: (pg.Rect, car_id_string)
        self.car_rects = []
        self.buttons = []
        self.tab_buttons = []

        # Current active tab (CARS or PARTS)
        self.current_tab = self.TAB_CARS

        # Currently selected part type in PARTS tab (engine, breaks, boost)
        self.selected_part_type = 'engine'

        # Part slots for click detection: list of (pg.Rect, part_id, part_type)
        self.part_slots = []

        # Feedback message to display (e.g., "Part installed successfully!")
        self.feedback_message = ""
        self.feedback_timer = 0  # Frames remaining to show message
        self.feedback_color = TEXT_MAIN

        self.init_ui()

    def init_ui(self):
        """Initializes buttons, tabs, and calculates car grid positions."""
        self.buttons = []
        self.tab_buttons = []
        cx = self.app.screen.get_width() // 2
        h = self.app.screen.get_height()

        # Back button at the bottom
        self.buttons.append(Button(
            self.app.lang.get("menu_back"),
            (cx, h - 50),
            self._on_back,
            app=self.app,
            custom_color=ACCENT_RED
        ))

        # Tab buttons at the top
        tab_y = 130
        self.tab_buttons.append(Button(
            self.app.lang.get("garage_tab_cars"),
            (cx - 120, tab_y),
            lambda: self._switch_tab(self.TAB_CARS),
            app=self.app
        ))
        self.tab_buttons.append(Button(
            self.app.lang.get("garage_tab_parts"),
            (cx + 120, tab_y),
            lambda: self._switch_tab(self.TAB_PARTS),
            app=self.app
        ))

        # Calculate grid for cars
        self.calculate_car_grid()

    def _switch_tab(self, tab):
        """Switches between CARS and PARTS tabs."""
        if self.current_tab != tab:
            self.current_tab = tab
            self.app.audio.play_sfx('ui_select')
            self.feedback_message = ""  # Clear any feedback on tab switch

    def calculate_car_grid(self):
        """Calculates positions for owned cars to display in a grid layout."""
        self.car_rects = []

        # Layout settings
        start_x = 150
        start_y = 180
        gap_x = 220
        gap_y = 200
        cols_per_row = 4

        for i, item in enumerate(self.player.garage):
            # FIX: Ensure we have the String ID, even if item is a Dict object
            # Check model_id first (correct for new saves), then name
            car_id = item
            if isinstance(item, dict):
                car_id = item.get('model_id') or item.get('name')

            row = i // cols_per_row
            col = i % cols_per_row

            x = start_x + col * gap_x
            y = start_y + row * gap_y

            # Target size for background panel
            rect = pg.Rect(x, y, 160, 160)
            self.car_rects.append((rect, car_id))

    def _on_back(self):
        self.app.audio.play_sfx('ui_back')
        self.return_callback()

    def _get_current_car_data(self):
        """
        Returns the full car data dict for the currently selected car.
        Handles both string and dict garage entries.
        """
        for item in self.player.garage:
            if isinstance(item, dict):
                item_id = item.get('model_id') or item.get('name')
                if item_id == self.player.current_car:
                    return item
            elif item == self.player.current_car:
                # Legacy string-only entry - create minimal dict
                return {'model_id': item, 'mounted_parts': {}}
        return None

    def _get_mounted_parts(self, car_data):
        """
        Returns the mounted_parts dict for a car, with safe defaults.
        """
        if not car_data:
            return {'engine': None, 'breaks': None, 'boost': None}
        return car_data.get('mounted_parts', {'engine': None, 'breaks': None, 'boost': None})

    def _get_inventory_parts(self, part_type):
        """
        Returns list of parts from inventory for a given type.
        Maps 'engine' -> 'engines', 'breaks' -> 'breaks', 'boost' -> 'boosts'
        """
        type_map = {
            'engine': 'engines',
            'breaks': 'breaks',
            'boost': 'boosts'
        }
        inv_key = type_map.get(part_type, part_type + 's')
        return self.player.inventory.get(inv_key, [])

    def _install_part(self, part_id, part_type):
        """
        Attempts to install a part on the current car.
        - Checks compatibility
        - Swaps with currently mounted part (if any)
        - Updates inventory and car data
        Returns True on success, False on failure.
        """
        car_data = self._get_current_car_data()
        if not car_data:
            self._show_feedback(self.app.lang.get("parts_no_car"), ACCENT_RED)
            return False

        car_id = car_data.get('model_id') or car_data.get('name')

        # Check compatibility
        if not is_part_compatible(car_id, part_id):
            car_class = get_car_size_class(car_id)
            part_class = get_part_size_class(part_id)
            msg = self.app.lang.get("parts_incompatible").format(
                part_class=part_class, car_class=car_class
            )
            self._show_feedback(msg, ACCENT_RED)
            self.app.audio.play_sfx('ui_error')
            return False

        # Get current mounted part (if any) to swap back to inventory
        mounted_parts = self._get_mounted_parts(car_data)
        old_part = mounted_parts.get(part_type)

        # Remove new part from inventory
        inv_key = {'engine': 'engines', 'breaks': 'breaks', 'boost': 'boosts'}[part_type]
        if part_id in self.player.inventory.get(inv_key, []):
            self.player.inventory[inv_key].remove(part_id)

        # Add old part back to inventory (if there was one)
        if old_part:
            if inv_key not in self.player.inventory:
                self.player.inventory[inv_key] = []
            self.player.inventory[inv_key].append(old_part)

        # Mount new part
        if 'mounted_parts' not in car_data:
            car_data['mounted_parts'] = {}
        car_data['mounted_parts'][part_type] = part_id

        self._show_feedback(self.app.lang.get("parts_installed"), ACCENT_GREEN)
        self.app.audio.play_sfx('ui_select')
        return True

    def _show_feedback(self, message, color=TEXT_MAIN):
        """Shows a feedback message that fades after some time."""
        self.feedback_message = message
        self.feedback_color = color
        self.feedback_timer = 180  # ~3 seconds at 60fps

    def handle_click_cars(self, mouse_pos):
        """Handles clicks in the CARS tab - car selection."""
        for rect, car_id in self.car_rects:
            if rect.collidepoint(mouse_pos):
                if self.player.set_current_car(car_id):
                    self.app.audio.play_sfx('ui_select')
                else:
                    self.app.audio.play_sfx('ui_error')
                return True
        return False

    def handle_click_parts(self, mouse_pos):
        """Handles clicks in the PARTS tab - part installation."""
        for rect, part_id, part_type in self.part_slots:
            if rect.collidepoint(mouse_pos):
                self._install_part(part_id, part_type)
                return True
        return False

    def update(self, event):
        """Handles input events for both tabs."""
        # Handle button events
        for btn in self.buttons:
            btn.handle_event(event)

        # Handle tab button events
        for btn in self.tab_buttons:
            btn.handle_event(event)

        # Handle mouse clicks based on current tab
        if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
            if self.current_tab == self.TAB_CARS:
                self.handle_click_cars(event.pos)
            elif self.current_tab == self.TAB_PARTS:
                self.handle_click_parts(event.pos)

    def draw(self, screen):
        """Draws the garage menu with the current tab content."""
        screen.fill(BG_COLOR)

        # Update feedback timer every frame (draw is called each frame)
        if self.feedback_timer > 0:
            self.feedback_timer -= 1
            if self.feedback_timer <= 0:
                self.feedback_message = ""

        # Title
        title_surf = self.title_font.render(self.app.lang.get("title_garage"), True, TEXT_MAIN)
        title_rect = title_surf.get_rect(center=(screen.get_width() // 2, 50))
        screen.blit(title_surf, title_rect)

        # Draw tab buttons with active indicator
        for i, btn in enumerate(self.tab_buttons):
            btn.draw(screen)
            # Highlight active tab
            is_active = (i == 0 and self.current_tab == self.TAB_CARS) or \
                       (i == 1 and self.current_tab == self.TAB_PARTS)
            if is_active:
                underline_rect = pg.Rect(btn.rect.left, btn.rect.bottom + 2, btn.rect.width, 3)
                pg.draw.rect(screen, ACCENT_GOLD, underline_rect)

        # Draw tab-specific content
        if self.current_tab == self.TAB_CARS:
            self._draw_cars_tab(screen)
        else:
            self._draw_parts_tab(screen)

        # Draw feedback message if active
        if self.feedback_message:
            feedback_surf = self.info_font.render(self.feedback_message, True, self.feedback_color)
            feedback_rect = feedback_surf.get_rect(center=(screen.get_width() // 2, screen.get_height() - 100))
            screen.blit(feedback_surf, feedback_rect)

        # Draw main buttons (Back)
        for btn in self.buttons:
            btn.draw(screen)

    def _draw_cars_tab(self, screen):
        """Draws the CARS tab content - car grid and mounted parts display."""
        mouse_pos = pg.mouse.get_pos()

        # Draw car grid
        for rect, car_id in self.car_rects:
            sprite = get_car_sprite(self.app.assets, car_id)

            is_selected = (car_id == self.player.current_car)
            is_hovered = rect.collidepoint(mouse_pos)

            # Background color based on state
            bg_color = (60, 60, 70)
            if is_selected:
                bg_color = (40, 70, 40)  # Greenish tint for selected
            elif is_hovered:
                bg_color = (70, 70, 80)  # Lighter for hover

            pg.draw.rect(screen, bg_color, rect, border_radius=10)

            # Border
            border_col = TEXT_DIM
            width = 2
            if is_selected:
                border_col = ACCENT_GOLD
                width = 4
            elif is_hovered:
                border_col = TEXT_MAIN

            pg.draw.rect(screen, border_col, rect, width, border_radius=10)

            # "Selected" label
            if is_selected:
                sel_surf = self.info_font.render(self.app.lang.get("lbl_selected"), True, ACCENT_GOLD)
                sel_rect = sel_surf.get_rect(midbottom=(rect.centerx, rect.top - 5))
                screen.blit(sel_surf, sel_rect)

            # Car sprite
            if sprite:
                target_size = (128, 128)
                scaled = pg.transform.scale(sprite, target_size)
                img_rect = scaled.get_rect(center=rect.center)
                screen.blit(scaled, img_rect)

            # Car name (display name from localization)
            display_name = get_car_display_name(car_id, self.app.lang)
            name_surf = self.small_font.render(display_name, True, TEXT_DIM)
            name_rect = name_surf.get_rect(midtop=(rect.centerx, rect.bottom + 8))
            screen.blit(name_surf, name_rect)

        # Draw mounted parts panel for selected car on the right side
        self._draw_mounted_parts_panel(screen)

    def _draw_mounted_parts_panel(self, screen):
        """
        Draws a panel showing currently mounted parts on the selected car.
        Displayed on the right side of the screen.
        """
        car_data = self._get_current_car_data()
        if not car_data:
            return

        # Panel position and size
        panel_x = screen.get_width() - 280
        panel_y = 180
        panel_w = 260
        panel_h = 200

        # Draw panel background
        panel_rect = pg.Rect(panel_x, panel_y, panel_w, panel_h)
        pg.draw.rect(screen, PANEL_BG, panel_rect, border_radius=10)
        pg.draw.rect(screen, TEXT_DIM, panel_rect, 2, border_radius=10)

        # Panel title
        title_surf = self.info_font.render(self.app.lang.get("parts_mounted_title"), True, TEXT_MAIN)
        title_rect = title_surf.get_rect(midtop=(panel_rect.centerx, panel_y + 10))
        screen.blit(title_surf, title_rect)

        # Get car size class
        car_id = car_data.get('model_id') or car_data.get('name')
        size_class = get_car_size_class(car_id)
        class_surf = self.small_font.render(f"[{self.app.lang.get('parts_size_class')}: {size_class}]", True, ACCENT_GOLD)
        class_rect = class_surf.get_rect(midtop=(panel_rect.centerx, panel_y + 35))
        screen.blit(class_surf, class_rect)

        # Draw mounted parts list
        mounted = self._get_mounted_parts(car_data)
        part_labels = {
            'engine': self.app.lang.get("parts_engine"),
            'breaks': self.app.lang.get("parts_brakes"),
            'boost': self.app.lang.get("parts_boost")
        }

        y_offset = panel_y + 65
        for part_type in self.PART_TYPES:
            part_id = mounted.get(part_type)
            label = part_labels.get(part_type, part_type)

            # Part type label
            label_surf = self.small_font.render(f"{label}:", True, TEXT_DIM)
            screen.blit(label_surf, (panel_x + 15, y_offset))

            # Part value (display name or "None")
            if part_id:
                value_text = get_part_display_name(part_id, self.app.lang)
                value_color = TEXT_MAIN
            else:
                value_text = self.app.lang.get("parts_none")
                value_color = TEXT_DIM

            value_surf = self.small_font.render(value_text, True, value_color)
            screen.blit(value_surf, (panel_x + 15, y_offset + 20))

            y_offset += 45

    def _draw_parts_tab(self, screen):
        """
        Draws the PARTS tab content.
        Shows current car info, part type selector, and inventory parts with compatibility indicators.
        """
        self.part_slots = []  # Reset clickable part slots
        mouse_pos = pg.mouse.get_pos()

        car_data = self._get_current_car_data()
        car_id = None
        car_size = None

        if car_data:
            car_id = car_data.get('model_id') or car_data.get('name')
            car_size = get_car_size_class(car_id)

        # Left panel: Current car and mounted parts summary
        left_panel_x = 50
        left_panel_y = 180
        left_panel_w = 300
        left_panel_h = 350

        left_panel_rect = pg.Rect(left_panel_x, left_panel_y, left_panel_w, left_panel_h)
        pg.draw.rect(screen, PANEL_BG, left_panel_rect, border_radius=10)
        pg.draw.rect(screen, TEXT_DIM, left_panel_rect, 2, border_radius=10)

        # Current car title
        car_title = self.app.lang.get("lbl_current_car")
        car_title_surf = self.info_font.render(car_title, True, TEXT_MAIN)
        screen.blit(car_title_surf, (left_panel_x + 15, left_panel_y + 15))

        if car_data:
            # Draw car sprite
            sprite = get_car_sprite(self.app.assets, car_id)
            if sprite:
                scaled = pg.transform.scale(sprite, (100, 100))
                screen.blit(scaled, (left_panel_x + 15, left_panel_y + 50))

            # Car name and size class (display name from localization)
            display_name = get_car_display_name(car_id, self.app.lang)
            name_surf = self.small_font.render(display_name, True, ACCENT_GOLD)
            screen.blit(name_surf, (left_panel_x + 130, left_panel_y + 55))

            size_text = f"{self.app.lang.get('parts_size_class')}: {car_size}"
            size_surf = self.small_font.render(size_text, True, TEXT_DIM)
            screen.blit(size_surf, (left_panel_x + 130, left_panel_y + 85))

            # Mounted parts summary
            mounted = self._get_mounted_parts(car_data)
            parts_y = left_panel_y + 165

            mounted_title = self.app.lang.get("parts_mounted_title")
            mounted_surf = self.info_font.render(mounted_title, True, TEXT_MAIN)
            screen.blit(mounted_surf, (left_panel_x + 15, parts_y))

            parts_y += 30
            part_labels = {
                'engine': self.app.lang.get("parts_engine"),
                'breaks': self.app.lang.get("parts_brakes"),
                'boost': self.app.lang.get("parts_boost")
            }

            for ptype in self.PART_TYPES:
                part = mounted.get(ptype)
                label = part_labels.get(ptype, ptype)
                text = f"{label}: {part if part else self.app.lang.get('parts_none')}"

                # Highlight the currently selected part type
                color = ACCENT_GOLD if ptype == self.selected_part_type else TEXT_DIM
                surf = self.small_font.render(text, True, color)

                # Make it clickable to switch part type
                text_rect = surf.get_rect(topleft=(left_panel_x + 20, parts_y))
                if text_rect.collidepoint(mouse_pos):
                    surf = self.small_font.render(text, True, TEXT_MAIN)
                    if pg.mouse.get_pressed()[0]:
                        self.selected_part_type = ptype

                screen.blit(surf, text_rect)
                parts_y += 25
        else:
            # No car selected message
            no_car_surf = self.info_font.render(self.app.lang.get("parts_no_car"), True, ACCENT_RED)
            screen.blit(no_car_surf, (left_panel_x + 15, left_panel_y + 60))

        # Part type selector buttons
        selector_y = left_panel_y + left_panel_h + 20
        selector_label = self.app.lang.get("parts_select_type")
        selector_surf = self.info_font.render(selector_label, True, TEXT_MAIN)
        screen.blit(selector_surf, (left_panel_x, selector_y))

        # Draw part type buttons
        btn_y = selector_y + 35
        part_labels = {
            'engine': self.app.lang.get("parts_engine"),
            'breaks': self.app.lang.get("parts_brakes"),
            'boost': self.app.lang.get("parts_boost")
        }

        for i, ptype in enumerate(self.PART_TYPES):
            btn_rect = pg.Rect(left_panel_x + i * 100, btn_y, 90, 35)
            is_active = (ptype == self.selected_part_type)
            is_hover = btn_rect.collidepoint(mouse_pos)

            bg = ACCENT_GOLD if is_active else (BUTTON_HOVER_COLOR if is_hover else BUTTON_COLOR)
            pg.draw.rect(screen, bg, btn_rect, border_radius=5)
            pg.draw.rect(screen, TEXT_DIM, btn_rect, 1, border_radius=5)

            label_surf = self.small_font.render(part_labels[ptype], True, TEXT_MAIN if is_active else TEXT_DIM)
            label_rect = label_surf.get_rect(center=btn_rect.center)
            screen.blit(label_surf, label_rect)

            # Handle click to change part type
            if is_hover and pg.mouse.get_pressed()[0]:
                if self.selected_part_type != ptype:
                    self.selected_part_type = ptype
                    self.app.audio.play_sfx('ui_hover')

        # Right panel: Inventory parts for selected type
        self._draw_inventory_parts_panel(screen, car_id, car_size, mouse_pos)

    def _draw_inventory_parts_panel(self, screen, car_id, car_size, mouse_pos):
        """
        Draws the inventory parts panel showing all parts of the selected type.
        Parts are displayed with compatibility indicators.
        """
        right_panel_x = 380
        right_panel_y = 180
        right_panel_w = screen.get_width() - right_panel_x - 50
        right_panel_h = 400

        right_panel_rect = pg.Rect(right_panel_x, right_panel_y, right_panel_w, right_panel_h)
        pg.draw.rect(screen, PANEL_BG, right_panel_rect, border_radius=10)
        pg.draw.rect(screen, TEXT_DIM, right_panel_rect, 2, border_radius=10)

        # Panel title
        part_labels = {
            'engine': self.app.lang.get("parts_engine"),
            'breaks': self.app.lang.get("parts_brakes"),
            'boost': self.app.lang.get("parts_boost")
        }
        title = f"{self.app.lang.get('parts_inventory')} - {part_labels.get(self.selected_part_type, '')}"
        title_surf = self.info_font.render(title, True, TEXT_MAIN)
        screen.blit(title_surf, (right_panel_x + 15, right_panel_y + 15))

        # Get parts from inventory
        parts = self._get_inventory_parts(self.selected_part_type)

        if not parts:
            # No parts message
            no_parts = self.app.lang.get("parts_empty_inventory")
            no_parts_surf = self.info_font.render(no_parts, True, TEXT_DIM)
            no_parts_rect = no_parts_surf.get_rect(center=(right_panel_rect.centerx, right_panel_rect.centery))
            screen.blit(no_parts_surf, no_parts_rect)
            return

        # Draw parts grid
        slot_w = 180
        slot_h = 60
        cols = max(1, (right_panel_w - 30) // (slot_w + 10))
        start_x = right_panel_x + 15
        start_y = right_panel_y + 50

        for i, part_id in enumerate(parts):
            row = i // cols
            col = i % cols

            slot_x = start_x + col * (slot_w + 10)
            slot_y = start_y + row * (slot_h + 10)

            # Check if slot is within panel bounds
            if slot_y + slot_h > right_panel_y + right_panel_h - 10:
                break  # Don't draw if it would overflow

            slot_rect = pg.Rect(slot_x, slot_y, slot_w, slot_h)

            # Check compatibility
            is_compatible = is_part_compatible(car_id, part_id) if car_id else False
            part_class = get_part_size_class(part_id)

            # Determine background color based on compatibility
            is_hovered = slot_rect.collidepoint(mouse_pos)
            if is_compatible:
                bg_color = (40, 70, 40) if is_hovered else (50, 60, 50)
            else:
                bg_color = (100, 50, 50) if is_hovered else INCOMPATIBLE_BG

            pg.draw.rect(screen, bg_color, slot_rect, border_radius=8)

            # Border
            border_color = ACCENT_GREEN if is_compatible else ACCENT_RED
            if is_hovered:
                border_color = TEXT_MAIN
            pg.draw.rect(screen, border_color, slot_rect, 2, border_radius=8)

            # Part name (display name from localization)
            display_name = get_part_display_name(part_id, self.app.lang)
            name_surf = self.small_font.render(display_name, True, TEXT_MAIN)
            screen.blit(name_surf, (slot_x + 10, slot_y + 8))

            # Compatibility indicator
            if is_compatible:
                compat_text = f"✓ {part_class}"
                compat_color = ACCENT_GREEN
            else:
                compat_text = f"✗ {part_class} ({self.app.lang.get('parts_incompatible_short')})"
                compat_color = ACCENT_RED

            compat_surf = self.small_font.render(compat_text, True, compat_color)
            screen.blit(compat_surf, (slot_x + 10, slot_y + 35))

            # Store slot for click detection
            self.part_slots.append((slot_rect, part_id, self.selected_part_type))

        # Legend at the bottom of the panel
        legend_y = right_panel_y + right_panel_h - 30
        legend_text = self.app.lang.get("parts_click_to_install")
        legend_surf = self.small_font.render(legend_text, True, TEXT_DIM)
        legend_rect = legend_surf.get_rect(midbottom=(right_panel_rect.centerx, legend_y))
        screen.blit(legend_surf, legend_rect)