# Release Process Guide 🚢

This document serves as a checklist and guide for the maintainer to perform a new release of **CA-Racing**.

## ✅ Pre-Release Checklist

*   [ ] **Code Freeze**: ensure all intended features and fixes are merged into `main`.
*   [ ] **Testing**: Verify that the game runs correctly on Linux and Windows.
*   [ ] **Version Bump**: Update version numbers in:
    *   `src/core/constants.py` (if applicable)
    *   `packaging/aur/PKGBUILD`
    *   `packaging/rpm/ca-racing.spec`
    *   `packaging/deb/ca-racing-*/DEBIAN/control`
*   [ ] **Build Assets**: Run `./build_dist.sh` to generate the latest binaries.

---

## 🚀 Release Steps

### 1. Push Changes to GitHub

Ensure your local repository is up to date and push the latest commits.

```bash
git push origin main
```

### 2. Create and Push Tag

Create a new annotated tag for the release version (e.g., `v0.2.0-alpha`).

```bash
# Create tag
git tag -a v0.2.0-alpha -m "Release v0.2.0-alpha"

# Push tag
git push origin v0.2.0-alpha
```

### 3. Create GitHub Release

1.  Navigate to [Releases](https://github.com/piotrek1372/ca-racing/releases).
2.  Draft a new release using the tag you just pushed.
3.  **Title**: `CA-Racing v0.2.0-alpha`
4.  **Description**: Generate release notes highlighting new features, bug fixes, and improvements.
5.  **Assets**: Upload the following build artifacts:
    *   `CA-Racing-0.2.0-alpha-setup.run` (Linux Installer)
    *   `CA-Racing-0.2.0-alpha-setup.AppImage` (Linux Portable)
    *   `CA-Racing-Setup.exe` (Windows Installer)

### 4. Post-Release Packaging Updates

After the release is published on GitHub, you must update the distribution package checksums.

#### Arch Linux (AUR)

1.  Navigate to `packaging/aur`.
2.  Update `sha256sums` in `PKGBUILD` using the new release files.
3.  Generate `.SRCINFO`:
    ```bash
    makepkg --printsrcinfo > .SRCINFO
    ```
4.  Commit and push changes to the AUR repository.

#### Other Distributions

*   **Debian/Ubuntu PPA**: Update the source package and upload to Launchpad.
*   **Fedora COPR**: Update the SRPM and trigger a new build.

---

## 🔍 Verification

After the release process is complete, perform a final check:

*   [ ] Confirm the GitHub Release is public and assets are downloadable.
*   [ ] Verify the AUR package installs correctly (`yay -S ca-racing`).
*   [ ] Check that installation instructions in `README.md` are still accurate.
