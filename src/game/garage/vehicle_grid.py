# -*- coding: utf-8 -*-
"""
Ten plik definiuje klasę VehicleGrid, która reprezentuje strukturę
pojazdu jako siatk<PERSON> komórek (VehicleCell).
"""
import json
from .vehicle_cell import VehicleCell

class VehicleGrid:
    """
    Zarządza siatką komórek pojazdu, ich układem i serializacją.
    """
    def __init__(self, width, height):
        if not (isinstance(width, int) and width > 0 and isinstance(height, int) and height > 0):
            raise ValueError("Szerokość i wysokość siatki muszą być dodatnimi liczbami całkowitymi.")
        self.width = width
        self.height = height
        self.grid = [[None for _ in range(width)] for _ in range(height)]

    def set_cell(self, x, y, cell: VehicleCell):
        """Umieszcza komórkę na siatce w podanych koordynatach."""
        if 0 <= x < self.width and 0 <= y < self.height:
            self.grid[y][x] = cell
        else:
            raise IndexError("Koordynaty poza zakresem siatki.")

    def get_cell(self, x, y):
        """Pobiera komórkę z podanych koordynatów."""
        if 0 <= x < self.width and 0 <= y < self.height:
            return self.grid[y][x]
        return None

    def get_neighbors(self, x, y):
        """Zwraca listę sąsiadów dla danej komórki (do 8)."""
        neighbors = []
        for i in range(-1, 2):
            for j in range(-1, 2):
                if i == 0 and j == 0:
                    continue
                nx, ny = x + j, y + i
                if 0 <= nx < self.width and 0 <= ny < self.height:
                    neighbor_cell = self.grid[ny][nx]
                    if neighbor_cell:
                        neighbors.append(neighbor_cell)
        return neighbors

    def iter_cells(self):
        """Iterator zwracający (x, y, cell) dla każdej komórki na siatce."""
        for y in range(self.height):
            for x in range(self.width):
                cell = self.grid[y][x]
                if cell:
                    yield x, y, cell

    def to_json(self):
        """
        Serializuje całą siatkę pojazdu do stringa JSON.
        """
        grid_data = [
            [cell.to_dict() if cell else None for cell in row]
            for row in self.grid
        ]
        return json.dumps({"width": self.width, "height": self.height, "grid": grid_data}, indent=4)

    @classmethod
    def from_json(cls, json_string):
        """
        Odtwarza pojazd z jego reprezentacji JSON.
        """
        data = json.loads(json_string)
        instance = cls(data['width'], data['height'])
        instance.grid = [
            [VehicleCell.from_dict(cell_data) if cell_data else None for cell_data in row]
            for row in data['grid']
        ]
        return instance
