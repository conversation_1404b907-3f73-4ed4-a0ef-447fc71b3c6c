#!/bin/bash
# Build script for PyInstaller distribution - VERBOSE MODE

set -e  # Exit on any error

echo "🔨 Building CA-Racing distribution..."
echo "======================================"
echo "Working directory: $(pwd)"
echo "Python version: $(python --version)"
echo "PyInstaller version: $(pyinstaller --version)"
echo "======================================"

# Check if spec file exists
if [ ! -f "ca-racing.spec" ]; then
    echo "❌ ERROR: ca-racing.spec not found!"
    exit 1
fi

echo "✅ Spec file found"
echo "======================================"
echo "Running PyInstaller..."
echo "======================================"

# Run PyInstaller with verbose output
pyinstaller --clean --log-level=DEBUG ca-racing.spec 2>&1 | tee pyinstaller.log

# Check if build succeeded
if [ ! -f "dist/CA-Racing" ] && [ ! -f "dist/CA-Racing.exe" ]; then
    echo "======================================"
    echo "❌ BUILD FAILED - Executable not created!"
    echo "======================================"
    echo "Checking dist directory:"
    ls -la dist/ || echo "dist/ directory does not exist"
    echo "======================================"
    echo "Last 50 lines of PyInstaller log:"
    tail -50 pyinstaller.log || echo "No log file"
    echo "======================================"
    exit 1
fi

echo "======================================"
echo "✅ Build complete!"
echo "Checking output files:"
ls -lh dist/
echo "======================================"