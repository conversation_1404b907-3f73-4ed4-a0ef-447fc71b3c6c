# -*- mode: python ; coding: utf-8 -*-

# #############################################################################
# Specification file for PyInstaller - CA-Racing
#
# This file is used by PyInstaller to package the Python application
# into a distributable package for Windows.
#
# To build the application, use the command in the terminal:
# pyinstaller CA-Racing.spec
# #############################################################################

import PyInstaller.utils.hooks
import os
import sys

# Add the project root to the Python path to allow importing 'src'
# Use os.getcwd() because __file__ is not defined in PyInstaller exec context
project_root = os.path.abspath(os.getcwd())
sys.path.insert(0, project_root)

from src.constants import APP_VERSION

# --- EXE file version information ---
# This metadata will be embedded in the .exe file and will be visible
# in the "Properties" window of the file in Windows.
# PyInstaller expects version in format X.Y.Z.W
# Clean up version for Windows (must be X.Y.Z.W)
# If APP_VERSION is '1.0.0-demo', we want '*******'
import re
base_version_match = re.match(r"(\d+\.\d+\.\d+)", APP_VERSION)
if base_version_match:
    version_for_windows = base_version_match.group(1) + ".0"
else:
    version_for_windows = "*******" # Fallback

version_info = {
    'version': version_for_windows,
    'file_description': 'CA-Racing Game',
    'company_name': 'Piotrek2713',
    'product_name': 'CA-Racing',
    'internal_name': 'CA-Racing'
}


# --- Main build configuration ---

# Analysis: Analyzes the main script and all its dependencies.
# In this section, we define what is to be included in the application.
a = Analysis(
    ['main.py'],
    pathex=[project_root],
    binaries=[],
    # Data and resources: Includes files and directories that are not Python code.
    # Format: ('source/path', 'destination/path/in/package')
    datas=[
        ('assets', 'assets'),
        ('data', 'data'),
        ('maps','maps')
    ],
    # Hidden imports: Explicitly includes modules that PyInstaller
    # might not find automatically (e.g., used by other libraries).
    hiddenimports=['pygame', 'numpy', 'PIL', 'pytmx'],
    hookspath=[],
    hooksconfig={},
    runtime_hooks=[],
    excludes=[],
    # Hides the console window when running the application.
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher_block_size=16,
    noarchive=False
)

# PYZ (Python Zipped Archive): Creates a compressed archive
# containing all Python modules.
pyz = PYZ(a.pure, a.zipped_data, cipher=None)

# EXE: Creates the main executable file (.exe) with embedded metadata.
exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    name='CA-Racing',
    debug=True,
    bootloader_ignore_signals=False,
    strip=False,
    # UPX compression: Enables compression of the executable file and libraries.
    # Requires specifying the path to the directory with the upx.exe file.
    # upx=True, # Set to True if UPX is in PATH
    # upx_dir='[path/to/upx/directory]',
    runtime_tmpdir=None,
    # Application icon.
    icon='assets/images/icon.ico',
    # Graphics mode: Setting 'console=False' hides the black console window.
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    # Embedding version information in the .exe file
    # version='file_version_info.txt' # Alternative method from a text file
)
