"""
server_race_manager.py - Server-Side Race Logic
================================================
Manages the state of a multiplayer race on the server, including laps,
waypoints, and finish conditions.
"""

import time
from typing import Dict, List, Optional

class ServerRaceManager:
    """
    Manages the logic and state for a single race instance on the server.
    """
    def __init__(self, players: List[str], track_data: Dict):
        """
        Initializes the race manager.

        Args:
            players: A list of player IDs participating in the race.
            track_data: Data for the track, including waypoints.
        """
        self.players = {player_id: self._create_player_state() for player_id in players}
        self.waypoints = track_data.get('waypoints', [])
        self.total_laps = track_data.get('laps', 3)
        self.race_start_time = time.time()
        self.is_finished = False

    def _create_player_state(self) -> Dict:
        """Creates the initial state for a player."""
        return {
            'lap': 0,
            'current_waypoint': 0,
            'last_lap_time': 0,
            'best_lap_time': 0,
            'finish_time': 0,
            'is_finished': False
        }

    def update_player_progress(self, player_id: str, car_position: Dict) -> Optional[Dict]:
        """
        Updates the race progress for a single player.

        Args:
            player_id: The ID of the player to update.
            car_position: The current position of the player's car.

        Returns:
            A dictionary with event information if a lap is completed or the race is finished.
        """
        player_state = self.players.get(player_id)
        if not player_state or player_state['is_finished']:
            return None

        # Check for waypoint progression
        self._check_waypoints(player_state, car_position)

        # Check for lap completion
        return self._check_finish_line(player_id, player_state)

    def _check_waypoints(self, player_state: Dict, car_position: Dict):
        """Checks if the player has passed the next waypoint."""
        if player_state['current_waypoint'] >= len(self.waypoints):
            return

        waypoint = self.waypoints[player_state['current_waypoint']]
        distance_to_waypoint = self._calculate_distance(car_position, waypoint)
        
        # Simple distance check for waypoint completion
        if distance_to_waypoint < 300: # Using a radius of 300
            player_state['current_waypoint'] += 1

    def _check_finish_line(self, player_id: str, player_state: Dict) -> Optional[Dict]:
        """Checks if the player has crossed the finish line to complete a lap."""
        # This is a simplified check. A real implementation would use a finish line trigger.
        # For now, we'll assume a lap is complete if all waypoints are passed.
        if player_state['current_waypoint'] >= len(self.waypoints):
            player_state['lap'] += 1
            player_state['current_waypoint'] = 0

            # Calculate lap time
            current_time = time.time()
            lap_time = current_time - player_state.get('lap_start_time', self.race_start_time)
            player_state['lap_start_time'] = current_time
            player_state['last_lap_time'] = lap_time
            if not player_state['best_lap_time'] or lap_time < player_state['best_lap_time']:
                player_state['best_lap_time'] = lap_time

            # Check for race finish
            if player_state['lap'] >= self.total_laps:
                player_state['is_finished'] = True
                player_state['finish_time'] = current_time - self.race_start_time
                self._check_race_finish()
                return {'event': 'race_finish', 'player_id': player_id, 'data': player_state}
            
            return {'event': 'lap_complete', 'player_id': player_id, 'data': player_state}
        
        return None

    def _check_race_finish(self):
        """Checks if all players have finished the race."""
        if all(p['is_finished'] for p in self.players.values()):
            self.is_finished = True

    def _calculate_distance(self, pos1: Dict, pos2: Dict) -> float:
        """Calculates the distance between two points."""
        return ((pos1['x'] - pos2['x']) ** 2 + (pos1['y'] - pos2['y']) ** 2) ** 0.5

    def get_race_standings(self) -> List[Dict]:
        """Returns the current race standings."""
        standings = sorted(
            self.players.items(),
            key=lambda item: (not item[1]['is_finished'], item[1]['finish_time'] if item[1]['is_finished'] else -item[1]['lap'], item[1]['current_waypoint']),
            reverse=True
        )
        return [{'player_id': pid, **pstate} for pid, pstate in standings]