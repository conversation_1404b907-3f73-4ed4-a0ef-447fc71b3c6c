#!/bin/bash

# CA-Racing Deployment Script
# Usage: ./deploy_to_vm.sh <USER> <VM_IP> [DEST_DIR] [SSH_KEY_PATH]

if [ "$#" -lt 2 ]; then
    echo "Usage: $0 <USER> <VM_IP> [DEST_DIR] [SSH_KEY_PATH]"
    echo "Example: $0 piotrek ************ /opt/ca-racing-server ~/.ssh/google_compute_engine"
    exit 1
fi

USER=$1
VM_IP=$2
DEST_DIR=${3:-/opt/ca-racing-server}
SSH_KEY=$4

echo "========================================"
echo "  CA-Racing Deployment to GCP VM"
echo "========================================"
echo "User: $USER"
echo "VM IP: $VM_IP"
echo "Destination: $DEST_DIR"

RSYNC_RSH=""

if [ -n "$SSH_KEY" ]; then
    # Auto-correct if user passed public key (.pub)
    if [[ "$SSH_KEY" == *.pub ]]; then
        PRIVATE_KEY="${SSH_KEY%.pub}"
        if [ -f "$PRIVATE_KEY" ]; then
            echo "⚠️  Warning: You provided a public key ($SSH_KEY)."
            echo "   Automatically switching to private key: $PRIVATE_KEY"
            SSH_KEY="$PRIVATE_KEY"
        fi
    fi

    if [ ! -f "$SSH_KEY" ]; then
        echo "❌ Error: SSH key file not found: $SSH_KEY"
        exit 1
    fi

    echo "SSH Key: $SSH_KEY"
    RSYNC_RSH="ssh -i $SSH_KEY"
elif [ -f "$HOME/.ssh/google_compute_engine" ]; then
    echo "Auto-detected GCP key: $HOME/.ssh/google_compute_engine"
    RSYNC_RSH="ssh -i $HOME/.ssh/google_compute_engine"
else
    echo "SSH Key: Default (Agent or id_rsa)"
fi
echo "========================================"

# Check if rsync is installed locally
if ! command -v rsync &> /dev/null; then
    echo "❌ Error: rsync is not installed locally. Please install it first."
    exit 1
fi

echo "Checking remote rsync..."
# Check if rsync is installed on the remote server
CHECK_CMD="command -v rsync >/dev/null 2>&1 || { echo >&2 'rsync_missing'; exit 1; }"

if [ -n "$RSYNC_RSH" ]; then
    ssh_check_cmd="$RSYNC_RSH $USER@$VM_IP \"$CHECK_CMD\""
else
    ssh_check_cmd="ssh $USER@$VM_IP \"$CHECK_CMD\""
fi

# Execute check
eval $ssh_check_cmd 2> /tmp/deploy_check.log

if [ $? -ne 0 ]; then
    if grep -q "rsync_missing" /tmp/deploy_check.log; then
        echo "❌ Error: 'rsync' is NOT installed on the remote VM."
        echo "   Please install it on the VM first:"
        echo "   Run: ssh $USER@$VM_IP 'sudo apt-get update && sudo apt-get install -y rsync'"
        rm /tmp/deploy_check.log
        exit 1
    else
        echo "⚠️  Warning: Could not verify remote rsync (connection issue?). Proceeding anyway..."
    fi
else
    echo "✅ Remote rsync found."
fi
rm -f /tmp/deploy_check.log

# Ensure destination directory exists and has permissions
echo "Preparing remote directory..."
MKDIR_CMD="sudo mkdir -p $DEST_DIR && sudo chown $USER:$USER $DEST_DIR"

if [ -n "$RSYNC_RSH" ]; then
    ssh_mkdir_cmd="$RSYNC_RSH $USER@$VM_IP \"$MKDIR_CMD\""
else
    ssh_mkdir_cmd="ssh $USER@$VM_IP \"$MKDIR_CMD\""
fi

eval $ssh_mkdir_cmd
if [ $? -ne 0 ]; then
    echo "❌ Error: Failed to create/own remote directory $DEST_DIR"
    echo "   Ensure you have sudo privileges on the VM."
    exit 1
fi
echo "✅ Remote directory ready."

echo "Starting transfer..."

# Construct rsync command
CMD="rsync -avz --progress"

if [ -n "$RSYNC_RSH" ]; then
    CMD="$CMD -e '$RSYNC_RSH'"
fi

# Add exclusions and paths
CMD="$CMD \
  --exclude 'assets/' \
  --exclude 'maps/' \
  --exclude 'data/' \
  --exclude 'docs_ai/' \
  --exclude 'packaging/' \
  --exclude '.git/' \
  --exclude '.github/' \
  --exclude '__pycache__/' \
  --exclude '*.pyc' \
  --exclude 'venv/' \
  --exclude 'env/' \
  --exclude 'test_client.py' \
  --exclude 'deploy_to_vm.sh' \
  --exclude 'dist/' \
  --exclude 'build' \
  --exclude 'CA-Racing.exe' \
  --exclude 'appimagetool' \
  ./ $USER@$VM_IP:$DEST_DIR"

# Execute
eval $CMD

if [ $? -eq 0 ]; then
    echo ""
    echo "✅ Deployment successful!"
    echo "Next steps:"
    if [ -n "$SSH_KEY" ]; then
        echo "1. SSH into the VM: ssh -i $SSH_KEY $USER@$VM_IP"
    elif [ -f "$HOME/.ssh/google_compute_engine" ]; then
        echo "1. SSH into the VM: ssh -i ~/.ssh/google_compute_engine $USER@$VM_IP"
    else
        echo "1. SSH into the VM: ssh $USER@$VM_IP"
    fi
    echo "2. Go to directory: cd $DEST_DIR"
    echo "3. Start server:    docker-compose up -d --build"
else
    echo ""
    echo "❌ Deployment failed."
    echo "Tip: If you see 'rsync: command not found', install rsync on the VM."
fi
