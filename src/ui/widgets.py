import pygame as pg
from src.constants import *

class Button:
    def __init__(self, text, center_pos, action, app=None, custom_color=None, min_width=280):
        """
        :param app: Reference to main App for Audio access (optional).
        :param min_width: Minimum button width (default 280 for consistency).
        """
        self.text = text
        self.action = action
        self.app = app  # Needed for sound
        self.font = pg.font.SysFont('Consolas', 35, bold=True)
        self.color_idle = custom_color if custom_color else BUTTON_COLOR
        self.color_hover = BUTTON_HOVER_COLOR
        self.text_color = TEXT_MAIN
        self.border_color = TEXT_DIM
        
        text_width, text_height = self.font.size(self.text)
        # Use minimum width but ensure text fits with padding
        button_width = max(min_width, text_width + 40)
        self.rect = pg.Rect(0, 0, button_width, text_height + 20)
        self.rect.center = center_pos
        
        self.shadow_rect = self.rect.copy()
        self.shadow_rect.x += 4
        self.shadow_rect.y += 4
        
        self.text_surf = self.font.render(self.text, True, self.text_color)
        self.text_rect = self.text_surf.get_rect(center=self.rect.center)
        
        self.is_hovered = False

    def draw(self, screen):
        mouse_pos = pg.mouse.get_pos()
        hover_now = self.rect.collidepoint(mouse_pos)
        
        # Play hover sound only on state change
        if hover_now and not self.is_hovered:
            if self.app and hasattr(self.app, 'audio'):
                self.app.audio.play_sfx('ui_hover')
        
        self.is_hovered = hover_now
        col = self.color_hover if self.is_hovered else self.color_idle
        
        pg.draw.rect(screen, (15, 15, 20), self.shadow_rect, border_radius=8)
        pg.draw.rect(screen, col, self.rect, border_radius=8)
        pg.draw.rect(screen, self.border_color, self.rect, 2, border_radius=8)
        screen.blit(self.text_surf, self.text_rect)

    def handle_event(self, event):
        if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
            if self.rect.collidepoint(event.pos):
                # Play Click Sound
                if self.app and hasattr(self.app, 'audio'):
                    if self.action: # Validation sound vs Success sound
                        self.app.audio.play_sfx('ui_click')
                    else:
                        self.app.audio.play_sfx('ui_error')
                
                if self.action:
                    self.action()

class InputBox:
    def __init__(self, center_pos, initial_text='', app=None):
        self.app = app
        self.font = pg.font.SysFont('Consolas', 35, bold=True)
        self.rect = pg.Rect(0, 0, 300, 50)
        self.rect.center = center_pos
        self.color = TEXT_DIM
        self.text = initial_text
        self.txt_surface = self.font.render(self.text, True, TEXT_MAIN)
        self.active = True

    def handle_event(self, event):
        if event.type == pg.KEYDOWN and self.active:
            if event.key == pg.K_RETURN:
                if self.app: self.app.audio.play_sfx('ui_select')
            elif event.key == pg.K_BACKSPACE:
                self.text = self.text[:-1]
                if self.app: self.app.audio.play_sfx('ui_hover') # Typing sound effect
            else:
                if len(self.text) < 12:
                    self.text += event.unicode
                    if self.app: self.app.audio.play_sfx('ui_hover')
            self.txt_surface = self.font.render(self.text, True, TEXT_MAIN)

    def draw(self, screen):
        pg.draw.rect(screen, (0,0,0), self.rect)
        pg.draw.rect(screen, self.color, self.rect, 2)
        text_rect = self.txt_surface.get_rect(center=self.rect.center)
        screen.blit(self.txt_surface, text_rect)

class SegmentedControl:
    """
    A horizontal segmented control for selecting one value from a set.
    """
    def __init__(self, rect, options, initial_option, on_change_callback, app=None):
        self.rect = rect
        self.options = options # List of values
        self.selected_option = initial_option
        self.on_change = on_change_callback
        self.app = app
        self.font = pg.font.SysFont('Consolas', 20, bold=True)
        
        # Calculate segments
        self.segment_width = rect.width / len(options)
        self.segments = [] # List of (rect, value)
        
        for i, option in enumerate(options):
            seg_rect = pg.Rect(
                rect.x + i * self.segment_width,
                rect.y,
                self.segment_width,
                rect.height
            )
            self.segments.append((seg_rect, option))

    def handle_event(self, event):
        if event.type == pg.MOUSEBUTTONDOWN and event.button == 1:
            if self.rect.collidepoint(event.pos):
                for seg_rect, option in self.segments:
                    if seg_rect.collidepoint(event.pos):
                        if self.selected_option != option:
                            self.selected_option = option
                            if self.app: self.app.audio.play_sfx('ui_click')
                            if self.on_change:
                                self.on_change(option)
                        return

    def draw(self, screen):
        # Background
        pg.draw.rect(screen, (40, 40, 45), self.rect, border_radius=8)
        
        # Draw segments
        for i, (seg_rect, option) in enumerate(self.segments):
            is_selected = (option == self.selected_option)
            mouse_pos = pg.mouse.get_pos()
            is_hovered = seg_rect.collidepoint(mouse_pos)
            
            # Determine color
            if is_selected:
                # Use a color based on the option if it's difficulty
                if option == "EASY": color = ACCENT_GREEN
                elif option == "MEDIUM": color = ACCENT_BLUE
                elif option == "HARD": color = ACCENT_RED
                else: color = ACCENT_GOLD
            else:
                color = (60, 60, 70) if not is_hovered else (80, 80, 90)
            
            # Draw segment background
            # If it's the first or last segment, round corners appropriately
            if is_selected:
                pg.draw.rect(screen, color, seg_rect, border_radius=8 if len(self.options) == 1 else 0)
                # Correction for rounded corners visually if needed, but simple rect is okay for internal segments
                # For better visuals, we can redraw the border radius on top
            elif is_hovered:
                pg.draw.rect(screen, color, seg_rect)

            # Text
            text_color = TEXT_MAIN if is_selected else TEXT_DIM
            text_surf = self.font.render(str(option), True, text_color)
            text_rect = text_surf.get_rect(center=seg_rect.center)
            screen.blit(text_surf, text_rect)
            
            # Draw separator lines
            if i > 0:
                pg.draw.line(screen, (30, 30, 35), (seg_rect.x, seg_rect.y), (seg_rect.x, seg_rect.bottom), 2)

        # Main Border
        pg.draw.rect(screen, TEXT_DIM, self.rect, 2, border_radius=8)