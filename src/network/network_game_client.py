"""
NetworkGameClient - Async Wrapper for NetworkClient
====================================================
Provides async-compatible API wrapping the synchronous NetworkClient.
Needed for compatibility with MultiplayerSession which expects async methods.
"""

import asyncio
import logging
from typing import Optional, Callable
from src.network.network_client import NetworkClient

logger = logging.getLogger(__name__)


class NetworkGameClient:
    """
    Async wrapper around synchronous NetworkClient.
    
    Provides async-compatible methods while using the thread-based
    NetworkClient under the hood.
    """
    
    def __init__(self):
        self._client: Optional[NetworkClient] = None
        self._player_id: Optional[str] = None
        self._host: Optional[str] = None
        self._port: Optional[int] = None
        
        # Callbacks (will be set by MultiplayerSession)
        self.on_connected: Optional[Callable] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_game_state: Optional[Callable] = None
        self.on_room_assigned: Optional[Callable] = None
        self.on_race_start: Optional[Callable] = None
        self.on_player_joined: Optional[Callable] = None
        self.on_player_left: Optional[Callable] = None
        self.on_room_list: Optional[Callable] = None  # ✅ NEW: For lobby room list updates
    
    async def connect(self, host: str, port: int, timeout: float = 15.0) -> bool:
        """
        Connect to game server (async wrapper).
        
        Args:
            host: Server IP address
            port: Server port
            timeout: Connection timeout (not used with sync client)
            
        Returns:
            True if connected successfully
        """
        self._host = host
        self._port = port
        
        # Create synchronous client
        self._client = NetworkClient(host, port)
        
        # Set up callbacks to forward messages
        self._client.set_callbacks(
            on_message=self._handle_message,
            on_disconnect=self._handle_disconnect
        )
        
        # Connect (this is synchronous but quick)
        success = self._client.connect()
        
        if success:
            logger.info(f"NetworkGameClient connected to {host}:{port}")
            if self.on_connected:
                self.on_connected()
        
        return success
    
    async def disconnect(self):
        """Disconnect from server (async wrapper)"""
        if self._client:
            self._client.disconnect()
            self._client = None
    
    async def send_message(self, message: dict):
        """Send message to server (async wrapper)"""
        if self._client:
            return self._client.send_message(message)
        return False
    
    def get_player_id(self) -> Optional[str]:
        """Get player ID assigned by server"""
        return self._player_id
    
    def is_connected(self) -> bool:
        """Check if connected to server"""
        return self._client is not None and self._client.is_connected()
    
    def _handle_message(self, message: dict):
        """Handle incoming message from server"""
        msg_type = message.get('type')
        
        logger.info(f"📨 Received message: {msg_type}")
        
        if msg_type == 'connected':
            # Server sent us our player ID
            self._player_id = message.get('player_id')
            logger.info(f"✅ Assigned player ID: {self._player_id}")
        
        elif msg_type == 'room_list':
            # ✅ FIX: Call specific room_list callback for lobby
            logger.info(f"📋 Received room list: {len(message.get('rooms', []))} rooms")
            if self.on_room_list:
                self.on_room_list(message)
            # Also forward via on_game_state for compatibility
            if self.on_game_state:
                self.on_game_state(message)
        
        elif msg_type == 'room_assigned':
            room_id = message.get('room_id')
            logger.info(f"🚪 Room assigned: {room_id}")
            if self.on_room_assigned:
                self.on_room_assigned(message)
        
        elif msg_type == 'race_start':
            logger.info("🏁 Race starting!")
            if self.on_race_start:
                self.on_race_start(message)
        
        elif msg_type == 'game_state':
            # Server authoritative state update (frequent, don't log)
            if self.on_game_state:
                self.on_game_state(message)
        
        elif msg_type == 'player_joined':
            logger.info(f"👋 Player joined: {message.get('player_id')}")
            if self.on_player_joined:
                self.on_player_joined(message)
        
        elif msg_type == 'player_left':
            logger.info(f"👋 Player left: {message.get('player_id')}")
            if self.on_player_left:
                self.on_player_left(message)
        
        elif msg_type == 'error':
            error_msg = message.get('message', 'Unknown error')
            logger.error(f"❌ Server error: {error_msg}")
        
        else:
            logger.warning(f"⚠️ Unknown message type: {msg_type}")
    
    def _handle_disconnect(self):
        """Handle disconnection from server"""
        logger.info("Disconnected from server")
        if self.on_disconnected:
            self.on_disconnected("Connection lost")
    
    # Additional methods for game functionality
    
    async def request_room_list(self, page: int = 0):
        """Request list of available rooms"""
        await self.send_message({
            'type': 'request_room_list',
            'page': page
        })
    
    async def create_room(self, room_name: str, host_name: str, track_name: str):
        """Create a new room"""
        await self.send_message({
            'type': 'create_room',
            'room_name': room_name,
            'host_name': host_name,
            'track_name': track_name
        })
    
    async def join_room(self, room_id: int):
        """Join an existing room"""
        await self.send_message({
            'type': 'join_room',
            'room_id': room_id
        })
    
    async def leave_room(self):
        """Leave current room"""
        await self.send_message({
            'type': 'leave_room'
        })
    
    async def set_ready(self, ready: bool = True):
        """Mark player as ready/not ready"""
        await self.send_message({
            'type': 'ready' if ready else 'not_ready'
        })
    
    async def send_input(self, throttle: float, steering: float, is_braking: float):
        """Send player input to server"""
        await self.send_message({
            'type': 'input',
            'throttle': throttle,
            'steering': steering,
            'is_braking': is_braking
        })
    
    async def send_player_info(self, car_model: str):
        """Send player info (car model, etc.)"""
        await self.send_message({
            'type': 'player_info',
            'car_model': car_model
        })
    
    async def ping(self):
        """Send ping to measure latency"""
        import time
        await self.send_message({
            'type': 'ping',
            'timestamp': time.time()
        })