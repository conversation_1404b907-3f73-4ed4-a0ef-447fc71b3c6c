"""
Client Discovery - UDP Broadcast Scanner
=========================================
Client-side component for discovering CA-Racing servers on the local network.

Broadcasts discovery packets and collects responses from available servers.
"""

import socket
import json
import logging
import time
from typing import List, Optional
from dataclasses import dataclass

logger = logging.getLogger(__name__)


@dataclass
class DiscoveredServer:
    """Information about a discovered server"""
    ip: str
    port: int
    server_name: str
    total_players: int
    total_rooms: int
    available_rooms: int
    version: str
    ping_ms: float = 0.0
    
    def __str__(self):
        return f"{self.server_name} ({self.ip}:{self.port}) - {self.total_players} players, {self.available_rooms}/{self.total_rooms} rooms"


class ServerDiscovery:
    """
    Client-side server discovery via UDP broadcast.
    
    Scans the local network for CA-Racing servers and returns
    a list of discovered servers with their information.
    """
    
    DISCOVERY_PORT = 8445
    MAGIC_PACKET = b"CA_RACING_DISCOVERY"
    BROADCAST_ADDRESS = "***************"
    
    def __init__(self):
        self.discovered_servers: List[DiscoveredServer] = []
    
    def scan_for_servers(self, timeout: float = 3.0, max_servers: int = 10) -> List[DiscoveredServer]:
        """
        Scan the local network for CA-Racing servers.
        
        Args:
            timeout: How long to wait for responses (seconds)
            max_servers: Maximum number of servers to discover
            
        Returns:
            List of discovered servers
        """
        self.discovered_servers = []
        
        try:
            # Create UDP socket
            sock = socket.socket(socket.AF_INET, socket.SOCK_DGRAM)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_BROADCAST, 1)
            sock.setsockopt(socket.SOL_SOCKET, socket.SO_REUSEADDR, 1)
            sock.settimeout(timeout)
            
            # Bind to any available port
            sock.bind(('', 0))
            
            logger.info(f"🔍 Scanning for servers (timeout: {timeout}s)...")
            
            # Send broadcast packet
            start_time = time.time()
            sock.sendto(self.MAGIC_PACKET, (self.BROADCAST_ADDRESS, self.DISCOVERY_PORT))
            
            # Also try localhost explicitly (for local testing)
            try:
                sock.sendto(self.MAGIC_PACKET, ('127.0.0.1', self.DISCOVERY_PORT))
            except:
                pass
            
            # Collect responses
            server_ips = set()  # Track unique servers by IP
            
            while time.time() - start_time < timeout and len(self.discovered_servers) < max_servers:
                try:
                    # Receive response
                    data, addr = sock.recvfrom(4096)
                    receive_time = time.time()
                    
                    # Skip duplicate responses from same IP
                    if addr[0] in server_ips:
                        continue
                    
                    # Parse server info
                    server_info = json.loads(data.decode('utf-8'))
                    
                    if server_info.get('type') == 'server_info':
                        ping_ms = (receive_time - start_time) * 1000
                        
                        server = DiscoveredServer(
                            ip=addr[0],
                            port=server_info.get('tcp_port', 8444),
                            server_name=server_info.get('server_name', 'Unknown Server'),
                            total_players=server_info.get('total_players', 0),
                            total_rooms=server_info.get('total_rooms', 0),
                            available_rooms=server_info.get('available_rooms', 0),
                            version=server_info.get('version', '1.0.0'),
                            ping_ms=ping_ms
                        )
                        
                        self.discovered_servers.append(server)
                        server_ips.add(addr[0])
                        
                        logger.info(f"✅ Found server: {server}")
                
                except socket.timeout:
                    # Timeout reached, stop listening
                    break
                except json.JSONDecodeError:
                    logger.warning(f"Invalid response from {addr[0]}")
                except Exception as e:
                    logger.error(f"Error receiving response: {e}")
            
            sock.close()
            
            if self.discovered_servers:
                logger.info(f"✅ Discovery complete: Found {len(self.discovered_servers)} server(s)")
            else:
                logger.info("⚠️ No servers found on the network")
            
            return self.discovered_servers
            
        except Exception as e:
            logger.error(f"Error during server discovery: {e}")
            return []
    
    def get_servers(self) -> List[DiscoveredServer]:
        """Get list of discovered servers from last scan"""
        return self.discovered_servers
    
    def clear(self):
        """Clear the list of discovered servers"""
        self.discovered_servers = []


# Convenience function for quick scanning
def find_servers(timeout: float = 3.0) -> List[DiscoveredServer]:
    """
    Quick function to scan for servers.
    
    Args:
        timeout: Scan timeout in seconds
        
    Returns:
        List of discovered servers
    """
    discovery = ServerDiscovery()
    return discovery.scan_for_servers(timeout)


if __name__ == "__main__":
    # Test discovery
    logging.basicConfig(level=logging.INFO)
    print("Scanning for CA-Racing servers...")
    servers = find_servers(timeout=3.0)
    
    if servers:
        print(f"\nFound {len(servers)} server(s):")
        for i, server in enumerate(servers, 1):
            print(f"{i}. {server}")
    else:
        print("\nNo servers found.")
