# Audio Credits

This file lists the attribution and licensing information for all audio assets used in CA-Racing.

| Asset Category | Filenames | Author | License | Source Link |
| :--- | :--- | :--- | :--- | :--- |
| **UI SFX** | `<PERSON><PERSON><PERSON><PERSON> - Ultimate UI SFX Pack - *.ogg` | JDSherbert | CC0 1.0 Universal | [itch.io](https://jdsherbert.itch.io/ultimate-ui-sfx-pack) |
| **Car Engine** | `engine_low.wav`, `engine_medium.wav`, `engine_high.wav` | CA-Racing developer Piotrek2713 | CC0 1.0 Universal (Public Domain) | Synthetically Generated |
| **Tire SFX** | `skid.wav` | Kenney | CC0 1.0 Universal | [Racing Pack](https://kenney.nl/assets/racing-pack) |
| **Crash SFX** | `crash.wav` | Kenney | CC0 1.0 Universal | [Impact Sounds](https://kenney.nl/assets/impact-sounds) |
| **Misc SFX** | `Grinding (Loop).wav` | <PERSON>ney | CC0 1.0 Universal | [Impact Sounds](https://kenney.nl/assets/impact-sounds) |
| **Music** | `race_music_2.ogg` (Solve The Puzzle) | Patrick de Arteaga | CC-BY 4.0 | [Patrick de Arteaga](https://patrickdearteaga.com) |

## Engine Sound Generation

The engine sound files are **synthetically generated** using digital signal processing by **Piotrek2713**:

### Generation Details (2026-01-12)
- **Created by:** Piotrek2713 (CA-Racing developer)
- **Method:** Harmonic synthesis with pink noise filtering
- **Generator:** [`generate_engine_sounds.py`](../../generate_engine_sounds.py)
- **Format:** WAV, Mono, 44100 Hz, 16-bit PCM
- **Duration:** 2.0 seconds per file (optimized for seamless looping)
- **RPM Configuration:**
  - `engine_low.wav`: 1500 RPM (idle/cruising) - 4-cylinder, 4-stroke
  - `engine_medium.wav`: 3500 RPM (acceleration) - 4-cylinder, 4-stroke
  - `engine_high.wav`: 6500 RPM (racing) - 4-cylinder, 4-stroke

### Technical Features
- **Harmonic content:** 7 harmonics with realistic amplitude distribution
- **Phase modulation:** Slight modulation for organic sound
- **Pink noise:** Ultra-low filtered mechanical noise (0.002-0.010 level)
- **Tremolo effect:** Dynamic amplitude modulation (8-14 Hz based on RPM)
- **Crossfade:** 50ms fade in/out for seamless looping
- **Headroom:** 20% (80% of max amplitude to prevent clipping)

### Why Synthetic?
The synthetic approach provides optimal sound quality for racing games:
- ✅ **Zero background noise** - Clean signal generation
- ✅ **Perfect looping** - Designed for seamless playback
- ✅ **Customizable** - Easy to adjust RPM, harmonics, or characteristics
- ✅ **Consistent quality** - No recording artifacts
- ✅ **Public domain** - No licensing concerns
