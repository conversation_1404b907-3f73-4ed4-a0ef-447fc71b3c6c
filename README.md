<<<<<<< HEAD
# CA-Racing

**CA-Racing** is a high-performance, dynamic 2D top-down racing game developed in Python using Pygame. It features a custom physics engine, AI-driven opponents, **complete multiplayer system**, cellular automata-based vehicle evolution system (garage), and a robust track system, designed to deliver an engaging arcade racing experience with roguelite elements.

## 🚀 Key Features

*   **Multiplayer Racing** *(NEW in v1.0.0-demo)*: Online racing with client-side prediction and server reconciliation
    *   Zero perceived input lag with client-side prediction
    *   Authoritative server physics with automatic error correction
    *   Room-lobby system supporting 10 concurrent players (5 rooms × 2 players)
    *   60Hz server tick rate for responsive gameplay
    *   Docker support for easy server deployment
*   **Custom Physics Engine**: Realistic car handling with drift mechanics, acceleration curves, collision response, and power-to-weight ratio calculations
*   **AI Opponents**: Competitive AI drivers that adapt to track layout using pathfinding and racing lines
*   **Cellular Automata Garage System**: Unique vehicle evolution system where cars mutate and evolve based on CA rules
*   **Dynamic Track System**: Tile-based map generation using Tiled Map Editor supporting complex track designs
*   **Professional Audio System**: Multi-layered engine sounds with dynamic mixing, UI feedback sounds, and background music
*   **Localization**: Full support for multiple languages (English, Polish, German, French, Spanish, Portuguese)
*   **Cross-Platform**: Runs natively on Linux and Windows
*   **Moddable**: Data-driven architecture allowing for easy modification of game assets and parameters

## 📂 Project Structure

The codebase is organized to ensure modularity and maintainability:

*   [`src/`](src/): Core source code
    *   [`core/`](src/core/): Engine subsystems
        *   [`app.py`](src/core/app.py): Main application loop and state management
        *   [`assets.py`](src/core/assets.py): Asset loading and caching system
        *   [`audio.py`](src/core/audio.py): Audio management and playback
        *   [`data_manager.py`](src/core/data_manager.py): Save/load game data
        *   [`game_data.py`](src/core/game_data.py): Vehicle and parts data management
        *   [`locale.py`](src/core/locale.py): Internationalization and translations
        *   [`path.py`](src/core/path.py): Path resolution for assets and resources
        *   [`tmx_loader.py`](src/core/tmx_loader.py): Tiled map parser
    *   [`game/`](src/game/): Game logic
        *   [`car.py`](src/game/car.py): Vehicle physics and behavior
        *   [`car_audio.py`](src/game/car_audio.py): Per-vehicle audio controller
        *   [`player.py`](src/game/player.py): Player input handling
        *   [`ai_driver.py`](src/game/ai_driver.py): AI opponent behavior
        *   [`race.py`](src/game/race.py): Race logic and lap tracking
        *   [`session.py`](src/game/session.py): Race session management
        *   [`multiplayer_session.py`](src/game/multiplayer_session.py): Multiplayer game session management
        *   [`physics_sync.py`](src/game/physics_sync.py): State buffering and interpolation for multiplayer
        *   [`track_manager.py`](src/game/track_manager.py): Track loading and rendering
        *   [`camera.py`](src/game/camera.py): Camera system with smooth following
        *   [`particles.py`](src/game/particles.py): Particle effects system
        *   [`garage/`](src/game/garage/): Cellular Automata garage system
            *   [`vehicle_grid.py`](src/game/garage/vehicle_grid.py): CA grid management
            *   [`vehicle_cell.py`](src/game/garage/vehicle_cell.py): Individual cell logic
            *   [`mutation_engine.py`](src/game/garage/mutation_engine.py): Vehicle mutation rules
    *   [`network/`](src/network/): Multiplayer networking
        *   [`client_handler.py`](src/network/client_handler.py): Client networking with prediction/reconciliation
        *   [`protocol.py`](src/network/protocol.py): Network protocol definitions
        *   [`__init__.py`](src/network/__init__.py): Network module interface (v2.0.0)
    *   [`physics/`](src/physics/): Physics engine
        *   [`engine_physics.py`](src/physics/engine_physics.py): Realistic engine physics calculations
        *   [`pixel_collision.py`](src/physics/pixel_collision.py): Pixel-perfect collision detection
    *   [`logic/`](src/logic/): Game logic layer
        *   [`race_manager.py`](src/logic/race_manager.py): High-level race orchestration
    *   [`ui/`](src/ui/): User Interface
        *   [`menu_main.py`](src/ui/menu_main.py): Main menu
        *   [`menu_garage.py`](src/ui/menu_garage.py): Garage/evolution interface
        *   [`menu_race_select.py`](src/ui/menu_race_select.py): Race selection screen
        *   [`menu_settings.py`](src/ui/menu_settings.py): Settings and configuration
        *   [`menu_shop.py`](src/ui/menu_shop.py): Parts shop interface
        *   [`widgets.py`](src/ui/widgets.py): Reusable UI components
        *   [`effects.py`](src/ui/effects.py): UI visual effects
    *   [`tools/`](src/tools/): Development tools and utilities
*   [`assets/`](assets/): Game resources
    *   [`images/`](assets/images/): Sprites, tilesets, and graphics
    *   [`sounds/`](assets/sounds/): Audio files (music and SFX)
*   [`data/`](data/): Configuration files
    *   [`game_data.json`](data/game_data.json): Vehicles and parts definitions
    *   [`settings.json`](data/settings.json): User settings
    *   [`lang/`](data/lang/): Translation files (JSON)
*   [`docs_ai/`](docs_ai/): AI agent documentation and knowledge base
*   [`packaging/`](packaging/): Distribution packaging scripts
    *   [`aur/`](packaging/aur/): Arch Linux AUR package
    *   [`deb/`](packaging/deb/): Debian/Ubuntu package configuration
    *   [`rpm/`](packaging/rpm/): Fedora/RHEL package configuration
    *   [`windows/`](packaging/windows/): Windows installer configuration
    *   [`scripts/`](packaging/scripts/): Build automation scripts
*   [`maps/`](maps/): Tiled map source files (`.tmx` and `.tsx`)

## 🛠️ Quick Start

### Prerequisites

*   Python 3.10 or higher
*   `pip` (Python Package Installer)

### Running from Source

1.  **Clone the repository:**
    ```bash
    git clone https://github.com/piotrek1372/ca-racing.git
    cd ca-racing
    ```

2.  **Install dependencies:**
    ```bash
    pip install -r requirements.txt
    ```

3.  **Launch the game:**
    ```bash
    python main.py
    ```

## 💿 Installation

For detailed installation instructions, including native packages for Linux (DEB, RPM, AUR) and the Windows installer, please refer to the [Installation Guide](INSTALL.md).

## 🏗️ Building & Packaging

The project includes a comprehensive packaging system for various platforms.

### Creating a Local Build

To create a standalone executable for your current platform:

```bash
pip install pyinstaller
pyinstaller ca-racing.spec
```
The executable will be generated in the `dist/` directory.

### Automated Releases

This project utilizes GitHub Actions for Continuous Delivery. Pushing a tagged commit (e.g., `v1.0.0`) triggers the automated build pipeline, which:
1.  Compiles standalone executables for Windows and Linux
2.  Generates native packages (`.deb`, `.rpm`) for Linux distributions
3.  Publishes a GitHub Release with all artifacts

For detailed release procedures, see [`RELEASE_PROCEDURE.md`](RELEASE_PROCEDURE.md).

## 🎮 Gameplay

*   **Race Mode**: Compete against AI opponents on various tracks
*   **Multiplayer Mode** *(NEW)*: Race against real players online - see [Multiplayer Quick Start](MULTIPLAYER_QUICKSTART.md)
*   **Garage Mode**: Use Cellular Automata rules to evolve and mutate your vehicles
*   **Shop**: Purchase parts and upgrades for your cars
*   **Settings**: Customize controls, audio, video, and language preferences

## 🌐 Multiplayer (v1.0.0-demo)

CA-Racing now features a complete multiplayer system with professional-grade networking:

**Quick Start:**
```bash
# Terminal 1: Start server
python server.py

# Terminal 2: Connect client
python test_client.py
```

**Key Features:**
- Client-side prediction for responsive controls
- Server reconciliation for fair gameplay
- State interpolation for smooth remote player rendering
- Room-lobby system (5 rooms, 2 players each)
- Docker support for easy deployment

**Documentation:**
- [**MULTIPLAYER_QUICKSTART.md**](MULTIPLAYER_QUICKSTART.md) - Get started in 5 minutes
- [**MULTIPLAYER_GUIDE.md**](MULTIPLAYER_GUIDE.md) - Complete technical reference
- [**README_MULTIPLAYER.md**](README_MULTIPLAYER.md) - System overview
- [**MULTIPLAYER_DEPLOYMENT_GUIDE.md**](MULTIPLAYER_DEPLOYMENT_GUIDE.md) - Production deployment

**Server Deployment:**
```bash
# Docker deployment
docker-compose up -d

# Or manual deployment
./start_server.sh
```

For detailed integration and setup instructions, see the [Multiplayer Quick Start Guide](MULTIPLAYER_QUICKSTART.md).

## 🔧 Development

### Project Standards

*   **Coding Style**: PEP 8 compliant, enforced via linters
*   **Type Hints**: Full type annotation coverage with mypy static analysis
*   **Documentation**: Comprehensive docstrings and AI agent knowledge base in [`docs_ai/`](docs_ai/)
*   **Variable Naming**: See [`VARIABLE_STANDARDS.md`](VARIABLE_STANDARDS.md) for conventions

### Key Documentation

**Core Systems:**
*   [`KNOWLEDGE_BASE.md`](KNOWLEDGE_BASE.md): Architecture and coding standards
*   [`GARAGE_SYSTEM_DESIGN.md`](GARAGE_SYSTEM_DESIGN.md): Cellular Automata garage system design
*   [`PHYSICS_BALANCE_GUIDE.md`](PHYSICS_BALANCE_GUIDE.md): Physics tuning guidelines
*   [`VARIABLE_STANDARDS.md`](VARIABLE_STANDARDS.md): Variable naming conventions
*   [`AI_INSTRUCTIONS.md`](AI_INSTRUCTIONS.md): Instructions for AI code assistants

**Multiplayer System:**
*   [`MULTIPLAYER_QUICKSTART.md`](MULTIPLAYER_QUICKSTART.md): 5-minute setup guide
*   [`MULTIPLAYER_GUIDE.md`](MULTIPLAYER_GUIDE.md): Complete technical reference (870+ lines)
*   [`README_MULTIPLAYER.md`](README_MULTIPLAYER.md): System overview and architecture
*   [`NETWORKING_ARCHITECTURE.md`](NETWORKING_ARCHITECTURE.md): Network architecture details
*   [`MULTIPLAYER_DEPLOYMENT_GUIDE.md`](MULTIPLAYER_DEPLOYMENT_GUIDE.md): Production deployment
*   [`NETWORK_QUICKSTART.md`](NETWORK_QUICKSTART.md): Quick network setup
*   [`NETWORK_README.md`](NETWORK_README.md): Network protocol specification

**Release Information:**
*   [`CHANGELOG.md`](CHANGELOG.md): Detailed version history
*   [`RELEASE_NOTES.md`](RELEASE_NOTES.md): User-facing release notes
*   [`VERSION_INFO.md`](VERSION_INFO.md): Versioning information and strategy

## 🌍 Contributing

Contributions are welcome! Please ensure your code:
*   Follows PEP 8 style guidelines
*   Includes type hints
*   Passes all tests and static analysis checks
*   Is documented with clear docstrings

## 📝 Changelog

See [`CHANGELOG.md`](CHANGELOG.md) for version history and release notes.

## 📄 License

This project is licensed under the MIT License - see the [`LICENSE`](LICENSE) file for details.

## 🙏 Credits

*   **Graphics**: Kenney Racing Pack
*   **Audio**: See [`assets/sounds/CREDITS.md`](assets/sounds/CREDITS.md) for detailed audio credits
*   **Framework**: Built with [Pygame](https://www.pygame.org/)

## 🔗 Links

*   **GitHub Repository**: [https://github.com/piotrek1372/ca-racing](https://github.com/piotrek1372/ca-racing)
*   **Issue Tracker**: [https://github.com/piotrek1372/ca-racing/issues](https://github.com/piotrek1372/ca-racing/issues)
*   **Releases**: [https://github.com/piotrek1372/ca-racing/releases](https://github.com/piotrek1372/ca-racing/releases)
=======
# CA-Racing
>>>>>>> 67bb197 (Initial commit)
