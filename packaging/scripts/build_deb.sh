#!/bin/bash
set -e

# Version configuration
VERSION="${1:-0.1.0-alpha}"
ARCH="amd64"

# Paths
SCRIPT_DIR="$( cd "$( dirname "${BASH_SOURCE[0]}" )" && pwd )"
PROJECT_ROOT="$( cd "$SCRIPT_DIR/../.." && pwd )"
DIST_DIR="$PROJECT_ROOT/dist"
DEB_BUILD_DIR="$PROJECT_ROOT/build/deb"
ARTIFACTS_DIR="$PROJECT_ROOT/artifacts"

echo "🐧 Building DEB package for version $VERSION..."

# Ensure artifacts directory exists
mkdir -p "$ARTIFACTS_DIR"

# Pre-requisite: Ensure the game is built
if [ ! -f "$DIST_DIR/CA-Racing" ]; then
    echo "❌ Critical Error: Build artifact not found at $DIST_DIR/CA-Racing"
    exit 1
fi

# Clean up previous build directories
rm -rf "$DEB_BUILD_DIR"
mkdir -p "$DEB_BUILD_DIR/opt/ca-racing"
mkdir -p "$DEB_BUILD_DIR/usr/share/applications"
mkdir -p "$DEB_BUILD_DIR/usr/share/icons/hicolor/1024x1024/apps"
mkdir -p "$DEB_BUILD_DIR/DEBIAN"

# Copy binary file
cp "$DIST_DIR/CA-Racing" "$DEB_BUILD_DIR/opt/ca-racing/ca-racing"
chmod +x "$DEB_BUILD_DIR/opt/ca-racing/ca-racing"

# Copy integration files
cp "$PROJECT_ROOT/packaging/deb/DEBIAN/control" "$DEB_BUILD_DIR/DEBIAN/control"
cp "$PROJECT_ROOT/packaging/deb/DEBIAN/postinst" "$DEB_BUILD_DIR/DEBIAN/postinst" 2>/dev/null || true
chmod 755 "$DEB_BUILD_DIR/DEBIAN/post"* 2>/dev/null || true

# Update Version in control file
sed -i "s@Version: .*@Version: $VERSION@" "$DEB_BUILD_DIR/DEBIAN/control"

# Create Desktop file
cat > "$DEB_BUILD_DIR/usr/share/applications/ca-racing.desktop" << EOF
[Desktop Entry]
Type=Application
Name=CA-Racing
GenericName=Racing Game
Comment=A retro-style 2D top-down racing game
Exec=/opt/ca-racing/ca-racing
Icon=ca-racing
Categories=Game;ArcadeGame;
Terminal=false
StartupNotify=true
Path=/opt/ca-racing
EOF

# Copy Icon
cp "$PROJECT_ROOT/assets/images/icon.png" "$DEB_BUILD_DIR/usr/share/icons/hicolor/1024x1024/apps/ca-racing.png"

# Build DEB
dpkg-deb --build "$DEB_BUILD_DIR" "$ARTIFACTS_DIR/ca-racing_${VERSION}_${ARCH}.deb"
echo "✅ DEB created at $ARTIFACTS_DIR/ca-racing_${VERSION}_${ARCH}.deb"