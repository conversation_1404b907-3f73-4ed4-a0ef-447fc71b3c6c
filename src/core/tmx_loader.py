"""
TMX Map Loader Module
=====================
Parses Tiled TMX map files for the racing game.
Extracts tile layers, collision objects, and tileset data.
"""
import os
import xml.etree.ElementTree as ET
import pygame as pg


class Tileset:
    """Represents a tileset with image and tile data."""
    
    def __init__(self, firstgid, name, tile_width, tile_height, image_path, columns):
        self.firstgid = firstgid
        self.name = name
        self.tile_width = tile_width
        self.tile_height = tile_height
        self.image_path = image_path
        self.columns = columns
        self.image = None
        self.tiles = {}  # gid -> surface
    
    def load_image(self, base_path):
        """Load the tileset image and split into individual tiles."""
        full_path = os.path.join(base_path, self.image_path)
        if not os.path.exists(full_path):
            print(f"[TMX] Warning: Tileset image not found: {full_path}")
            return False
        
        self.image = pg.image.load(full_path).convert_alpha()
        
        # Calculate number of rows
        img_height = self.image.get_height()
        rows = img_height // self.tile_height
        
        # Extract individual tiles
        gid = self.firstgid
        for row in range(rows):
            for col in range(self.columns):
                x = col * self.tile_width
                y = row * self.tile_height
                rect = pg.Rect(x, y, self.tile_width, self.tile_height)
                tile_surface = self.image.subsurface(rect).copy()
                self.tiles[gid] = tile_surface
                gid += 1
        
        print(f"[TMX] Loaded tileset '{self.name}' with {len(self.tiles)} tiles")
        return True
    
    def get_tile(self, gid):
        """Get a tile surface by global ID."""
        return self.tiles.get(gid)


class TileLayer:
    """Represents a tile layer with CSV data."""
    
    def __init__(self, name, width, height, data):
        self.name = name
        self.width = width
        self.height = height
        self.data = data  # 2D list of tile GIDs
    
    def get_tile_at(self, x, y):
        """Get tile GID at grid position."""
        if 0 <= x < self.width and 0 <= y < self.height:
            return self.data[y][x]
        return 0


class CollisionObject:
    """Represents a collision object (polygon or rectangle)."""
    
    def __init__(self, obj_id, x, y, obj_type='rect', width=0, height=0, points=None):
        self.id = obj_id
        self.x = x
        self.y = y
        self.type = obj_type  # 'rect', 'polygon', 'point'
        self.width = width
        self.height = height
        self.points = points or []  # List of (x, y) tuples relative to object position
    
    def get_world_points(self):
        """Get polygon points in world coordinates."""
        if self.type == 'polygon':
            return [(self.x + px, self.y + py) for px, py in self.points]
        elif self.type == 'rect':
            # Rectangle as 4 corner points
            return [
                (self.x, self.y),
                (self.x + self.width, self.y),
                (self.x + self.width, self.y + self.height),
                (self.x, self.y + self.height)
            ]
        return []


class TMXMap:
    """
    Represents a complete TMX map with layers, tilesets, and collision data.
    """
    
    def __init__(self):
        self.width = 0  # Map width in tiles
        self.height = 0  # Map height in tiles
        self.tile_width = 64
        self.tile_height = 64
        self.pixel_width = 0  # Map width in pixels
        self.pixel_height = 0  # Map height in pixels
        
        self.tilesets = []  # List of Tileset objects
        self.tile_layers = []  # List of TileLayer objects
        self.collision_objects = []  # List of CollisionObject
        
        self.rendered_surface = None  # Pre-rendered map surface
    
    @property
    def world_width(self):
        return self.pixel_width
    
    @property
    def world_height(self):
        return self.pixel_height
    
    def get_tileset_for_gid(self, gid):
        """Find the tileset that contains a given global tile ID."""
        if gid == 0:
            return None
        for tileset in reversed(self.tilesets):
            if gid >= tileset.firstgid:
                return tileset
        return None
    
    def get_tile_surface(self, gid):
        """Get the surface for a tile by its GID."""
        tileset = self.get_tileset_for_gid(gid)
        if tileset:
            return tileset.get_tile(gid)
        return None

    def render_to_surface(self):
        """Pre-render all tile layers to a single surface."""
        self.rendered_surface = pg.Surface(
            (self.pixel_width, self.pixel_height),
            pg.SRCALPHA
        )
        self.rendered_surface.fill((40, 40, 45))  # Background color

        for layer in self.tile_layers:
            for y in range(layer.height):
                for x in range(layer.width):
                    gid = layer.get_tile_at(x, y)
                    if gid > 0:
                        tile_surf = self.get_tile_surface(gid)
                        if tile_surf:
                            px = x * self.tile_width
                            py = y * self.tile_height
                            self.rendered_surface.blit(tile_surf, (px, py))

        print(f"[TMX] Rendered map surface: {self.pixel_width}x{self.pixel_height}")
        return self.rendered_surface

    def point_in_collision(self, x, y):
        """Check if a point collides with any collision object."""
        for obj in self.collision_objects:
            if obj.type == 'point':
                continue

            points = obj.get_world_points()
            if len(points) < 3:
                continue

            if self._point_in_polygon(x, y, points):
                return True
        return False

    def _point_in_polygon(self, x, y, polygon):
        """Ray casting algorithm for point-in-polygon test."""
        n = len(polygon)
        inside = False

        j = n - 1
        for i in range(n):
            xi, yi = polygon[i]
            xj, yj = polygon[j]

            if ((yi > y) != (yj > y)) and (x < (xj - xi) * (y - yi) / (yj - yi) + xi):
                inside = not inside
            j = i

        return inside

    def get_collision_edges(self):
        """Get all collision polygon edges for line-segment collision checks."""
        edges = []
        for obj in self.collision_objects:
            points = obj.get_world_points()
            if len(points) < 2:
                continue

            for i in range(len(points)):
                p1 = points[i]
                p2 = points[(i + 1) % len(points)]
                edges.append((p1, p2))

        return edges


def load_tmx(filepath):
    """
    Load a TMX file and return a TMXMap object.

    Args:
        filepath: Path to the .tmx file

    Returns:
        TMXMap object or None if loading failed
    """
    if not os.path.exists(filepath):
        print(f"[TMX] Error: File not found: {filepath}")
        return None

    base_path = os.path.dirname(filepath)

    try:
        tree = ET.parse(filepath)
        root = tree.getroot()
    except ET.ParseError as e:
        print(f"[TMX] Error parsing XML: {e}")
        return None

    tmx_map = TMXMap()

    # Parse map attributes
    tmx_map.width = int(root.get('width', 0))
    tmx_map.height = int(root.get('height', 0))
    tmx_map.tile_width = int(root.get('tilewidth', 64))
    tmx_map.tile_height = int(root.get('tileheight', 64))
    tmx_map.pixel_width = tmx_map.width * tmx_map.tile_width
    tmx_map.pixel_height = tmx_map.height * tmx_map.tile_height

    print(f"[TMX] Loading map: {tmx_map.width}x{tmx_map.height} tiles, "
          f"{tmx_map.pixel_width}x{tmx_map.pixel_height} pixels")

    # Parse tilesets
    for tileset_elem in root.findall('tileset'):
        _parse_tileset(tileset_elem, base_path, tmx_map)

    # Parse layers
    for layer_elem in root.findall('layer'):
        _parse_tile_layer(layer_elem, tmx_map)

    # Parse object groups (collision layers)
    for objgroup_elem in root.findall('objectgroup'):
        _parse_object_group(objgroup_elem, tmx_map)

    # Pre-render the map
    tmx_map.render_to_surface()

    return tmx_map


def _parse_tileset(elem, base_path, tmx_map):
    """Parse a tileset element (either embedded or external)."""
    firstgid = int(elem.get('firstgid', 1))
    source = elem.get('source')

    if source:
        # External tileset (.tsx file)
        tsx_path = os.path.join(base_path, source)
        if not os.path.exists(tsx_path):
            print(f"[TMX] Warning: External tileset not found: {tsx_path}")
            return

        tsx_base = os.path.dirname(tsx_path)
        try:
            tsx_tree = ET.parse(tsx_path)
            tsx_root = tsx_tree.getroot()
        except ET.ParseError as e:
            print(f"[TMX] Error parsing tileset: {e}")
            return

        name = tsx_root.get('name', 'unnamed')
        tile_width = int(tsx_root.get('tilewidth', 64))
        tile_height = int(tsx_root.get('tileheight', 64))
        columns = int(tsx_root.get('columns', 8))

        # Get image source
        image_elem = tsx_root.find('image')
        if image_elem is not None:
            image_source = image_elem.get('source', '')
        else:
            print(f"[TMX] Warning: No image in tileset {name}")
            return
    else:
        # Embedded tileset
        name = elem.get('name', 'unnamed')
        tile_width = int(elem.get('tilewidth', 64))
        tile_height = int(elem.get('tileheight', 64))
        columns = int(elem.get('columns', 8))
        tsx_base = base_path

        image_elem = elem.find('image')
        if image_elem is not None:
            image_source = image_elem.get('source', '')
        else:
            return

    tileset = Tileset(firstgid, name, tile_width, tile_height, image_source, columns)
    tileset.load_image(tsx_base)
    tmx_map.tilesets.append(tileset)


def _parse_tile_layer(elem, tmx_map):
    """Parse a tile layer element."""
    name = elem.get('name', 'unnamed')
    width = int(elem.get('width', tmx_map.width))
    height = int(elem.get('height', tmx_map.height))

    data_elem = elem.find('data')
    if data_elem is None:
        return

    encoding = data_elem.get('encoding', 'csv')
    if encoding != 'csv':
        print(f"[TMX] Warning: Unsupported encoding '{encoding}', only CSV supported")
        return

    # Parse CSV data
    csv_text = data_elem.text.strip()
    rows = []
    for line in csv_text.split('\n'):
        line = line.strip().rstrip(',')
        if line:
            row = [int(x) for x in line.split(',') if x.strip()]
            rows.append(row)

    layer = TileLayer(name, width, height, rows)
    tmx_map.tile_layers.append(layer)
    print(f"[TMX] Loaded tile layer '{name}': {width}x{height}")


def _parse_object_group(elem, tmx_map):
    """Parse an object group (collision layer)."""
    group_name = elem.get('name', 'unnamed')

    for obj_elem in elem.findall('object'):
        obj_id = int(obj_elem.get('id', 0))
        x = float(obj_elem.get('x', 0))
        y = float(obj_elem.get('y', 0))
        width = float(obj_elem.get('width', 0))
        height = float(obj_elem.get('height', 0))

        # Check for polygon
        polygon_elem = obj_elem.find('polygon')
        if polygon_elem is not None:
            points_str = polygon_elem.get('points', '')
            points = _parse_points(points_str)
            obj = CollisionObject(obj_id, x, y, 'polygon', points=points)
            tmx_map.collision_objects.append(obj)
            continue

        # Check for polyline
        polyline_elem = obj_elem.find('polyline')
        if polyline_elem is not None:
            points_str = polyline_elem.get('points', '')
            points = _parse_points(points_str)
            obj = CollisionObject(obj_id, x, y, 'polygon', points=points)
            tmx_map.collision_objects.append(obj)
            continue

        # Rectangle or point
        if width > 0 and height > 0:
            obj = CollisionObject(obj_id, x, y, 'rect', width, height)
            tmx_map.collision_objects.append(obj)
        else:
            # Point object
            obj = CollisionObject(obj_id, x, y, 'point')
            tmx_map.collision_objects.append(obj)

    print(f"[TMX] Loaded object group '{group_name}': {len(tmx_map.collision_objects)} objects")


def _parse_points(points_str):
    """Parse a points string like '0,0 10,20 30,40' into list of tuples."""
    points = []
    for pair in points_str.split():
        parts = pair.split(',')
        if len(parts) == 2:
            px = float(parts[0])
            py = float(parts[1])
            points.append((px, py))
    return points

