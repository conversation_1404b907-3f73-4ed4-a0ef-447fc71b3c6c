# ======================================
# CA-Racing Multiplayer Server - Dockerfile
# ======================================
# Multi-stage build for minimal production image
#
# Features:
# - Python 3.11 slim base
# - Non-root user (gameserver)
# - Minimal attack surface
# - Optimized for Google Cloud deployment

# ======================================
# Stage 1: Builder
# ======================================
FROM python:3.11-slim AS builder

# Set working directory
WORKDIR /build

# Install build dependencies
RUN apt-get update && apt-get install -y --no-install-recommends \
    gcc \
    python3-dev \
    && rm -rf /var/lib/apt/lists/*

# Copy requirements first (for better layer caching)
COPY requirements.txt .

# Create virtual environment and install dependencies
RUN python -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"
RUN pip install --no-cache-dir --upgrade pip setuptools wheel && \
    pip install --no-cache-dir -r requirements.txt

# ======================================
# Stage 2: Production Runtime
# ======================================
FROM python:3.11-slim

# Metadata
LABEL maintainer="CA-Racing Team"
LABEL description="CA-Racing Multiplayer Game Server"
LABEL version="1.0.0"

# Set environment variables
ENV PYTHONUNBUFFERED=1 \
    PYTHONDONTWRITEBYTECODE=1 \
    PATH="/opt/venv/bin:$PATH" \
    SERVER_HOST=0.0.0.0 \
    SERVER_PORT=5555

# Create non-root user for security
RUN groupadd -r gameserver && \
    useradd -r -g gameserver -s /bin/bash -d /home/<USER>
    mkdir -p /home/<USER>/app && \
    chown -R gameserver:gameserver /home/<USER>/app

# Copy virtual environment from builder
COPY --from=builder --chown=gameserver:gameserver /opt/venv /opt/venv

# Set working directory
WORKDIR /app

# Copy application files
COPY --chown=gameserver:gameserver server.py .
COPY --chown=gameserver:gameserver src/ ./src/

# Switch to non-root user
USER gameserver

# Expose server port
EXPOSE 5555

# Health check (check if server responds on port 5555)
HEALTHCHECK --interval=30s --timeout=10s --start-period=5s --retries=3 \
    CMD python -c "import socket; s = socket.socket(); s.settimeout(5); s.connect(('127.0.0.1', 5555)); s.close()" || exit 1

# Start server
CMD ["python", "server.py"]
