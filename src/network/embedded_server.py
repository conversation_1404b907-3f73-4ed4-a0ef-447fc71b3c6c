"""
Embedded Server Manager
======================
Manages a local game server instance that runs in a separate thread/process
for localhost connections.
"""

import asyncio
import threading
import logging
import time
import subprocess
import sys
import os
from typing import Optional

logger = logging.getLogger(__name__)


class EmbeddedServer:
    """Manages an embedded local server for localhost connections"""
    
    def __init__(self, port: int = 8444):
        self.port = port
        self.server_process: Optional[subprocess.Popen] = None
        self.server_thread: Optional[threading.Thread] = None
        self.loop: Optional[asyncio.AbstractEventLoop] = None
        self.server_task: Optional[asyncio.Task] = None
        self.is_running = False
        
    def start(self) -> bool:
        """Start the embedded server"""
        if self.is_running:
            logger.info("Embedded server is already running")
            return True
            
        try:
            # Get the path to server.py
            current_dir = os.path.dirname(os.path.dirname(os.path.dirname(__file__)))
            server_path = os.path.join(current_dir, 'server.py')
            
            if not os.path.exists(server_path):
                logger.error(f"Server script not found at {server_path}")
                return False
            
            # Start server as subprocess
            logger.info(f"Starting embedded server on port {self.port}")
            
            # Use the same Python executable that's running the main app
            python_exe = sys.executable
            
            self.server_process = subprocess.Popen(
                [python_exe, server_path],
                cwd=current_dir,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True
            )
            
            # Give the server a moment to start
            time.sleep(2)
            
            # Check if the process is still running
            if self.server_process.poll() is None:
                self.is_running = True
                logger.info(f"✅ Embedded server started successfully (PID: {self.server_process.pid})")
                return True
            else:
                # Process died, get error output
                stdout, stderr = self.server_process.communicate()
                logger.error(f"❌ Embedded server failed to start:")
                logger.error(f"STDOUT: {stdout}")
                logger.error(f"STDERR: {stderr}")
                return False
                
        except Exception as e:
            logger.error(f"Failed to start embedded server: {e}")
            return False
    
    def stop(self):
        """Stop the embedded server"""
        if not self.is_running:
            return
            
        logger.info("Stopping embedded server...")
        
        try:
            if self.server_process:
                self.server_process.terminate()
                
                # Wait for graceful shutdown
                try:
                    self.server_process.wait(timeout=5)
                except subprocess.TimeoutExpired:
                    # Force kill if it doesn't stop gracefully
                    logger.warning("Server didn't stop gracefully, force killing...")
                    self.server_process.kill()
                    self.server_process.wait()
                
                self.server_process = None
                
        except Exception as e:
            logger.error(f"Error stopping embedded server: {e}")
        
        self.is_running = False
        logger.info("Embedded server stopped")
    
    def is_server_running(self) -> bool:
        """Check if the server is still running"""
        if not self.is_running or not self.server_process:
            return False
            
        # Check if process is still alive
        if self.server_process.poll() is not None:
            # Process has died
            self.is_running = False
            return False
            
        return True
    
    def __del__(self):
        """Cleanup when object is destroyed"""
        self.stop()


# Global instance for the embedded server
_embedded_server: Optional[EmbeddedServer] = None


def get_embedded_server() -> EmbeddedServer:
    """Get the global embedded server instance"""
    global _embedded_server
    if _embedded_server is None:
        _embedded_server = EmbeddedServer()
    return _embedded_server


def start_localhost_server() -> bool:
    """Start the localhost server if not already running"""
    server = get_embedded_server()
    return server.start()


def stop_localhost_server():
    """Stop the localhost server"""
    server = get_embedded_server()
    server.stop()


def is_localhost_server_running() -> bool:
    """Check if localhost server is running"""
    server = get_embedded_server()
    return server.is_server_running()
