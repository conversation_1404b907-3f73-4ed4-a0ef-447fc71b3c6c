def generate_geometry():
    """Generates spine points and wall points using analytical normals."""
    spine_points = []
    normals = []
    
    # O<PERSON><PERSON><PERSON><PERSON> ile segmentów mamy przetworz<PERSON>
    total_segments = len(track_sequence) - 1

    for i in range(total_segments):
        current_segment = track_sequence[i]
        
        start_pos = get_tile_center(current_segment['grid'])
        action = current_segment['action']
        direction = pygame.math.Vector2(current_segment['dir'])

        # --- LOGIKA GENEROWANIA PUNKTÓW ---
        # Zmieniamy zakres pętli lub warunek dodawania
        # Używamy tej samej logiki obliczania geometrii, ale filtrujemy zapis
        
        segment_spine = []
        segment_normals = []

        if 'straight' in action or 'start' in action:
            # --- STRAIGHT SEGMENT ---
            end_pos = get_tile_center(track_sequence[i+1]['grid'])
            
            if direction.x == 0: 
                normal = pygame.math.Vector2(1, 0) if direction.y < 0 else pygame.math.Vector2(-1, 0)
            else: 
                normal = pygame.math.Vector2(0, 1) if direction.x > 0 else pygame.math.Vector2(0, -1)

            for j in range(POINTS_PER_TILE):
                t = j / (POINTS_PER_TILE - 1)
                point = start_pos.lerp(end_pos, t)
                segment_spine.append(point)
                segment_normals.append(normal)

        elif 'turn' in action:
            # --- TURN SEGMENT ---
            prev_segment = track_sequence[i-1]
            prev_dir = pygame.math.Vector2(prev_segment['dir'])
            
            pivot = start_pos + (direction - prev_dir) * (TILE_WIDTH / 2)
            start_angle = math.atan2((start_pos - pivot).y, (start_pos - pivot).x)
            
            end_pos = get_tile_center(track_sequence[i+1]['grid'])
            end_angle = math.atan2((end_pos - pivot).y, (end_pos - pivot).x)

            if abs(end_angle - start_angle) > math.pi:
                if end_angle > start_angle: start_angle += 2 * math.pi
                else: end_angle += 2 * math.pi

            radius = (start_pos - pivot).length()
            
            for j in range(POINTS_PER_TILE):
                t = j / (POINTS_PER_TILE - 1)
                angle = start_angle + (end_angle - start_angle) * t
                point = pivot + pygame.math.Vector2(math.cos(angle) * radius, math.sin(angle) * radius)
                normal = (point - pivot).normalize()
                
                segment_spine.append(point)
                segment_normals.append(normal)

        # --- KLUCZOWA POPRAWKA: USUWANIE DUPLIKATÓW ---
        # Dodajemy wszystkie punkty OPRÓCZ ostatniego, chyba że to ostatni segment trasy (niezamkniętej)
        # Ponieważ trasa jest pętlą (Closed Loop), ostatni punkt ostatniego segmentu 
        # powinien pokrywać się z pierwszym punktem pierwszego segmentu.
        
        # Dla bezpieczeństwa w pętli: Zawsze odcinamy ostatni punkt każdego segmentu.
        # "Zszycie" nastąpi samoistnie między segmentami.
        
        points_to_add = POINTS_PER_TILE - 1
        
        # Dodaj do głównej listy (slicing [:-1] ucina ostatni element)
        spine_points.extend(segment_spine[:-1])
        normals.extend(segment_normals[:-1])

    # --- Smooth the normals ---
    # Sugestia: Zwiększ nieco okno wygładzania, jeśli nadal widać krawędzie
    # Przy 16 punktach na kafelek, okno 5 to zaledwie 1/3 kafelka.
    normals = smooth_normals(normals, window_size=8) 

    # Generate walls
    inner_wall = [p + n * INNER_WALL_OFFSET for p, n in zip(spine_points, normals)]
    outer_wall = [p - n * OUTER_WALL_OFFSET for p, n in zip(spine_points, normals)]
    
    return spine_points, normals, inner_wall, outer_wall