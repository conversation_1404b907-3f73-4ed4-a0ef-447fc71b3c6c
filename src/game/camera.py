import pygame
import random
from typing import Optional, Union

from src.game.car import Car

class Camera:
    def __init__(self, screen_size, map_size):
        self.screen_size = screen_size
        self.map_size = map_size
        self.position = pygame.Vector2(0, 0)
        self.offset = pygame.Vector2(0, 0)
        self.target: Optional[Car] = None

        # Camera Shake
        self.shake_magnitude = 0
        self.shake_duration = 0

        # FOV/Zoom
        self.base_zoom = 1.0
        self.current_zoom = 1.0
        self.zoom_speed = 0.005  # How quickly the zoom adapts
        self.camera_speed = 4.0

    def set_target(self, target: Car):
        self.target = target
        if self.target:
            self.offset = pygame.Vector2(self.target.x, self.target.y)

    def snap_to_target(self):
        """Instantly moves the camera to the target's position."""
        if self.target:
            self.offset = pygame.Vector2(self.target.x, self.target.y)
            self.position = self.offset - pygame.Vector2(self.screen_size) / (2 * self.current_zoom)
 
    def start_shake(self, magnitude: float, duration: float):
        self.shake_magnitude = magnitude
        self.shake_duration = duration

    def update(self, dt: float):
        if self.target:
            # Update FOV/Zoom based on speed
            # Update FOV/Zoom based on speed
            limit = getattr(self.target, 'max_speed_units', 25.0) # Default to 25.0 (MAX_SPEED_FORWARD from old car.py)
            
            if limit > 0:
                speed = self.target.velocity.magnitude()
                speed_normalized = speed / limit
                target_zoom = self.base_zoom - (speed_normalized * 0.15)  # Zoom out up to 15%
                self.current_zoom += (target_zoom - self.current_zoom) * self.zoom_speed

            # Update position to follow the target using LERP for smooth tracking
            target_pos = pygame.Vector2(self.target.x, self.target.y)
            self.offset += (target_pos - self.offset) * self.camera_speed * dt
            
            # Calculate the top-left position of the camera view
            self.position = self.offset - pygame.Vector2(self.screen_size) / (2 * self.current_zoom)


        # Update shake
        if self.shake_duration > 0:
            self.shake_duration -= dt
            if self.shake_duration <= 0:
                self.shake_magnitude = 0

        # Clamp camera to map boundaries
        self.position.x = max(0, min(self.position.x, self.map_size[0] - self.screen_size[0] / self.current_zoom))
        self.position.y = max(0, min(self.position.y, self.map_size[1] - self.screen_size[1] / self.current_zoom))

    def apply(self, surface: pygame.Surface) -> pygame.Surface:
        scaled_surface = pygame.transform.scale(surface, (int(surface.get_width() * self.current_zoom), int(surface.get_height() * self.current_zoom)))
        
        offset = pygame.Vector2(0, 0)
        if self.shake_magnitude > 0:
            offset.x = random.uniform(-self.shake_magnitude, self.shake_magnitude)
            offset.y = random.uniform(-self.shake_magnitude, self.shake_magnitude)

        view_rect = pygame.Rect(
            (self.position.x * self.current_zoom) + offset.x,
            (self.position.y * self.current_zoom) + offset.y,
            self.screen_size[0],
            self.screen_size[1]
        )
        
        return scaled_surface.subsurface(view_rect)

    def apply_pos(self, pos: Union[pygame.math.Vector2, tuple]) -> tuple:
        """
        Transforms a world position to screen coordinates.
        Args:
            pos: Vector2 or tuple (x, y) in world coordinates.
        Returns:
            tuple (x, y) in screen coordinates (integers).
        """
        if hasattr(pos, 'x'):
            px, py = pos.x, pos.y
        else:
            px, py = pos
            
        # 1. Offset by camera position (World -> Camera View)
        ox = px - self.position.x
        oy = py - self.position.y
        
        # 2. Scale by zoom level
        sx = ox * self.current_zoom
        sy = oy * self.current_zoom
        
        # 3. Return integers
        return (int(sx), int(sy))

    def get_offset(self) -> pygame.Vector2:
        offset = pygame.Vector2(self.position.x, self.position.y)
        if self.shake_magnitude > 0:
            offset.x += random.uniform(-self.shake_magnitude, self.shake_magnitude)
            offset.y += random.uniform(-self.shake_magnitude, self.shake_magnitude)
        return offset
