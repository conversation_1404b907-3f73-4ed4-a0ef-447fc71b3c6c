"""
Car Audio Controller - CA-Racing
================================
Manages car engine sounds using a three-layer multi-sampling system.

✅ v4.0: MAJOR AUDIO FIXES
- Louder engine sounds (ENGINE_GAIN increased to 1.8)
- Faster audio response (SMOOTHING_FACTOR increased to 0.4)
- Dynamic speed calibration based on car's actual max_speed
- Skid sound hysteresis to prevent restart flickering
- Eliminated pre-start noise
- Fixed delay issues
"""
import pygame as pg
import math


class CarAudioController:
    """
    Manages car engine sounds using a three-layer multi-sampling system.
    
    ✅ v4.0: MAJOR FIXES FOR REPORTED ISSUES
    - Engine sound too quiet -> ENGINE_GAIN = 1.8
    - Engine sound delayed -> SMOOTHING_FACTOR = 0.4
    - Noise during turning -> Skid hysteresis added
    - Noise before start -> Channels start at volume=0
    - Dynamic speed_ratio based on car's actual max_speed_units
    """
    
    # Class-level set to track allocated channel IDs across all instances
    _allocated_channels = set()
    
    # --- Audio Mastering Constants ---
    ENGINE_GAIN = 1.8       # ✅ INCREASED from 1.0 - louder engine
    CRASH_GAIN = 0.6        # Crash sound volume multiplier
    SKID_GAIN = 0.4         # Skid sound volume multiplier
    
    def __init__(self, car, audio_manager):
        self.car = car
        self.audio_manager = audio_manager
        self.assets = audio_manager.assets['sfx']

        # --- Audio Mastering Settings ---
        self.HEADROOM_GAIN = 0.85    # ✅ INCREASED from 0.7 - more headroom for louder sound
        self.SMOOTHING_FACTOR = 0.4  # ✅ INCREASED from 0.1 - faster response (less delay)
        self.TEST_SINGLE_LAYER = None  # 'LOW', 'MID', 'HIGH', or None
        
        # Noise gate - hard zero below this volume
        self.NOISE_GATE_THRESHOLD = 0.03  # ✅ REDUCED from 0.05 - less aggressive gate
        
        # --- Collision Sound Cooldown ---
        self.crash_cooldown = 0.0
        self.CRASH_COOLDOWN_TIME = 0.3  # ✅ REDUCED from 0.5 - more responsive crashes
        
        # --- Restart Throttling ---
        self._last_restart_check = 0.0
        self.RESTART_CHECK_INTERVAL = 0.15  # Check every 150ms
        
        # --- Skid Sound Hysteresis ---
        # Prevents rapid on/off switching that causes noise
        self.is_skidding = False
        self.SKID_START_THRESHOLD = 70.0   # Start skid sound above this turn rate
        self.SKID_STOP_THRESHOLD = 40.0    # Stop skid sound below this turn rate
        self.SKID_MIN_SPEED_KMH = 40.0     # Minimum speed for skid sounds
        self.skid_fadeout_time = 0.0       # Timer for smooth fadeout
        
        # --- Dynamic Channel Allocation ---
        self.channels = {}
        self.channel_ids = {}
        self._allocate_channels()
        
        # Load engine sounds
        self.engine_sound_low = self.assets.get('engine_low')
        self.engine_sound_mid = self.assets.get('engine_mid')
        self.engine_sound_high = self.assets.get('engine_high')
        
        # Store last angle for turn rate calculation
        self.last_angle = car.angle
        
        # Volume tracking for smooth transitions
        self.last_vols = [0.0, 0.0, 0.0]
        
        # ✅ Get car's max speed for dynamic calibration
        # This ensures speed_ratio is correctly scaled to the car's capabilities
        self._max_speed_units = getattr(car, 'max_speed_units', 10.0)
        if self._max_speed_units <= 0:
            self._max_speed_units = 10.0  # Fallback
        
        # Initialize engine sounds (start silent)
        self._init_engine_sound()
        
        print(f"[AUDIO] CarAudioController initialized for {'PLAYER' if getattr(car, 'is_player', False) else 'AI'}")
        print(f"[AUDIO] Max speed units: {self._max_speed_units:.2f}, Channels: {list(self.channel_ids.values())}")

    def _allocate_channels(self):
        """
        Allocates dedicated channels for this car.
        Ensures uniqueness by tracking allocated IDs in a class-level set.
        """
        required_channels = ['low', 'mid', 'high', 'crash', 'skid']
        total_channels = pg.mixer.get_num_channels()
        
        for name in required_channels:
            found_id = -1
            
            # 1. Try to find a free channel not in allocated set
            for i in range(total_channels):
                if i not in CarAudioController._allocated_channels:
                    ch = pg.mixer.Channel(i)
                    if not ch.get_busy():
                        found_id = i
                        break
            
            # 2. Fallback: steal an unreserved channel
            if found_id == -1:
                print(f"[AUDIO WARNING] No free channels for {name}. Attempting fallback.")
                for i in range(total_channels - 1, -1, -1):
                    if i not in CarAudioController._allocated_channels:
                        found_id = i
                        break
            
            # 3. Critical failure
            if found_id == -1:
                print(f"[AUDIO CRITICAL] Channel exhaustion! Forcing channel 0 for {name}.")
                found_id = 0
            
            # Register the channel
            CarAudioController._allocated_channels.add(found_id)
            self.channels[name] = pg.mixer.Channel(found_id)
            self.channel_ids[name] = found_id
        
        # Quick access references
        self.engine_channel_low = self.channels['low']
        self.engine_channel_mid = self.channels['mid']
        self.engine_channel_high = self.channels['high']
        self.crash_channel = self.channels['crash']
        self.skid_channel = self.channels['skid']

    def _init_engine_sound(self):
        """
        Initializes the three-layer engine loops.
        
        ✅ CRITICAL: Start channels at volume=0 to prevent pre-start noise.
        The update() method will gradually fade them in.
        """
        if self.engine_sound_low:
            self.engine_channel_low.play(self.engine_sound_low, loops=-1)
            self.engine_channel_low.set_volume(0.0)
            
        if self.engine_sound_mid:
            self.engine_channel_mid.play(self.engine_sound_mid, loops=-1)
            self.engine_channel_mid.set_volume(0.0)
            
        if self.engine_sound_high:
            self.engine_channel_high.play(self.engine_sound_high, loops=-1)
            self.engine_channel_high.set_volume(0.0)

    def _ensure_channels_playing(self, dt):
        """
        Ensures all engine channels are playing.
        Throttled to avoid CPU overhead.
        """
        self._last_restart_check += dt
        if self._last_restart_check < self.RESTART_CHECK_INTERVAL:
            return
        self._last_restart_check = 0.0
        
        channels = [
            (self.engine_channel_low, self.engine_sound_low, 'low'),
            (self.engine_channel_mid, self.engine_sound_mid, 'mid'),
            (self.engine_channel_high, self.engine_sound_high, 'high')
        ]
        
        for channel, sound, name in channels:
            if sound and not channel.get_busy():
                channel.play(sound, loops=-1)
                # Restore last known volume
                idx = ['low', 'mid', 'high'].index(name)
                channel.set_volume(self.last_vols[idx])

    def update(self, dt, listener_car=None):
        """
        Updates engine cross-fading and other car sounds.
        
        ✅ v4.0 FIXES:
        - Dynamic speed_ratio based on car's actual max speed
        - Faster smoothing for less delay
        - Skid hysteresis to prevent noise
        """
        # Cooldown decrement
        if self.crash_cooldown > 0:
            self.crash_cooldown -= dt
        
        # Soft mute if SFX volume is zero
        if self.audio_manager.VOLUME_SFX <= 0:
            self.engine_channel_low.set_volume(0.0)
            self.engine_channel_mid.set_volume(0.0)
            self.engine_channel_high.set_volume(0.0)
            if self.skid_channel:
                self.skid_channel.set_volume(0.0)
            return

        # Get current speed
        speed_val = self.car.velocity.magnitude() if self.car.velocity else 0.0
        
        if not all([self.engine_sound_low, self.engine_sound_mid, self.engine_sound_high]):
            return

        # ✅ FIXED: Dynamic speed_ratio based on car's actual max speed
        # This ensures proper calibration regardless of car stats
        speed_ratio = min(1.0, speed_val / self._max_speed_units) if self._max_speed_units > 0 else 0.0
        
        # Calculate turn rate for skid detection
        current_angle = self.car.angle
        angle_diff = current_angle - self.last_angle
        
        # Normalize angle difference to handle wrap-around
        while angle_diff > 180:
            angle_diff -= 360
        while angle_diff < -180:
            angle_diff += 360
        
        turn_rate = abs(angle_diff) / dt if dt > 0 else 0
        
        # Update engine crossfade
        self._update_engine_crossfade(speed_ratio)
        
        # Ensure channels are playing
        self._ensure_channels_playing(dt)
        
        # Update skid sounds with hysteresis
        self._update_skid_sounds(dt, turn_rate)
        
        self.last_angle = current_angle

    def _update_engine_crossfade(self, speed_ratio):
        """
        Calculates and applies volume cross-fading with thresholding.
        
        ✅ v4.0: Louder engine with ENGINE_GAIN = 1.8
        """
        base_volume = self.audio_manager.VOLUME_SFX * self.HEADROOM_GAIN
        
        # Tri-Phase Cross-fading
        # LOW: Full at 0%, fades to 0 at 40%
        low_vol = max(0.0, 1.0 - (speed_ratio / 0.4))
        
        # MID: Starts at 20%, peak at 50%, ends at 80%
        if speed_ratio < 0.2:
            mid_vol = 0.0
        elif speed_ratio < 0.5:
            mid_vol = (speed_ratio - 0.2) / 0.3
        elif speed_ratio < 0.8:
            mid_vol = 1.0 - ((speed_ratio - 0.5) / 0.3)
        else:
            mid_vol = 0.0
        
        # HIGH: Starts at 60%, full at 100%
        if speed_ratio < 0.6:
            high_vol = 0.0
        else:
            high_vol = (speed_ratio - 0.6) / 0.4

        # Test mode for debugging
        if self.TEST_SINGLE_LAYER == 'LOW':
            mid_vol = high_vol = 0.0
        elif self.TEST_SINGLE_LAYER == 'MID':
            low_vol = high_vol = 0.0
        elif self.TEST_SINGLE_LAYER == 'HIGH':
            low_vol = mid_vol = 0.0
        
        # Apply gains
        vols = [
            low_vol * base_volume * self.ENGINE_GAIN,
            mid_vol * base_volume * self.ENGINE_GAIN,
            high_vol * base_volume * self.ENGINE_GAIN
        ]
        
        # Apply noise gate and clamp
        for i in range(3):
            if vols[i] < self.NOISE_GATE_THRESHOLD:
                vols[i] = 0.0
            vols[i] = max(0.0, min(1.0, vols[i]))

        # ✅ FASTER smoothing (SMOOTHING_FACTOR = 0.4)
        channels = [self.engine_channel_low, self.engine_channel_mid, self.engine_channel_high]
        for i in range(3):
            target_vol = vols[i]
            current_vol = self.last_vols[i]
            
            # Linear interpolation with faster factor
            new_vol = current_vol + (target_vol - current_vol) * self.SMOOTHING_FACTOR
            
            # Snap to target if very close
            if abs(new_vol - target_vol) < 0.01:
                new_vol = target_vol
            
            # Apply noise gate on final value
            if new_vol < self.NOISE_GATE_THRESHOLD:
                new_vol = 0.0
            
            # Only update if changed significantly
            if abs(new_vol - current_vol) > 0.005:
                channels[i].set_volume(new_vol)
                self.last_vols[i] = new_vol

    def _update_skid_sounds(self, dt, turn_rate):
        """
        Handles skid sounds with hysteresis to prevent noise from rapid on/off.
        
        ✅ v4.0: Added hysteresis thresholds to prevent flickering
        """
        if self.skid_channel is None:
            return
        
        speed_kmh = self.car.get_speed_kmh() if hasattr(self.car, 'get_speed_kmh') else 0
        
        # Hysteresis logic - different thresholds for starting vs stopping
        should_skid = False
        
        if self.is_skidding:
            # Currently skidding - use lower threshold to stop (prevents flickering)
            should_skid = (turn_rate > self.SKID_STOP_THRESHOLD and 
                          speed_kmh > self.SKID_MIN_SPEED_KMH * 0.7)
        else:
            # Not skidding - use higher threshold to start
            should_skid = (turn_rate > self.SKID_START_THRESHOLD and 
                          speed_kmh > self.SKID_MIN_SPEED_KMH)
        
        if should_skid:
            self.is_skidding = True
            self.skid_fadeout_time = 0.0
            
            # Start playing if not already
            if not self.skid_channel.get_busy():
                sound = self.assets.get('skid')
                if sound:
                    self.skid_channel.play(sound, loops=-1)
            
            # Calculate intensity based on turn rate and speed
            turn_intensity = min(1.0, (turn_rate - self.SKID_STOP_THRESHOLD) / 100.0)
            speed_intensity = min(1.0, speed_kmh / 150.0)
            intensity = turn_intensity * speed_intensity
            
            vol = intensity * self.audio_manager.VOLUME_SFX * self.SKID_GAIN
            vol = max(0.0, min(0.5, vol))  # Cap at 50%
            self.skid_channel.set_volume(vol)
            
        else:
            if self.is_skidding:
                # Start fadeout
                self.skid_fadeout_time += dt
                
                if self.skid_fadeout_time < 0.2:  # 200ms fadeout
                    # Gradual fadeout
                    fade_ratio = 1.0 - (self.skid_fadeout_time / 0.2)
                    current_vol = self.skid_channel.get_volume()
                    self.skid_channel.set_volume(current_vol * fade_ratio)
                else:
                    # Fadeout complete
                    self.is_skidding = False
                    self.skid_channel.fadeout(50)

    def play_crash_sound(self, severity=1.0):
        """
        Plays a crash sound with cooldown to prevent phasing.
        """
        if self.crash_cooldown > 0:
            return
        
        sound = self.assets.get('crash')
        if sound:
            if self.crash_channel.get_busy():
                return
            
            vol = min(0.8, severity * self.audio_manager.VOLUME_SFX * self.CRASH_GAIN)
            self.crash_channel.set_volume(vol)
            self.crash_channel.play(sound)
            self.crash_cooldown = self.CRASH_COOLDOWN_TIME

    def stop_all(self):
        """Stops all engine channels."""
        self.engine_channel_low.stop()
        self.engine_channel_mid.stop()
        self.engine_channel_high.stop()
        if self.skid_channel:
            self.skid_channel.stop()
        self.is_skidding = False

    def get_audio_debug_info(self):
        """Returns diagnostic information about the audio state."""
        total_channels = pg.mixer.get_num_channels()
        
        allocated_info = {}
        for name, ch in self.channels.items():
            allocated_info[name] = {
                'busy': ch.get_busy(),
                'vol': ch.get_volume()
            }
        
        return {
            'total_channels': total_channels,
            'allocated_channels': allocated_info,
            'crash_cooldown': self.crash_cooldown,
            'is_skidding': self.is_skidding,
            'max_speed_units': self._max_speed_units,
            'volumes': {
                'low': self.last_vols[0],
                'mid': self.last_vols[1],
                'high': self.last_vols[2]
            }
        }
    
    def print_audio_debug(self):
        """Print formatted debug information to console."""
        info = self.get_audio_debug_info()
        car_name = "PLAYER" if getattr(self.car, 'is_player', False) else "AI"
        
        print(f"\n=== AUDIO DEBUG [{car_name}] ===")
        print(f"Max Speed Units: {info['max_speed_units']:.2f}")
        print(f"Engine Volumes: L={info['volumes']['low']:.2f} M={info['volumes']['mid']:.2f} H={info['volumes']['high']:.2f}")
        print(f"Skidding: {info['is_skidding']}, Crash Cooldown: {info['crash_cooldown']:.2f}s")
        
        alloc = info['allocated_channels']
        print(f"Channels: LOW={alloc['low']['busy']} MID={alloc['mid']['busy']} HIGH={alloc['high']['busy']}")
        print("=" * 50)

    def cleanup(self):
        """Stops all sounds and releases channels."""
        self.stop_all()
        
        # Release channels from global set
        if hasattr(self, 'channel_ids'):
            for ch_id in self.channel_ids.values():
                CarAudioController._allocated_channels.discard(ch_id)
        
        self.channels = {}
        self.channel_ids = {}
        print(f"[AUDIO] CarAudioController cleaned up")