# Changelog

All notable changes to this project will be documented in this file.

The format is based on [Keep a Changelog](https://keepachangelog.com/en/1.0.0/),
and this project adheres to [Semantic Versioning](https://semver.org/spec/v2.0.0.html).

## [1.0.0-demo] - 2026-01-13

### Added

-   **Multiplayer System** with Client-Side Prediction and Server Reconciliation
    -   Asynchronous game server with room-lobby system (5 rooms, 2 players each)
    -   NetworkClient with client-side prediction for zero perceived input lag
    -   Server reconciliation for authoritative physics with automatic error correction
    -   State interpolation for smooth rendering of remote players
    -   60Hz server tick rate for responsive gameplay
-   Comprehensive multiplayer documentation suite:
    -   MULTIPLAYER_QUICKSTART.md - 5-minute setup guide
    -   MULTIPLAYER_GUIDE.md - Complete technical reference
    -   README_MULTIPLAYER.md - System overview
    -   MULTIPLAYER_DEPLOYMENT_GUIDE.md - Production deployment guide
    -   NETWORKING_ARCHITECTURE.md - Detailed architecture documentation
    -   NETWORK_QUICKSTART.md - Quick network setup
    -   NETWORK_README.md - Network protocol documentation
-   Docker support for multiplayer server deployment:
    -   Dockerfile for containerized server deployment
    -   docker-compose.yml for easy orchestration
    -   .dockerignore for optimized container builds
-   Test client (test_client.py) for connection verification
-   Remote server script (remote_server.py) for production deployments
-   Server startup script (start_server.sh) for streamlined deployment
-   Network protocol module (src/network/protocol.py) with version tracking
-   Physics synchronization module (src/game/physics_sync.py) with state buffering
-   Client handler module (src/network/client_handler.py) with prediction logic

### Changed

-   Network module upgraded to version 2.0.0
-   Enhanced physics module for network synchronization
-   Improved project structure with network-aware components
-   Updated documentation with multiplayer integration guides

### Fixed

-   Network state synchronization for multiplayer gameplay
-   Client-server physics reconciliation
-   Input buffering and replay for accurate predictions

### Technical Specifications

-   **Protocol**: JSON over TCP with binary optimization planned
-   **Architecture**: Fixed room system with server authority
-   **Tick Rate**: 60 Hz authoritative simulation
-   **Prediction**: 60-frame input buffer (1 second history)
-   **Interpolation**: 100ms delay with 3-snapshot buffer
-   **Security**: Development/testing ready (TLS/SSL planned for production)

## [0.1.1-alpha] - 2026-01-11

### Added

-   Professional audio mastering system with tri-phase cross-fading
-   Extreme cubic-gate curve for wind noise physics
-   Global audio channel registry for multi-car racing
-   Air burst effects on collision
-   Audio ducking system for better sound mixing
-   Comprehensive audio system documentation (AUDIO_FIX_SUMMARY_PL.md, AUDIO_NOISE_FIX_REPORT.md, AUDIO_MASTERING_REPORT.md)
-   Physics module with engine physics and pixel collision detection
-   Enhanced AI driver documentation and knowledge base
-   Variable naming standards documentation (VARIABLE_STANDARDS.md)
-   Extended test suite for audio system validation (test_audio_fix.py)

### Changed

-   Audio buffer size increased from 512 to 1024 samples for improved stability
-   Each car now allocates 6 dedicated audio channels (Low, Mid, High, Crash, Skid, Wind)
-   Skid sound logic improved with proper fadeout handling
-   Wind noise base gain reduced to 2% ("stealth mix")
-   Channel restart checking throttled from 60Hz to 10Hz
-   Enhanced cornering audio with muffle logic instead of volume increase

### Fixed

-   **[CRITICAL]** Resolved audio channel conflicts between player and AI cars causing white noise
-   **[HIGH]** Eliminated excessive channel restarts causing audio clicks and pops
-   **[MEDIUM]** Fixed audio buffer underrun issues with increased buffer size
-   Proper cleanup of audio resources after races
-   Noise gate threshold properly enforces hard zero to prevent quantization noise accumulation
-   Skid sounds no longer persist incorrectly after stopping

### Security

-   No security-related changes in this release

## [0.1.0-rc.1] - 2026-01-06

### Added

-   Initial release candidate for community feedback.
-   Setup CI/CD pipelines for automated builds and releases.

### Changed

-   Versioning scheme updated to support prereleases.

### Fixed

-   Minor bug fixes and stability improvements.
