"""
Resource Path Helper for PyInstaller Compatibility

This module provides utilities for resolving file paths correctly in both
development and PyInstaller-bundled environments.

Author: Piotrek2713
License: MIT
"""

import sys
import os
from pathlib import Path
from typing import Union


def resource_path(relative_path: Union[str, Path]) -> str:
    """
    Get absolute path to resource - works for dev and PyInstaller builds.
    
    When running from source, returns path relative to project root.
    When running as PyInstaller executable, returns path from temp extraction dir.
    
    Args:
        relative_path: Path relative to the project root (e.g., 'assets/car.png')
        
    Returns:
        str: Absolute path to the resource
        
    Example:
        >>> img_path = resource_path("assets/sprites/car.png")
        >>> image = pygame.image.load(img_path)
    """
    try:
        # PyInstaller creates a temp folder and stores path in _MEIPASS
        base_path = sys._MEIPASS
    except AttributeError:
        # Development mode - use the directory containing the main script
        base_path = os.path.abspath(".")
    
    final_path = os.path.join(base_path, str(relative_path))
    return os.path.normpath(final_path)


def get_data_dir() -> str:
    """
    Get the data directory path.
    
    Returns:
        str: Absolute path to the data directory
    """
    return resource_path("data")


def get_assets_dir() -> str:
    """
    Get the assets directory path.
    
    Returns:
        str: Absolute path to the assets directory
    """
    return resource_path("assets")


def get_maps_dir() -> str:
    """
    Get the maps directory path.
    
    Returns:
        str: Absolute path to the maps directory
    """
    return resource_path("maps")


def verify_resource_exists(relative_path: Union[str, Path]) -> bool:
    """
    Check if a resource file exists.
    
    Args:
        relative_path: Path relative to project root
        
    Returns:
        bool: True if the resource exists, False otherwise
    """
    full_path = resource_path(relative_path)
    return os.path.exists(full_path)


def list_resources(directory: str, extension: str = None) -> list:
    """
    List all resources in a directory.
    
    Args:
        directory: Relative path to directory
        extension: Optional file extension filter (e.g., '.png')
        
    Returns:
        list: List of file paths found
    """
    dir_path = resource_path(directory)
    
    if not os.path.exists(dir_path):
        return []
    
    files = []
    for item in os.listdir(dir_path):
        item_path = os.path.join(dir_path, item)
        if os.path.isfile(item_path):
            if extension is None or item.endswith(extension):
                files.append(item_path)
    
    return files


# Debug information - useful during development
def print_environment_info():
    """Print information about the current runtime environment."""
    print("=== CA-Racing Environment Info ===")
    print(f"Running as frozen executable: {getattr(sys, 'frozen', False)}")
    print(f"Base path: {resource_path('.')}")
    print(f"Data dir: {get_data_dir()}")
    print(f"Assets dir: {get_assets_dir()}")
    print(f"Maps dir: {get_maps_dir()}")
    print(f"Python executable: {sys.executable}")
    print("=" * 35)


if __name__ == "__main__":
    # Test the resource path resolution
    print_environment_info()
    
    # Test common resources
    test_paths = [
        "assets/sprites/car.png",
        "data/config.json",
        "maps/track1.tmx"
    ]
    
    print("\nTesting resource paths:")
    for path in test_paths:
        resolved = resource_path(path)
        exists = verify_resource_exists(path)
        print(f"  {path}")
        print(f"    -> {resolved}")
        print(f"    -> Exists: {exists}")