"""
Car Physics Module
==================
Implements car physics including acceleration, braking, steering, and momentum.
"""
import pygame as pg
import math
from src.constants import ACCENT_GREEN, ACCENT_RED


class Car:
    """
    Represents a car with physics-based movement.
    Uses a simple 2D car physics model with:
    - Velocity-based movement
    - Angular velocity for steering
    - Friction and drag
    - Collision response
    """

    def __init__(self, x, y, angle, stats, sprite=None, is_player=True):
        # Position and rotation
        self.x = float(x)
        self.y = float(y)
        self.angle = float(angle)  # Degrees, 0 = right, 90 = down

        # Velocity
        self.velocity = 0.0  # Forward/backward speed
        self.angular_velocity = 0.0

        # Stats from car + parts
        self.max_speed = stats.get('max_speed', 200)
        self.acceleration = stats.get('acceleration', 150)
        self.braking = stats.get('braking', 100)
        self.boost_power = stats.get('boost_power', 0)

        # Physics constants
        self.friction = 0.98  # Velocity multiplier per frame
        self.drag = 0.001  # Speed-squared drag coefficient
        self.turn_speed = 3.0  # Degrees per frame at max turn
        self.min_turn_speed_factor = 0.3  # Minimum speed for full turning

        # Boost state
        self.boost_active = False
        self.boost_fuel = 100.0
        self.boost_regen_rate = 0.2
        self.boost_use_rate = 1.5

        # Visual
        self.sprite = sprite
        self.is_player = is_player

        # Collision
        self.width = 40
        self.height = 20
        self.collision_rect = pg.Rect(0, 0, self.width, self.height)

        # Race state
        self.current_waypoint = 0
        self.lap = 0
        self.finished = False

    def get_speed_kmh(self):
        """Returns current speed in km/h for display."""
        return abs(self.velocity) * 2  # Scale factor for display

    def update(self, dt, keys=None, tmx_map=None):
        """
        Update car physics for one frame.

        Args:
            dt: Delta time in seconds
            keys: Dict of pressed keys (for player input)
            tmx_map: TMXMap object for collision detection (optional)
        """
        if self.finished:
            return

        if self.is_player and keys:
            self._handle_input(keys, dt)

        # Update position based on velocity
        self._update_physics(dt)

        # Check collision with track boundaries using TMX
        if tmx_map:
            self._check_tmx_collision(tmx_map)

    def _handle_input(self, keys, dt):
        """Process player input for acceleration, braking, and steering."""
        # Acceleration / Braking
        if keys.get(pg.K_UP) or keys.get(pg.K_w):
            accel = self.acceleration * dt * 0.1
            if self.boost_active and self.boost_fuel > 0:
                accel *= 1.5
            self.velocity += accel
        elif keys.get(pg.K_DOWN) or keys.get(pg.K_s):
            # Braking is faster than natural deceleration
            brake_force = self.braking * dt * 0.15
            if self.velocity > 0:
                self.velocity = max(0, self.velocity - brake_force)
            else:
                # Reverse (slower)
                self.velocity -= self.acceleration * dt * 0.03

        # Steering (only effective when moving)
        speed_factor = min(1.0, abs(self.velocity) / 50.0)
        if speed_factor < self.min_turn_speed_factor:
            speed_factor = self.min_turn_speed_factor if abs(self.velocity) > 5 else 0

        if keys.get(pg.K_LEFT) or keys.get(pg.K_a):
            self.angle -= self.turn_speed * speed_factor
        elif keys.get(pg.K_RIGHT) or keys.get(pg.K_d):
            self.angle += self.turn_speed * speed_factor

        # Boost
        if keys.get(pg.K_SPACE) and self.boost_power > 0:
            self.boost_active = True
            self.boost_fuel -= self.boost_use_rate
            if self.boost_fuel <= 0:
                self.boost_fuel = 0
                self.boost_active = False
        else:
            self.boost_active = False
            # Regenerate boost
            if self.boost_fuel < 100:
                self.boost_fuel = min(100, self.boost_fuel + self.boost_regen_rate)

    def _update_physics(self, dt):
        """Update position and apply physics."""
        # Clamp velocity to max speed
        effective_max = self.max_speed
        if self.boost_active:
            effective_max += self.boost_power

        if self.velocity > effective_max:
            self.velocity = effective_max
        elif self.velocity < -effective_max * 0.3:  # Slower reverse
            self.velocity = -effective_max * 0.3

        # Apply friction and drag
        self.velocity *= self.friction
        drag_force = self.velocity * abs(self.velocity) * self.drag
        self.velocity -= drag_force

        # Calculate movement vector
        angle_rad = math.radians(self.angle)
        dx = math.cos(angle_rad) * self.velocity * dt * 60
        dy = math.sin(angle_rad) * self.velocity * dt * 60

        self.x += dx
        self.y += dy

        # Update collision rect
        self.collision_rect.center = (int(self.x), int(self.y))

    def _check_tmx_collision(self, tmx_map):
        """Check and respond to collision with TMX collision objects."""
        # Sample points around the car
        angle_rad = math.radians(self.angle)
        cos_a = math.cos(angle_rad)
        sin_a = math.sin(angle_rad)

        # Check corners and edges of the car
        half_w = self.width // 2
        half_h = self.height // 2
        check_points = [
            (half_w, 0),        # Front
            (-half_w, 0),       # Back
            (half_w, half_h),   # Front right
            (half_w, -half_h),  # Front left
            (-half_w, half_h),  # Back right
            (-half_w, -half_h), # Back left
            (0, half_h),        # Right side
            (0, -half_h),       # Left side
        ]

        collision = False
        push_x, push_y = 0, 0
        collision_count = 0

        for lx, ly in check_points:
            # Rotate point to world coordinates
            wx = self.x + lx * cos_a - ly * sin_a
            wy = self.y + lx * sin_a + ly * cos_a

            # Check if point is inside any collision polygon
            if tmx_map.point_in_collision(wx, wy):
                collision = True
                collision_count += 1
                # Push away from collision point
                push_x -= (wx - self.x) * 0.3
                push_y -= (wy - self.y) * 0.3

        if collision:
            # Reduce velocity based on severity
            speed_reduction = 0.5 + (0.3 * (1 - collision_count / len(check_points)))
            self.velocity *= speed_reduction

            # Push car away from wall
            if collision_count > 0:
                self.x += push_x / collision_count
                self.y += push_y / collision_count

    def check_car_collision(self, other_car):
        """Check and respond to collision with another car."""
        dx = other_car.x - self.x
        dy = other_car.y - self.y
        dist = math.sqrt(dx * dx + dy * dy)

        min_dist = (self.width + other_car.width) / 2

        if dist < min_dist and dist > 0:
            # Collision! Push cars apart
            overlap = min_dist - dist
            push_x = (dx / dist) * overlap * 0.5
            push_y = (dy / dist) * overlap * 0.5

            self.x -= push_x
            self.y -= push_y
            other_car.x += push_x
            other_car.y += push_y

            # Exchange some velocity (simplified collision response)
            self.velocity *= 0.7
            other_car.velocity *= 0.7

            return True
        return False

    def check_waypoint(self, waypoints, lap_total):
        """Check if car has passed through the next waypoint."""
        if self.finished:
            return False

        if not waypoints or self.current_waypoint >= len(waypoints):
            return False

        wp = waypoints[self.current_waypoint]
        dist = math.sqrt((self.x - wp[0])**2 + (self.y - wp[1])**2)

        if dist < 80:  # Waypoint radius
            self.current_waypoint += 1
            if self.current_waypoint >= len(waypoints):
                self.current_waypoint = 0
                self.lap += 1
                if self.lap >= lap_total:
                    self.finished = True
                return True
        return False

    def draw(self, screen, camera_offset=(0, 0)):
        """Draw the car on screen."""
        if not self.sprite:
            # Fallback: draw a simple rectangle
            surf = pg.Surface((self.width, self.height), pg.SRCALPHA)
            color = ACCENT_GREEN if self.is_player else ACCENT_RED
            pg.draw.rect(surf, color, (0, 0, self.width, self.height))
        else:
            # Scale and rotate sprite
            scaled = pg.transform.scale(self.sprite, (self.width, self.height))
            surf = scaled

        # Rotate
        rotated = pg.transform.rotate(surf, -self.angle)
        rect = rotated.get_rect(center=(int(self.x - camera_offset[0]), int(self.y - camera_offset[1])))
        screen.blit(rotated, rect)

        # Draw boost effect
        if self.boost_active and self.is_player:
            angle_rad = math.radians(self.angle)
            bx = self.x - math.cos(angle_rad) * 25 - camera_offset[0]
            by = self.y - math.sin(angle_rad) * 25 - camera_offset[1]
            pg.draw.circle(screen, (255, 150, 0), (int(bx), int(by)), 8)
            pg.draw.circle(screen, (255, 255, 100), (int(bx), int(by)), 4)

