"""
CA-Racing Network Protocol
===========================
Efficient binary protocol with optional compression for minimal bandwidth usage.

Key Features:
- Binary serialization for compact packets
- Optional zlib compression for large payloads
- Type-safe packet definitions with dataclasses
- Minimal overhead for real-time gaming
"""

import struct
import zlib
import json
from enum import IntEnum
from dataclasses import dataclass, asdict
from typing import Optional, Dict, Any, List
import time


class PacketType(IntEnum):
    """Packet type identifiers (1 byte each)"""
    # Connection Management (0-9)
    WELCOME = 0
    ROOM_ASSIGNED = 1
    PLAYER_JOINED = 2
    PLAYER_LEFT = 3
    HEARTBEAT = 4
    PLAYER_INFO = 5
    
    # Game Control (10-19)
    PLAYER_READY = 10
    RACE_START = 11
    RACE_FINISH = 12
    RACE_LOADING = 13
    
    # Lobby/Room Management (13-22)
    ROOM_LIST = 14
    CREATE_ROOM = 15
    JOIN_ROOM = 16
    LEAVE_ROOM = 17
    CHAT_MESSAGE = 18
    SPECTATE_ROOM = 19
    ROOM_UPDATED = 20
    ROOM_DATA_UPDATE = 21
    
    # Real-time State (20-29)
    PLAYER_INPUT = 22
    GAME_STATE = 23
    
    # Network Diagnostics (30-39)
    PING = 30
    PONG = 31
    
    # Error Handling (40-49)
    ERROR = 40


# === PACKET STRUCTURES ===

@dataclass
class Transform:
    """3D transform state (position + rotation)"""
    x: float
    y: float
    angle: float
    
    def pack(self) -> bytes:
        """Pack to binary: 3 floats (12 bytes)"""
        return struct.pack('fff', self.x, self.y, self.angle)
    
    @staticmethod
    def unpack(data: bytes) -> 'Transform':
        """Unpack from binary"""
        x, y, angle = struct.unpack('fff', data[:12])
        return Transform(x, y, angle)


@dataclass
class Velocity:
    """Velocity vector"""
    vx: float
    vy: float
    
    def pack(self) -> bytes:
        """Pack to binary: 2 floats (8 bytes)"""
        return struct.pack('ff', self.vx, self.vy)
    
    @staticmethod
    def unpack(data: bytes) -> 'Velocity':
        """Unpack from binary"""
        vx, vy = struct.unpack('ff', data[:8])
        return Velocity(vx, vy)


@dataclass
class InputState:
    """
    Player input snapshot (optimized for bandwidth).
    
    Packed as: 3 floats + 2 ints = 20 bytes
    """
    throttle: float       # 0.0 to 1.0
    steering: float       # -1.0 to 1.0
    is_braking: float     # 0.0 to 1.0
    timestamp: float      # Client timestamp (for latency calculation)
    sequence_number: int  # Input sequence for reconciliation
    
    def pack(self) -> bytes:
        """Pack to binary: 3 floats + 1 double + 1 int (24 bytes)"""
        return struct.pack('fffdi', 
                          self.throttle, 
                          self.steering, 
                          self.is_braking,
                          self.timestamp,
                          self.sequence_number)
    
    @staticmethod
    def unpack(data: bytes) -> 'InputState':
        """Unpack from binary"""
        throttle, steering, is_braking, timestamp, seq = struct.unpack('fffdi', data[:24])
        return InputState(throttle, steering, is_braking, timestamp, seq)


@dataclass
class PlayerState:
    """
    Authoritative player state from server.
    
    Packed format:
    - Transform (12 bytes)
    - Velocity (8 bytes)
    - timestamp (8 bytes)
    - sequence (4 bytes)
    - lap, waypoint (2 bytes each)
    - finished (1 byte)
    Total: ~37 bytes
    """
    player_id: str
    x: float
    y: float
    angle: float
    velocity_x: float
    velocity_y: float
    timestamp: float
    sequence_number: int
    lap: int = 0
    current_waypoint: int = 0
    finished: bool = False
    
    def pack(self) -> bytes:
        """Pack to binary (without player_id, handled separately)"""
        return struct.pack('fffddihh?',
                          self.x,
                          self.y,
                          self.angle,
                          self.velocity_x,
                          self.velocity_y,
                          self.timestamp,
                          self.sequence_number,
                          self.lap,
                          self.current_waypoint,
                          self.finished)
    
    @staticmethod
    def unpack(player_id: str, data: bytes) -> 'PlayerState':
        """Unpack from binary"""
        (x, y, angle, vx, vy, timestamp, seq, lap, waypoint, finished) = \
            struct.unpack('fffddihh?', data[:43])
        
        return PlayerState(
            player_id=player_id,
            x=x, y=y, angle=angle,
            velocity_x=vx, velocity_y=vy,
            timestamp=timestamp,
            sequence_number=seq,
            lap=lap,
            current_waypoint=waypoint,
            finished=finished
        )


# === PACKET BUILDER ===

class PacketBuilder:
    """
    Builds and parses network packets with optional compression.
    
    Packet Format:
    [1 byte: PacketType] [1 byte: compressed flag] [4 bytes: payload length] [N bytes: payload]
    
    For efficiency:
    - Small packets (<256 bytes): No compression
    - Large packets: zlib compression (level 6)
    """
    
    COMPRESSION_THRESHOLD = 256  # Compress payloads larger than this
    
    @staticmethod
    def build(packet_type: PacketType, payload: Dict[str, Any], compress: bool = False) -> bytes:
        """
        Build a packet from type and payload.
        
        Args:
            packet_type: Packet type identifier
            payload: Dictionary payload (will be JSON serialized)
            compress: Force compression (auto if payload > threshold)
        
        Returns:
            Complete packet bytes ready to send
        """
        # Serialize payload to JSON
        payload_json = json.dumps(payload).encode('utf-8')
        
        # Auto-compress if over threshold
        if len(payload_json) > PacketBuilder.COMPRESSION_THRESHOLD:
            compress = True
        
        # Apply compression if needed
        if compress:
            payload_data = zlib.compress(payload_json, level=6)
            compressed_flag = 1
        else:
            payload_data = payload_json
            compressed_flag = 0
        
        # Build header: [type][compressed][length]
        # Use ! for network byte order (no padding)
        header = struct.pack('!BBI', packet_type, compressed_flag, len(payload_data))
        
        return header + payload_data
    
    @staticmethod
    def parse(data: bytes) -> tuple[PacketType, Dict[str, Any]]:
        """
        Parse a packet into type and payload.
        
        Args:
            data: Raw packet bytes
        
        Returns:
            (packet_type, payload_dict)
        
        Raises:
            ValueError: If packet is malformed
        """
        if len(data) < 6:
            raise ValueError("Packet too short (minimum 6 bytes)")
        
        # Parse header
        packet_type_int, compressed_flag, payload_length = struct.unpack('!BBI', data[:6])
        packet_type = PacketType(packet_type_int)
        
        # Extract payload
        payload_data = data[6:6+payload_length]
        
        if len(payload_data) != payload_length:
            raise ValueError(f"Payload length mismatch: expected {payload_length}, got {len(payload_data)}")
        
        # Decompress if needed
        if compressed_flag:
            payload_json = zlib.decompress(payload_data)
        else:
            payload_json = payload_data
        
        # Parse JSON
        payload = json.loads(payload_json.decode('utf-8'))
        
        return packet_type, payload


# === SPECIALIZED PACKET BUILDERS ===

class Packets:
    """
    Factory methods for common packet types.
    
    Provides type-safe packet construction for all protocol messages.
    """
    
    @staticmethod
    def welcome(player_id: str, server_time: float) -> bytes:
        """Welcome packet sent on connection"""
        return PacketBuilder.build(PacketType.WELCOME, {
            'player_id': player_id,
            'timestamp': server_time
        })
    
    @staticmethod
    def room_assigned(room_id: int, server_time: float) -> bytes:
        """Room assignment notification"""
        return PacketBuilder.build(PacketType.ROOM_ASSIGNED, {
            'room_id': room_id,
            'timestamp': server_time
        })
    
    @staticmethod
    def player_joined(player_id: str, server_time: float) -> bytes:
        """Player joined room notification"""
        return PacketBuilder.build(PacketType.PLAYER_JOINED, {
            'player_id': player_id,
            'timestamp': server_time
        })
    
    @staticmethod
    def player_left(player_id: str, server_time: float) -> bytes:
        """Player left room notification"""
        return PacketBuilder.build(PacketType.PLAYER_LEFT, {
            'player_id': player_id,
            'timestamp': server_time
        })
    
    @staticmethod
    def heartbeat(player_id: str, client_time: float) -> bytes:
        """Heartbeat to keep connection alive"""
        return PacketBuilder.build(PacketType.HEARTBEAT, {
            'player_id': player_id,
            'timestamp': client_time
        })
    
    @staticmethod
    def race_start(start_positions: Dict[str, Dict[str, float]], server_time: float) -> bytes:
        """Race start with all player positions"""
        return PacketBuilder.build(PacketType.RACE_START, {
            'timestamp': server_time,
            'players': start_positions
        }, compress=True)
    
    @staticmethod
    def race_loading(server_time: float) -> bytes:
        """Race loading notification"""
        return PacketBuilder.build(PacketType.RACE_LOADING, {
            'timestamp': server_time
        })
    
    @staticmethod
    def game_state(sequence: int, players: Dict[str, PlayerState], server_time: float) -> bytes:
        """
        Game state update (authoritative).
        
        This is the most frequent packet type, sent at tick rate (30-60Hz).
        Uses compression for multiple players.
        """
        players_data = {
            pid: asdict(state) for pid, state in players.items()
        }
        
        return PacketBuilder.build(PacketType.GAME_STATE, {
            'sequence_number': sequence,
            'timestamp': server_time,
            'players': players_data
        }, compress=len(players) > 1)
    
    @staticmethod
    def player_input(input_state: InputState) -> bytes:
        """Player input update (client -> server)"""
        return PacketBuilder.build(PacketType.PLAYER_INPUT, {
            'throttle': input_state.throttle,
            'steering': input_state.steering,
            'is_braking': input_state.is_braking,
            'timestamp': input_state.timestamp,
            'sequence_number': input_state.sequence_number
        })
    
    @staticmethod
    def ping(client_time: float) -> bytes:
        """Ping for latency measurement"""
        return PacketBuilder.build(PacketType.PING, {
            'timestamp': client_time
        })
    
    @staticmethod
    def pong(client_timestamp: float, server_time: float) -> bytes:
        """Pong response with both timestamps"""
        return PacketBuilder.build(PacketType.PONG, {
            'client_timestamp': client_timestamp,
            'server_timestamp': server_time
        })
    
    @staticmethod
    def error(message: str, server_time: float) -> bytes:
        """Error message"""
        return PacketBuilder.build(PacketType.ERROR, {
            'message': message,
            'timestamp': server_time
        })
    
    @staticmethod
    def room_list(rooms: List[Dict], total_rooms: int, page: int, server_time: float) -> bytes:
        """Room list update"""
        return PacketBuilder.build(PacketType.ROOM_LIST, {
            'rooms': rooms,
            'total_rooms': total_rooms,
            'page': page,
            'timestamp': server_time
        }, compress=len(rooms) > 5)
    
    @staticmethod
    def create_room(room_name: str, host_name: str, track_name: str, client_time: float) -> bytes:
        """Create room request"""
        return PacketBuilder.build(PacketType.CREATE_ROOM, {
            'room_name': room_name,
            'host_name': host_name,
            'track_name': track_name,
            'timestamp': client_time
        })
    
    @staticmethod
    def join_room(room_id: int, client_time: float) -> bytes:
        """Join room request"""
        return PacketBuilder.build(PacketType.JOIN_ROOM, {
            'room_id': room_id,
            'timestamp': client_time
        })
    
    @staticmethod
    def leave_room(client_time: float) -> bytes:
        """Leave room request"""
        return PacketBuilder.build(PacketType.LEAVE_ROOM, {
            'timestamp': client_time
        })
    
    @staticmethod
    def spectate_room(room_id: int, client_time: float) -> bytes:
        """Spectate room request"""
        return PacketBuilder.build(PacketType.SPECTATE_ROOM, {
            'room_id': room_id,
            'timestamp': client_time
        })
    
    @staticmethod
    def room_updated(room_id: int, players_count: int, state: str, server_time: float) -> bytes:
        """Room state update notification"""
        return PacketBuilder.build(PacketType.ROOM_UPDATED, {
            'room_id': room_id,
            'players_count': players_count,
            'state': state,
            'timestamp': server_time
        })

    @staticmethod
    def room_data_update(room_id: int, room_name: str, map_name: str, current_players: int, max_players: int, status: str, server_time: float) -> bytes:
        """Single room data update for lobby view"""
        return PacketBuilder.build(PacketType.ROOM_DATA_UPDATE, {
            'room_id': room_id,
            'room_name': room_name,
            'map_name': map_name,
            'current_players': current_players,
            'max_players': max_players,
            'status': status,
            'timestamp': server_time
        })
    
    @staticmethod
    def chat_message(sender: str, message: str, in_room: bool, server_time: float) -> bytes:
        """Chat message"""
        return PacketBuilder.build(PacketType.CHAT_MESSAGE, {
            'sender': sender,
            'message': message,
            'in_room': in_room,
            'timestamp': server_time
        })


# === LAG SIMULATOR ===

class LagSimulator:
    """
    Simulates network conditions for testing lag compensation.
    
    Features:
    - Configurable latency (delay packets)
    - Packet loss simulation
    - Jitter (variable latency)
    
    Use in development to test prediction/reconciliation under bad network conditions.
    """
    
    def __init__(self, base_latency_ms: float = 0, jitter_ms: float = 0, packet_loss: float = 0.0):
        """
        Initialize lag simulator.
        
        Args:
            base_latency_ms: Base latency in milliseconds (e.g., 100 = 100ms)
            jitter_ms: Random jitter range (e.g., 20 = ±20ms)
            packet_loss: Packet loss rate (0.0 to 1.0, e.g., 0.05 = 5% loss)
        """
        self.base_latency_ms = base_latency_ms
        self.jitter_ms = jitter_ms
        self.packet_loss = packet_loss
        self.packet_queue: List[tuple[float, bytes]] = []  # (delivery_time, packet)
    
    def send(self, packet: bytes, current_time: float) -> None:
        """
        Queue packet for delayed delivery.
        
        Args:
            packet: Packet bytes to send
            current_time: Current timestamp
        """
        import random
        
        # Simulate packet loss
        if random.random() < self.packet_loss:
            return  # Drop packet
        
        # Calculate delivery time with jitter
        jitter = random.uniform(-self.jitter_ms, self.jitter_ms)
        total_delay_ms = self.base_latency_ms + jitter
        delivery_time = current_time + (total_delay_ms / 1000.0)
        
        self.packet_queue.append((delivery_time, packet))
    
    def receive(self, current_time: float) -> List[bytes]:
        """
        Get packets ready for delivery.
        
        Args:
            current_time: Current timestamp
        
        Returns:
            List of packets ready to be delivered
        """
        ready_packets = []
        remaining = []
        
        for delivery_time, packet in self.packet_queue:
            if delivery_time <= current_time:
                ready_packets.append(packet)
            else:
                remaining.append((delivery_time, packet))
        
        self.packet_queue = remaining
        return ready_packets
    
    def clear(self) -> None:
        """Clear all queued packets"""
        self.packet_queue.clear()


# === UTILITY FUNCTIONS ===

def estimate_bandwidth(packet_type: PacketType, typical_payload_size: int, 
                       packets_per_second: int) -> float:
    """
    Estimate bandwidth usage for a packet type.
    
    Args:
        packet_type: Type of packet
        typical_payload_size: Average payload size in bytes
        packets_per_second: Frequency of this packet
    
    Returns:
        Bandwidth in KB/s
    """
    header_size = 6  # Type + compressed flag + length
    total_packet_size = header_size + typical_payload_size
    
    bytes_per_second = total_packet_size * packets_per_second
    kilobytes_per_second = bytes_per_second / 1024.0
    
    return kilobytes_per_second


def get_protocol_stats() -> Dict[str, Any]:
    """
    Get statistics about the protocol.
    
    Returns:
        Dictionary with protocol metadata
    """
    return {
        'version': '1.0',
        'header_size_bytes': 6,
        'compression_threshold': PacketBuilder.COMPRESSION_THRESHOLD,
        'packet_types': {pt.name: pt.value for pt in PacketType},
        'typical_sizes': {
            'input': 24 + 6,  # InputState + header
            'state': 43 + 6,  # PlayerState + header
            'heartbeat': 20 + 6,  # JSON + header
        }
    }


if __name__ == '__main__':
    """Test the protocol"""
    print("=== CA-Racing Protocol Test ===\n")
    
    # Test 1: Build and parse a game state packet
    print("[Test 1] Game State Packet")
    
    state1 = PlayerState(
        player_id="player_1",
        x=100.5, y=200.3, angle=45.0,
        velocity_x=5.0, velocity_y=3.0,
        timestamp=time.time(),
        sequence_number=100,
        lap=1, current_waypoint=3,
        finished=False
    )
    
    state2 = PlayerState(
        player_id="player_2",
        x=150.0, y=250.0, angle=90.0,
        velocity_x=6.0, velocity_y=4.0,
        timestamp=time.time(),
        sequence_number=100,
        lap=1, current_waypoint=2,
        finished=False
    )
    
    packet = Packets.game_state(100, {'player_1': state1, 'player_2': state2}, time.time())
    print(f"  Packet size: {len(packet)} bytes")
    
    packet_type, payload = PacketBuilder.parse(packet)
    print(f"  Parsed type: {packet_type.name}")
    print(f"  Players in state: {len(payload['players'])}")
    print("  ✅ Test passed!\n")
    
    # Test 2: Compression test
    print("[Test 2] Compression Test")
    
    # Create large payload
    large_payload = {'data': 'x' * 1000}
    uncompressed = PacketBuilder.build(PacketType.GAME_STATE, large_payload, compress=False)
    compressed = PacketBuilder.build(PacketType.GAME_STATE, large_payload, compress=True)
    
    print(f"  Uncompressed: {len(uncompressed)} bytes")
    print(f"  Compressed: {len(compressed)} bytes")
    print(f"  Ratio: {len(compressed)/len(uncompressed)*100:.1f}%")
    print("  ✅ Test passed!\n")
    
    # Test 3: Lag Simulator
    print("[Test 3] Lag Simulator")
    
    simulator = LagSimulator(base_latency_ms=100, jitter_ms=20, packet_loss=0.0)
    current_time = time.time()
    
    # Send packet
    test_packet = Packets.heartbeat("player_test", current_time)
    simulator.send(test_packet, current_time)
    
    # Try to receive immediately (should be empty)
    immediate = simulator.receive(current_time)
    print(f"  Immediate receive: {len(immediate)} packets (expected 0)")
    
    # Receive after delay
    future_time = current_time + 0.12  # 120ms later
    delayed = simulator.receive(future_time)
    print(f"  Delayed receive: {len(delayed)} packets (expected 1)")
    print("  ✅ Test passed!\n")
    
    # Test 4: Bandwidth estimation
    print("[Test 4] Bandwidth Estimation")
    
    # Game state at 60 Hz for 2 players
    bw = estimate_bandwidth(PacketType.GAME_STATE, 43 * 2, 60)
    print(f"  Game state (60 Hz, 2 players): {bw:.2f} KB/s")
    
    # Input at 60 Hz
    bw_input = estimate_bandwidth(PacketType.PLAYER_INPUT, 24, 60)
    print(f"  Player input (60 Hz): {bw_input:.2f} KB/s")
    
    print("  ✅ Test passed!\n")
    
    # Protocol stats
    print("[Protocol Stats]")
    stats = get_protocol_stats()
    for key, value in stats.items():
        print(f"  {key}: {value}")
    
    print("\n=== All Tests Passed! ===")
