"""
CA-Racing Network Client Bridge
=================================
Client-side network bridge that handles:
- Loading player data from local settings.json
- Managing connection to game server
- Sending player input at 30Hz
- Receiving and applying server state updates

Architecture:
- Async I/O using asyncio
- JSON-based protocol (compatible with existing server)
- Client-side prediction with server reconciliation
"""

import asyncio
import json
import time
import os
from typing import Optional, Dict, Any, Callable
from dataclasses import dataclass, asdict
import logging

logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@dataclass
class PlayerLocalData:
    """Local player data loaded from settings.json"""
    player_name: str
    car_id: str
    
    @classmethod
    def load_from_settings(cls, settings_path: str = "data/settings.json") -> "PlayerLocalData":
        """
        Load player data from settings.json.
        
        Args:
            settings_path: Path to settings file
            
        Returns:
            PlayerLocalData instance with loaded data
        """
        try:
            with open(settings_path, 'r') as f:
                settings = json.load(f)
            
            player_name = settings.get('player_name', 'Player1')
            car_id = settings.get('last_selected_car_id', 'car_0')
            
            logger.info(f"Loaded player data: {player_name} with car {car_id}")
            return cls(player_name=player_name, car_id=car_id)
            
        except FileNotFoundError:
            logger.warning(f"Settings file not found: {settings_path}, using defaults")
            return cls(player_name='Player1', car_id='car_0')
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse settings.json: {e}, using defaults")
            return cls(player_name='Player1', car_id='car_0')


@dataclass
class InputSnapshot:
    """Player input snapshot for server transmission"""
    throttle: float  # 0.0 to 1.0
    steering: float  # -1.0 to 1.0
    is_braking: float  # 0.0 to 1.0
    timestamp: float
    sequence_number: int


@dataclass
class PhysicsState:
    """Physics state received from server"""
    x: float
    y: float
    angle: float
    velocity_x: float
    velocity_y: float
    is_drifting: bool
    timestamp: float
    sequence_number: int


class NetworkBridge:
    """
    Client-side network bridge for multiplayer racing.
    
    Responsibilities:
    - Load local player data from settings.json
    - Manage TCP connection to server
    - Send player input at fixed intervals (30Hz)
    - Receive and buffer server state updates
    - Provide callbacks for game events
    """
    
    INPUT_SEND_RATE = 30  # Hz
    INPUT_SEND_INTERVAL = 1.0 / INPUT_SEND_RATE
    CONNECTION_TIMEOUT = 10.0  # seconds
    
    def __init__(self, settings_path: str = "data/settings.json"):
        """
        Initialize network bridge.
        
        Args:
            settings_path: Path to settings.json file
        """
        # Load local player data
        self.player_data = PlayerLocalData.load_from_settings(settings_path)
        
        # Connection state
        self.reader: Optional[asyncio.StreamReader] = None
        self.writer: Optional[asyncio.StreamWriter] = None
        self.connected = False
        self.player_id: Optional[str] = None
        self.room_id: Optional[int] = None
        
        # Input tracking
        self.input_sequence = 0
        self.last_input_time = 0.0
        self.input_accumulator = 0.0
        
        # State buffers
        self.opponent_states: Dict[str, PhysicsState] = {}
        self.rankings: list = []
        
        # Callbacks
        self.on_connected: Optional[Callable] = None
        self.on_room_assigned: Optional[Callable[[int], None]] = None
        self.on_race_start: Optional[Callable] = None
        self.on_state_update: Optional[Callable[[Dict], None]] = None
        self.on_rankings_update: Optional[Callable[[list], None]] = None
        self.on_disconnected: Optional[Callable] = None
        self.on_room_update: Optional[Callable[[Dict], None]] = None
        
        # Background tasks
        self.receive_task: Optional[asyncio.Task] = None
        self.heartbeat_task: Optional[asyncio.Task] = None
        
    async def connect(self, host: str = 'localhost', port: int = 8443) -> bool:
        """
        Connect to game server and perform handshake.
        
        Args:
            host: Server hostname or IP
            port: Server port
            
        Returns:
            True if connection successful
        """
        try:
            logger.info(f"Connecting to {host}:{port}...")
            
            self.reader, self.writer = await asyncio.wait_for(
                asyncio.open_connection(host, port),
                timeout=self.CONNECTION_TIMEOUT
            )
            
            # Wait for server welcome message
            data = await asyncio.wait_for(
                self.reader.readline(),
                timeout=5.0
            )
            
            if not data:
                logger.error("Server closed connection during handshake")
                return False
            
            welcome = json.loads(data.decode())
            if welcome.get('type') != 'connected':
                logger.error(f"Unexpected welcome message: {welcome}")
                return False
            
            self.player_id = welcome.get('player_id')
            logger.info(f"Received player ID: {self.player_id}")
            
            # Send client hello with player data
            await self.send_message({
                'type': 'client_hello',
                'player_name': self.player_data.player_name,
                'car_id': self.player_data.car_id,
                'timestamp': time.time()
            })
            
            self.connected = True
            
            # Start background tasks
            self.receive_task = asyncio.create_task(self._receive_loop())
            self.heartbeat_task = asyncio.create_task(self._heartbeat_loop())
            
            if self.on_connected:
                self.on_connected()
            
            logger.info("✅ Connected to server successfully")
            return True
            
        except asyncio.TimeoutError:
            logger.error("Connection timeout")
            return False
        except Exception as e:
            logger.error(f"Connection failed: {e}")
            return False
    
    async def disconnect(self):
        """Disconnect from server and cleanup resources"""
        if not self.connected:
            return
        
        logger.info("Disconnecting from server...")
        
        # Cancel background tasks
        if self.receive_task:
            self.receive_task.cancel()
        if self.heartbeat_task:
            self.heartbeat_task.cancel()
        
        # Close connection
        if self.writer:
            self.writer.close()
            await self.writer.wait_closed()
        
        self.connected = False
        self.reader = None
        self.writer = None
        
        if self.on_disconnected:
            self.on_disconnected()
        
        logger.info("Disconnected from server")
    
    async def send_message(self, message: Dict[str, Any]):
        """
        Send JSON message to server.
        
        Args:
            message: Dictionary to send as JSON
        """
        if not self.writer:
            logger.warning("Cannot send message: not connected")
            return
        
        try:
            data = json.dumps(message) + '\n'
            self.writer.write(data.encode())
            await self.writer.drain()
        except Exception as e:
            logger.error(f"Failed to send message: {e}")
            await self.disconnect()
    
    async def join_room(self, room_id: int):
        """
        Request to join a specific room.
        
        Args:
            room_id: ID of room to join
        """
        await self.send_message({
            'type': 'join_room',
            'room_id': room_id,
            'timestamp': time.time()
        })
    
    async def send_ready(self):
        """Signal that player is ready to start race"""
        await self.send_message({
            'type': 'ready',
            'timestamp': time.time()
        })
    
    async def send_input(self, throttle: float, steering: float, is_braking: float, dt: float):
        """
        Send player input to server at fixed rate (30Hz).
        
        Args:
            throttle: Throttle input (0.0 to 1.0)
            steering: Steering input (-1.0 to 1.0)
            is_braking: Brake input (0.0 to 1.0)
            dt: Delta time since last frame
        """
        # Accumulate time
        self.input_accumulator += dt
        
        # Only send at fixed rate
        if self.input_accumulator >= self.INPUT_SEND_INTERVAL:
            self.input_accumulator -= self.INPUT_SEND_INTERVAL
            
            self.input_sequence += 1
            
            await self.send_message({
                'type': 'input',
                'throttle': throttle,
                'steering': steering,
                'is_braking': is_braking,
                'timestamp': time.time(),
                'sequence_number': self.input_sequence
            })
    
    async def send_physics_state(
        self,
        x: float,
        y: float,
        angle: float,
        velocity_x: float,
        velocity_y: float,
        is_drifting: bool,
        lap: int,
        current_waypoint: int
    ):
        """
        Send physics state update to server (client-side prediction).
        
        This is sent alongside input for server reconciliation.
        """
        await self.send_message({
            'type': 'physics_state',
            'x': x,
            'y': y,
            'angle': angle,
            'velocity_x': velocity_x,
            'velocity_y': velocity_y,
            'is_drifting': is_drifting,
            'lap': lap,
            'current_waypoint': current_waypoint,
            'timestamp': time.time(),
            'sequence_number': self.input_sequence
        })
    
    async def _receive_loop(self):
        """Background task to receive messages from server"""
        try:
            while self.connected and self.reader:
                data = await self.reader.readline()
                
                if not data:
                    logger.warning("Server closed connection")
                    break
                
                try:
                    message = json.loads(data.decode())
                    await self._handle_message(message)
                except json.JSONDecodeError as e:
                    logger.error(f"Invalid JSON received: {e}")
                    
        except asyncio.CancelledError:
            logger.info("Receive loop cancelled")
        except Exception as e:
            logger.error(f"Receive loop error: {e}")
        finally:
            await self.disconnect()
    
    async def _handle_message(self, message: Dict[str, Any]):
        """
        Handle incoming message from server.
        
        Args:
            message: Parsed JSON message
        """
        msg_type = message.get('type')
        
        if msg_type == 'room_assigned':
            self.room_id = message.get('room_id')
            logger.info(f"Assigned to room {self.room_id}")
            if self.on_room_assigned:
                self.on_room_assigned(self.room_id)
        
        elif msg_type == 'race_start':
            logger.info("Race starting!")
            if self.on_race_start:
                self.on_race_start()

        elif msg_type == 'room_data_update':
            if self.on_room_update:
                self.on_room_update(message)
        
        elif msg_type == 'game_state':
            # Server authoritative state update
            if self.on_state_update:
                self.on_state_update(message)
        
        elif msg_type == 'physics_sync':
            # Physics state updates for all players
            states = message.get('states', {})
            for player_id, state in states.items():
                if player_id != self.player_id:
                    self.opponent_states[player_id] = PhysicsState(
                        x=state['x'],
                        y=state['y'],
                        angle=state['angle'],
                        velocity_x=state['velocity_x'],
                        velocity_y=state['velocity_y'],
                        is_drifting=state.get('is_drifting', False),
                        timestamp=message.get('timestamp', time.time()),
                        sequence_number=state.get('sequence_number', 0)
                    )
        
        elif msg_type == 'rankings':
            # Waypoint-based ranking update
            self.rankings = message.get('data', [])
            if self.on_rankings_update:
                self.on_rankings_update(self.rankings)
        
        elif msg_type == 'heartbeat_request':
            # Respond to heartbeat
            await self.send_message({
                'type': 'heartbeat',
                'timestamp': time.time()
            })
        
        elif msg_type == 'error':
            logger.error(f"Server error: {message.get('message', 'Unknown error')}")
    
    async def _heartbeat_loop(self):
        """Send periodic heartbeat to server"""
        try:
            while self.connected:
                await asyncio.sleep(5.0)
                await self.send_message({
                    'type': 'heartbeat',
                    'timestamp': time.time()
                })
        except asyncio.CancelledError:
            logger.info("Heartbeat loop cancelled")
        except Exception as e:
            logger.error(f"Heartbeat error: {e}")
    
    def get_opponent_states(self) -> Dict[str, PhysicsState]:
        """Get latest opponent physics states"""
        return self.opponent_states.copy()
    
    def get_rankings(self) -> list:
        """Get latest race rankings"""
        return self.rankings.copy()
